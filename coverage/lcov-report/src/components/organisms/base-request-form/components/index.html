
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/organisms/base-request-form/components</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> src/components/organisms/base-request-form/components</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">83.67% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>41/49</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.92% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>34/41</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>14/20</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85.1% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>40/47</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="ContactSection.tsx"><a href="ContactSection.tsx.html">ContactSection.tsx</a></td>
	<td data-value="90.9" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="11" class="abs high">10/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="11" class="abs high">10/11</td>
	</tr>

<tr>
	<td class="file high" data-value="MapSection.tsx"><a href="MapSection.tsx.html">MapSection.tsx</a></td>
	<td data-value="80" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	</tr>

<tr>
	<td class="file medium" data-value="ObservationsSection.tsx"><a href="ObservationsSection.tsx.html">ObservationsSection.tsx</a></td>
	<td data-value="75" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	</tr>

<tr>
	<td class="file high" data-value="PropertySection.tsx"><a href="PropertySection.tsx.html">PropertySection.tsx</a></td>
	<td data-value="90.9" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="11" class="abs high">10/11</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="20" class="abs medium">15/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="11" class="abs high">10/11</td>
	</tr>

<tr>
	<td class="file medium" data-value="SubareaSection.tsx"><a href="SubareaSection.tsx.html">SubareaSection.tsx</a></td>
	<td data-value="76.92" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.92" class="pct medium">76.92%</td>
	<td data-value="13" class="abs medium">10/13</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="11" class="abs high">9/11</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-02T21:56:20.330Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    