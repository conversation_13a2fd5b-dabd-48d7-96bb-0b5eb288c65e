<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748893537595" clover="3.2.0">
  <project timestamp="1748893537595" name="All files">
    <metrics statements="2103" coveredstatements="1701" conditionals="1194" coveredconditionals="891" methods="852" coveredmethods="635" elements="4149" coveredelements="3227" complexity="0" loc="2103" ncloc="2103" packages="81" files="124" classes="124"/>
    <package name="src">
      <metrics statements="24" coveredstatements="18" conditionals="12" coveredconditionals="9" methods="8" coveredmethods="5"/>
      <file name="App.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/App.tsx">
        <metrics statements="24" coveredstatements="18" conditionals="12" coveredconditionals="9" methods="8" coveredmethods="5"/>
        <line num="18" count="1" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="31" count="4" type="stmt"/>
        <line num="32" count="4" type="stmt"/>
        <line num="33" count="4" type="stmt"/>
        <line num="36" count="4" type="stmt"/>
        <line num="38" count="4" type="stmt"/>
        <line num="39" count="4" type="stmt"/>
        <line num="40" count="4" type="stmt"/>
        <line num="42" count="4" type="stmt"/>
        <line num="43" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="44" count="3" type="stmt"/>
        <line num="53" count="4" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="2" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="4" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.custom-select">
      <metrics statements="7" coveredstatements="7" conditionals="11" coveredconditionals="10" methods="5" coveredmethods="3"/>
      <file name="CustomSelect.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/custom-select/CustomSelect.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="11" coveredconditionals="10" methods="5" coveredmethods="3"/>
        <line num="7" count="76" type="stmt"/>
        <line num="31" count="10" type="stmt"/>
        <line num="32" count="10" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="42" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="46" count="2" type="stmt"/>
        <line num="59" count="10" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.form-wrapper">
      <metrics statements="7" coveredstatements="6" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="1"/>
      <file name="FormWrapper.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/form-wrapper/FormWrapper.tsx">
        <metrics statements="7" coveredstatements="6" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="1"/>
        <line num="7" count="10" type="stmt"/>
        <line num="9" count="10" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="10" type="stmt"/>
        <line num="17" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="1" type="stmt"/>
        <line num="21" count="10" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.geometry-icon">
      <metrics statements="10" coveredstatements="10" conditionals="7" coveredconditionals="7" methods="6" coveredmethods="6"/>
      <file name="GeometryIcon.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/geometry-icon/GeometryIcon.tsx">
        <metrics statements="10" coveredstatements="10" conditionals="7" coveredconditionals="7" methods="6" coveredmethods="6"/>
        <line num="7" count="16" type="stmt"/>
        <line num="8" count="16" type="stmt"/>
        <line num="10" count="16" type="stmt"/>
        <line num="17" count="17" type="stmt"/>
        <line num="19" count="85" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="17" type="stmt"/>
        <line num="26" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="28" count="2" type="stmt"/>
        <line num="39" count="16" type="stmt"/>
        <line num="44" count="1" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.components.atoms.header-nav-button">
      <metrics statements="2" coveredstatements="2" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
      <file name="HeaderNavButton.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/header-nav-button/HeaderNavButton.tsx">
        <metrics statements="2" coveredstatements="2" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="19" count="18" type="stmt"/>
        <line num="26" count="4" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.components.atoms.map-wrapper">
      <metrics statements="9" coveredstatements="9" conditionals="4" coveredconditionals="3" methods="3" coveredmethods="2"/>
      <file name="MapWrapper.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/map-wrapper/MapWrapper.tsx">
        <metrics statements="9" coveredstatements="9" conditionals="4" coveredconditionals="3" methods="3" coveredmethods="2"/>
        <line num="18" count="7" type="stmt"/>
        <line num="19" count="7" type="stmt"/>
        <line num="20" count="7" type="stmt"/>
        <line num="23" count="7" type="stmt"/>
        <line num="24" count="4" type="stmt"/>
        <line num="31" count="4" type="stmt"/>
        <line num="48" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="49" count="4" type="stmt"/>
        <line num="52" count="7" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.multi-select">
      <metrics statements="3" coveredstatements="3" conditionals="1" coveredconditionals="1" methods="2" coveredmethods="2"/>
      <file name="MultiSelect.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/multi-select/MultiSelect.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="1" coveredconditionals="1" methods="2" coveredmethods="2"/>
        <line num="12" count="77" type="stmt"/>
        <line num="19" count="27" type="stmt"/>
        <line num="32" count="14" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.multi-select-auto-complete">
      <metrics statements="3" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
      <file name="MultiSelectAutoComplete.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/multi-select-auto-complete/MultiSelectAutoComplete.tsx">
        <metrics statements="3" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
        <line num="13" count="1" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.notification-description-generic-error">
      <metrics statements="1" coveredstatements="1" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="NotificationDescriptionGenericError.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/notification-description-generic-error/NotificationDescriptionGenericError.tsx">
        <metrics statements="1" coveredstatements="1" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="8" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.option-item">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="OptionItem.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/option-item/OptionItem.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="14" count="18" type="stmt"/>
        <line num="16" count="18" type="stmt"/>
        <line num="24" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.preview-table">
      <metrics statements="6" coveredstatements="6" conditionals="6" coveredconditionals="5" methods="4" coveredmethods="4"/>
      <file name="PreviewTable.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/preview-table/PreviewTable.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="6" coveredconditionals="5" methods="4" coveredmethods="4"/>
        <line num="5" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="6" count="4" type="stmt"/>
        <line num="8" count="4" type="stmt"/>
        <line num="13" count="8" type="stmt"/>
        <line num="31" count="6" type="stmt"/>
        <line num="34" count="12" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.section-batch-file-imported">
      <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="SectionBatchFileImported.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/section-batch-file-imported/SectionBatchFileImported.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="10" count="77" type="stmt"/>
        <line num="14" count="8" type="stmt"/>
        <line num="15" count="8" type="stmt"/>
      </file>
    </package>
    <package name="src.components.atoms.table-invalid-documents">
      <metrics statements="3" coveredstatements="3" conditionals="8" coveredconditionals="8" methods="2" coveredmethods="2"/>
      <file name="TableInvalidDocuments.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/atoms/table-invalid-documents/TableInvalidDocuments.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="8" coveredconditionals="8" methods="2" coveredmethods="2"/>
        <line num="13" count="77" type="stmt"/>
        <line num="20" count="16" type="stmt"/>
        <line num="50" count="19" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.auto-fields-tab">
      <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="2" methods="2" coveredmethods="2"/>
      <file name="AutoFieldsTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/auto-fields-tab/AutoFieldsTab.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="2" methods="2" coveredmethods="2"/>
        <line num="15" count="5" type="stmt"/>
        <line num="16" count="5" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.banner">
      <metrics statements="6" coveredstatements="6" conditionals="10" coveredconditionals="10" methods="3" coveredmethods="3"/>
      <file name="Banner.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/banner/Banner.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="10" coveredconditionals="10" methods="3" coveredmethods="3"/>
        <line num="24" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="26" count="5" type="stmt"/>
        <line num="28" count="5" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.checkbox-dropdown">
      <metrics statements="25" coveredstatements="23" conditionals="18" coveredconditionals="14" methods="10" coveredmethods="10"/>
      <file name="CheckboxDropdown.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/checkbox-dropdown/CheckboxDropdown.tsx">
        <metrics statements="25" coveredstatements="23" conditionals="18" coveredconditionals="14" methods="10" coveredmethods="10"/>
        <line num="28" count="72" type="stmt"/>
        <line num="38" count="14" type="stmt"/>
        <line num="40" count="14" type="stmt"/>
        <line num="41" count="14" type="stmt"/>
        <line num="43" count="14" type="stmt"/>
        <line num="45" count="14" type="stmt"/>
        <line num="46" count="9" type="stmt"/>
        <line num="49" count="14" type="stmt"/>
        <line num="50" count="14" type="stmt"/>
        <line num="51" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="55" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="72" count="14" type="stmt"/>
        <line num="74" count="14" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="107" count="24" type="stmt"/>
        <line num="112" count="22" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.form-list-item">
      <metrics statements="3" coveredstatements="3" conditionals="16" coveredconditionals="15" methods="2" coveredmethods="2"/>
      <file name="FormListItem.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/form-list-item/FormListItem.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="16" coveredconditionals="15" methods="2" coveredmethods="2"/>
        <line num="30" count="80" type="stmt"/>
        <line num="32" count="80" type="stmt"/>
        <line num="43" count="6" type="cond" truecount="6" falsecount="0"/>
      </file>
    </package>
    <package name="src.components.molecules.geometry-card">
      <metrics statements="14" coveredstatements="14" conditionals="16" coveredconditionals="16" methods="7" coveredmethods="6"/>
      <file name="GeometryCard.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/geometry-card/GeometryCard.tsx">
        <metrics statements="14" coveredstatements="14" conditionals="16" coveredconditionals="16" methods="7" coveredmethods="6"/>
        <line num="31" count="11" type="stmt"/>
        <line num="32" count="11" type="stmt"/>
        <line num="34" count="11" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="11" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="44" count="11" type="stmt"/>
        <line num="49" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="72" count="3" type="stmt"/>
        <line num="73" count="3" type="stmt"/>
        <line num="85" count="3" type="stmt"/>
        <line num="86" count="3" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.input-car">
      <metrics statements="44" coveredstatements="40" conditionals="24" coveredconditionals="22" methods="14" coveredmethods="12"/>
      <file name="InputCar.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/input-car/InputCar.tsx">
        <metrics statements="44" coveredstatements="40" conditionals="24" coveredconditionals="22" methods="14" coveredmethods="12"/>
        <line num="32" count="39" type="stmt"/>
        <line num="33" count="39" type="stmt"/>
        <line num="34" count="39" type="stmt"/>
        <line num="35" count="39" type="stmt"/>
        <line num="37" count="39" type="stmt"/>
        <line num="38" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="7" type="stmt"/>
        <line num="43" count="7" type="stmt"/>
        <line num="44" count="7" type="stmt"/>
        <line num="46" count="3" type="stmt"/>
        <line num="47" count="3" type="stmt"/>
        <line num="50" count="39" type="stmt"/>
        <line num="52" count="15" type="stmt"/>
        <line num="57" count="39" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="39" type="stmt"/>
        <line num="65" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="72" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="73" count="1" type="stmt"/>
        <line num="76" count="39" type="stmt"/>
        <line num="77" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="80" count="39" type="stmt"/>
        <line num="81" count="4" type="stmt"/>
        <line num="82" count="4" type="stmt"/>
        <line num="83" count="4" type="stmt"/>
        <line num="84" count="4" type="stmt"/>
        <line num="85" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="86" count="2" type="stmt"/>
        <line num="89" count="39" type="stmt"/>
        <line num="90" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="91" count="19" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="97" count="39" type="stmt"/>
        <line num="98" count="18" type="stmt"/>
        <line num="101" count="39" type="stmt"/>
        <line num="110" count="4" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="129" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.lat-long-tab">
      <metrics statements="28" coveredstatements="28" conditionals="18" coveredconditionals="16" methods="12" coveredmethods="12"/>
      <file name="LatLongTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/lat-long-tab/LatLongTab.tsx">
        <metrics statements="28" coveredstatements="28" conditionals="18" coveredconditionals="16" methods="12" coveredmethods="12"/>
        <line num="15" count="73" type="stmt"/>
        <line num="31" count="11" type="stmt"/>
        <line num="32" count="11" type="stmt"/>
        <line num="33" count="11" type="stmt"/>
        <line num="36" count="22" type="cond" truecount="2" falsecount="0"/>
        <line num="42" count="3" type="stmt"/>
        <line num="43" count="3" type="stmt"/>
        <line num="45" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="46" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="54" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="55" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="2" type="stmt"/>
        <line num="60" count="3" type="stmt"/>
        <line num="61" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="81" count="1" type="stmt"/>
        <line num="84" count="11" type="stmt"/>
        <line num="85" count="11" type="stmt"/>
        <line num="89" count="11" type="stmt"/>
        <line num="103" count="11" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="126" count="3" type="stmt"/>
        <line num="182" count="9" type="stmt"/>
        <line num="212" count="9" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.removable-list-file">
      <metrics statements="3" coveredstatements="3" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
      <file name="RemovableListFile.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/removable-list-file/RemovableListFile.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="26" count="21" type="stmt"/>
        <line num="27" count="21" type="stmt"/>
        <line num="45" count="4" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.components.molecules.report-about-column">
      <metrics statements="10" coveredstatements="10" conditionals="2" coveredconditionals="2" methods="5" coveredmethods="5"/>
      <file name="ReportAboutColumn.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/report-about-column/ReportAboutColumn.tsx">
        <metrics statements="10" coveredstatements="10" conditionals="2" coveredconditionals="2" methods="5" coveredmethods="5"/>
        <line num="8" count="73" type="stmt"/>
        <line num="9" count="73" type="stmt"/>
        <line num="17" count="29" type="stmt"/>
        <line num="18" count="29" type="stmt"/>
        <line num="19" count="85" type="cond" truecount="2" falsecount="0"/>
        <line num="21" count="29" type="stmt"/>
        <line num="22" count="83" type="stmt"/>
        <line num="24" count="35" type="stmt"/>
        <line num="46" count="29" type="stmt"/>
        <line num="49" count="29" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.report-card">
      <metrics statements="13" coveredstatements="13" conditionals="24" coveredconditionals="23" methods="8" coveredmethods="7"/>
      <file name="ReportCard.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/report-card/ReportCard.tsx">
        <metrics statements="13" coveredstatements="13" conditionals="24" coveredconditionals="23" methods="8" coveredmethods="7"/>
        <line num="34" count="7" type="stmt"/>
        <line num="35" count="7" type="stmt"/>
        <line num="37" count="7" type="stmt"/>
        <line num="39" count="7" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="43" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="49" count="7" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="82" count="4" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.report-status-indicator">
      <metrics statements="6" coveredstatements="6" conditionals="8" coveredconditionals="8" methods="2" coveredmethods="2"/>
      <file name="ReportStatusIndicator.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/report-status-indicator/ReportStatusIndicator.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="8" coveredconditionals="8" methods="2" coveredmethods="2"/>
        <line num="17" count="72" type="stmt"/>
        <line num="32" count="29" type="stmt"/>
        <line num="33" count="29" type="cond" truecount="2" falsecount="0"/>
        <line num="35" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="42" count="29" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.request-status-indicator">
      <metrics statements="21" coveredstatements="21" conditionals="19" coveredconditionals="19" methods="2" coveredmethods="2"/>
      <file name="RequestStatusIndicator.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/request-status-indicator/RequestStatusIndicator.tsx">
        <metrics statements="21" coveredstatements="21" conditionals="19" coveredconditionals="19" methods="2" coveredmethods="2"/>
        <line num="11" count="73" type="stmt"/>
        <line num="24" count="121" type="stmt"/>
        <line num="25" count="121" type="stmt"/>
        <line num="26" count="121" type="stmt"/>
        <line num="27" count="121" type="stmt"/>
        <line num="29" count="121" type="stmt"/>
        <line num="30" count="78" type="stmt"/>
        <line num="31" count="78" type="cond" truecount="2" falsecount="0"/>
        <line num="32" count="19" type="stmt"/>
        <line num="33" count="59" type="cond" truecount="2" falsecount="0"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="58" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="2" type="stmt"/>
        <line num="40" count="56" type="cond" truecount="4" falsecount="0"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="55" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="54" type="stmt"/>
        <line num="46" count="54" type="stmt"/>
        <line num="48" count="78" type="stmt"/>
        <line num="51" count="121" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.select-city">
      <metrics statements="9" coveredstatements="9" conditionals="12" coveredconditionals="11" methods="5" coveredmethods="5"/>
      <file name="SelectCity.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/select-city/SelectCity.tsx">
        <metrics statements="9" coveredstatements="9" conditionals="12" coveredconditionals="11" methods="5" coveredmethods="5"/>
        <line num="27" count="17" type="stmt"/>
        <line num="28" count="17" type="stmt"/>
        <line num="30" count="17" type="stmt"/>
        <line num="31" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="36" count="17" type="stmt"/>
        <line num="45" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.components.molecules.select-tag">
      <metrics statements="12" coveredstatements="6" conditionals="12" coveredconditionals="2" methods="8" coveredmethods="2"/>
      <file name="SelectTag.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/molecules/select-tag/SelectTag.tsx">
        <metrics statements="12" coveredstatements="6" conditionals="12" coveredconditionals="2" methods="8" coveredmethods="2"/>
        <line num="27" count="32" type="stmt"/>
        <line num="28" count="32" type="stmt"/>
        <line num="29" count="32" type="stmt"/>
        <line num="31" count="32" type="stmt"/>
        <line num="32" count="16" type="stmt"/>
        <line num="35" count="32" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.agro-credit-form">
      <metrics statements="19" coveredstatements="19" conditionals="12" coveredconditionals="12" methods="12" coveredmethods="12"/>
      <file name="AgroCreditForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/agro-credit-form/AgroCreditForm.tsx">
        <metrics statements="19" coveredstatements="19" conditionals="12" coveredconditionals="12" methods="12" coveredmethods="12"/>
        <line num="37" count="18" type="stmt"/>
        <line num="39" count="18" type="stmt"/>
        <line num="50" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="79" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="80" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.agro-credit-form-confirm">
      <metrics statements="6" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="1"/>
      <file name="AgroCreditFormConfirm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/agro-credit-form-confirm/AgroCreditFormConfirm.tsx">
        <metrics statements="6" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="1"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.agro-credit-form-review">
      <metrics statements="12" coveredstatements="12" conditionals="6" coveredconditionals="6" methods="7" coveredmethods="7"/>
      <file name="AgroCreditFormReview.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/agro-credit-form-review/AgroCreditFormReview.tsx">
        <metrics statements="12" coveredstatements="12" conditionals="6" coveredconditionals="6" methods="7" coveredmethods="7"/>
        <line num="39" count="9" type="stmt"/>
        <line num="40" count="9" type="stmt"/>
        <line num="41" count="9" type="stmt"/>
        <line num="42" count="9" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="49" count="9" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="85" count="2" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="152" count="4" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.agro-credit-form-scores">
      <metrics statements="16" coveredstatements="11" conditionals="2" coveredconditionals="1" methods="4" coveredmethods="2"/>
      <file name="AgroCreditFormScores.style.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/agro-credit-form-scores/AgroCreditFormScores.style.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="71" type="stmt"/>
        <line num="10" count="71" type="stmt"/>
        <line num="30" count="71" type="stmt"/>
        <line num="35" count="71" type="stmt"/>
        <line num="41" count="71" type="stmt"/>
        <line num="50" count="71" type="stmt"/>
      </file>
      <file name="AgroCreditFormScores.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/agro-credit-form-scores/AgroCreditFormScores.tsx">
        <metrics statements="10" coveredstatements="5" conditionals="2" coveredconditionals="1" methods="4" coveredmethods="2"/>
        <line num="28" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="2" type="stmt"/>
        <line num="47" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.agro-credit-upload">
      <metrics statements="25" coveredstatements="24" conditionals="14" coveredconditionals="13" methods="11" coveredmethods="9"/>
      <file name="AgroCreditUpload.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/agro-credit-upload/AgroCreditUpload.tsx">
        <metrics statements="25" coveredstatements="24" conditionals="14" coveredconditionals="13" methods="11" coveredmethods="9"/>
        <line num="21" count="7" type="stmt"/>
        <line num="22" count="7" type="stmt"/>
        <line num="23" count="7" type="stmt"/>
        <line num="26" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="27" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="35" count="8" type="stmt"/>
        <line num="40" count="8" type="stmt"/>
        <line num="43" count="2" type="stmt"/>
        <line num="48" count="2" type="stmt"/>
        <line num="49" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="51" count="2" type="stmt"/>
        <line num="52" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="55" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="76" count="7" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="116" count="3" type="stmt"/>
        <line num="117" count="3" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.base-request-form">
      <metrics statements="44" coveredstatements="31" conditionals="22" coveredconditionals="14" methods="16" coveredmethods="11"/>
      <file name="BaseRequestForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/BaseRequestForm.tsx">
        <metrics statements="44" coveredstatements="31" conditionals="22" coveredconditionals="14" methods="16" coveredmethods="11"/>
        <line num="82" count="15" type="stmt"/>
        <line num="102" count="15" type="stmt"/>
        <line num="111" count="15" type="stmt"/>
        <line num="112" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="144" count="15" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="147" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="148" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="156" count="15" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="3" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="170" count="15" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="177" count="15" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="210" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="221" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="234" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="235" count="1" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.base-request-form.components">
      <metrics statements="47" coveredstatements="33" conditionals="41" coveredconditionals="27" methods="20" coveredmethods="11"/>
      <file name="ContactSection.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/components/ContactSection.tsx">
        <metrics statements="11" coveredstatements="3" conditionals="13" coveredconditionals="6" methods="4" coveredmethods="1"/>
        <line num="31" count="70" type="stmt"/>
        <line num="41" count="7" type="stmt"/>
        <line num="43" count="7" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
      </file>
      <file name="MapSection.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/components/MapSection.tsx">
        <metrics statements="10" coveredstatements="8" conditionals="4" coveredconditionals="3" methods="5" coveredmethods="2"/>
        <line num="18" count="70" type="stmt"/>
        <line num="25" count="6" type="stmt"/>
        <line num="27" count="6" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="45" count="3" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="3" type="stmt"/>
        <line num="53" count="3" type="stmt"/>
        <line num="54" count="3" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
      </file>
      <file name="ObservationsSection.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/components/ObservationsSection.tsx">
        <metrics statements="4" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
        <line num="10" count="70" type="stmt"/>
        <line num="14" count="6" type="stmt"/>
        <line num="16" count="6" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
      </file>
      <file name="PropertySection.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/components/PropertySection.tsx">
        <metrics statements="11" coveredstatements="10" conditionals="20" coveredconditionals="15" methods="3" coveredmethods="3"/>
        <line num="18" count="70" type="stmt"/>
        <line num="28" count="8" type="stmt"/>
        <line num="30" count="8" type="stmt"/>
        <line num="39" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="10" type="cond" truecount="4" falsecount="0"/>
        <line num="41" count="6" type="stmt"/>
        <line num="43" count="10" type="cond" truecount="1" falsecount="1"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="10" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
      </file>
      <file name="SubareaSection.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/components/SubareaSection.tsx">
        <metrics statements="11" coveredstatements="9" conditionals="4" coveredconditionals="3" methods="6" coveredmethods="4"/>
        <line num="19" count="71" type="stmt"/>
        <line num="28" count="5" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="38" count="5" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="43" count="10" type="stmt"/>
        <line num="48" count="5" type="stmt"/>
        <line num="54" count="5" type="cond" truecount="1" falsecount="1"/>
      </file>
    </package>
    <package name="src.components.organisms.batch-request-form">
      <metrics statements="21" coveredstatements="21" conditionals="8" coveredconditionals="8" methods="10" coveredmethods="10"/>
      <file name="BatchRequestForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/batch-request-form/BatchRequestForm.tsx">
        <metrics statements="21" coveredstatements="21" conditionals="8" coveredconditionals="8" methods="10" coveredmethods="10"/>
        <line num="35" count="12" type="stmt"/>
        <line num="36" count="12" type="stmt"/>
        <line num="40" count="12" type="stmt"/>
        <line num="41" count="6" type="stmt"/>
        <line num="45" count="3" type="stmt"/>
        <line num="46" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="47" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="54" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="81" count="12" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="145" count="4" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.certification-2bsvs-form">
      <metrics statements="7" coveredstatements="7" conditionals="9" coveredconditionals="9" methods="3" coveredmethods="3"/>
      <file name="Certification2BSvsForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/certification-2bsvs-form/Certification2BSvsForm.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="9" coveredconditionals="9" methods="3" coveredmethods="3"/>
        <line num="9" count="76" type="stmt"/>
        <line num="16" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="29" count="10" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.certification-2bsvs-form-step-one">
      <metrics statements="7" coveredstatements="7" conditionals="4" coveredconditionals="4" methods="3" coveredmethods="3"/>
      <file name="Certification2bsvsFormStepOne.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/certification-2bsvs-form-step-one/Certification2bsvsFormStepOne.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="4" coveredconditionals="4" methods="3" coveredmethods="3"/>
        <line num="16" count="76" type="stmt"/>
        <line num="17" count="7" type="stmt"/>
        <line num="24" count="6" type="stmt"/>
        <line num="26" count="6" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.certification-2bsvs-form-step-two">
      <metrics statements="6" coveredstatements="6" conditionals="6" coveredconditionals="6" methods="3" coveredmethods="3"/>
      <file name="Certification2bsvsFormStepTwo.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/certification-2bsvs-form-step-two/Certification2bsvsFormStepTwo.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="6" coveredconditionals="6" methods="3" coveredmethods="3"/>
        <line num="21" count="77" type="stmt"/>
        <line num="27" count="6" type="stmt"/>
        <line num="28" count="6" type="stmt"/>
        <line num="30" count="6" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="91" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.certification-renova-bio-form">
      <metrics statements="4" coveredstatements="3" conditionals="6" coveredconditionals="6" methods="2" coveredmethods="1"/>
      <file name="CertificationRenovaBioForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/certification-renova-bio-form/CertificationRenovaBioForm.tsx">
        <metrics statements="4" coveredstatements="3" conditionals="6" coveredconditionals="6" methods="2" coveredmethods="1"/>
        <line num="11" count="3" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.certification-renova-bio-step-one">
      <metrics statements="6" coveredstatements="6" conditionals="2" coveredconditionals="2" methods="5" coveredmethods="4"/>
      <file name="CertificationRenovaBioStepOne.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/certification-renova-bio-step-one/CertificationRenovaBioStepOne.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="2" coveredconditionals="2" methods="5" coveredmethods="4"/>
        <line num="11" count="10" type="stmt"/>
        <line num="18" count="10" type="stmt"/>
        <line num="20" count="10" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.certification-renova-bio-step-two">
      <metrics statements="5" coveredstatements="5" conditionals="6" coveredconditionals="6" methods="3" coveredmethods="3"/>
      <file name="CertificationRenovaBioStepTwo.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/certification-renova-bio-step-two/CertificationRenovaBioStepTwo.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="6" coveredconditionals="6" methods="3" coveredmethods="3"/>
        <line num="26" count="7" type="stmt"/>
        <line num="27" count="7" type="stmt"/>
        <line num="29" count="7" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.contact-form">
      <metrics statements="8" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="5"/>
      <file name="ContactForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/contact-form/ContactForm.tsx">
        <metrics statements="8" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="5"/>
        <line num="19" count="12" type="stmt"/>
        <line num="28" count="12" type="stmt"/>
        <line num="30" count="12" type="stmt"/>
        <line num="42" count="13" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.contact-form-item">
      <metrics statements="36" coveredstatements="36" conditionals="24" coveredconditionals="23" methods="13" coveredmethods="13"/>
      <file name="ContactFormItem.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/contact-form-item/ContactFormItem.tsx">
        <metrics statements="36" coveredstatements="36" conditionals="24" coveredconditionals="23" methods="13" coveredmethods="13"/>
        <line num="17" count="71" type="stmt"/>
        <line num="29" count="35" type="stmt"/>
        <line num="30" count="35" type="stmt"/>
        <line num="39" count="35" type="stmt"/>
        <line num="40" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="47" count="4" type="stmt"/>
        <line num="48" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="49" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="59" count="3" type="stmt"/>
        <line num="62" count="35" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="70" count="35" type="stmt"/>
        <line num="95" count="4" type="stmt"/>
        <line num="104" count="4" type="stmt"/>
        <line num="105" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="115" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="123" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="139" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="153" count="2" type="stmt"/>
        <line num="157" count="2" type="stmt"/>
        <line num="158" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="170" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="187" count="3" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="199" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.deforestation-analysis-form">
      <metrics statements="103" coveredstatements="68" conditionals="75" coveredconditionals="41" methods="33" coveredmethods="25"/>
      <file name="DeforestationForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/deforestation-analysis-form/DeforestationForm.tsx">
        <metrics statements="103" coveredstatements="68" conditionals="75" coveredconditionals="41" methods="33" coveredmethods="25"/>
        <line num="17" count="71" type="stmt"/>
        <line num="23" count="12" type="stmt"/>
        <line num="25" count="12" type="stmt"/>
        <line num="26" count="12" type="stmt"/>
        <line num="29" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="31" count="11" type="stmt"/>
        <line num="32" count="22" type="stmt"/>
        <line num="34" count="8" type="stmt"/>
        <line num="35" count="11" type="stmt"/>
        <line num="38" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="7" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="45" count="11" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="58" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="2" type="stmt"/>
        <line num="65" count="2" type="stmt"/>
        <line num="66" count="4" type="stmt"/>
        <line num="69" count="2" type="stmt"/>
        <line num="71" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="72" count="1" type="stmt"/>
        <line num="75" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="80" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="87" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="89" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="2" type="stmt"/>
        <line num="149" count="2" type="stmt"/>
        <line num="150" count="4" type="stmt"/>
        <line num="153" count="2" type="stmt"/>
        <line num="155" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="156" count="1" type="stmt"/>
        <line num="159" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="164" count="1" type="stmt"/>
        <line num="165" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="166" count="1" type="stmt"/>
        <line num="168" count="1" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="177" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="185" count="12" type="stmt"/>
        <line num="186" count="12" type="stmt"/>
        <line num="189" count="12" type="stmt"/>
        <line num="202" count="7" type="stmt"/>
        <line num="203" count="13" type="stmt"/>
        <line num="205" count="7" type="stmt"/>
        <line num="206" count="13" type="stmt"/>
        <line num="208" count="7" type="stmt"/>
        <line num="209" count="7" type="stmt"/>
        <line num="210" count="7" type="stmt"/>
        <line num="212" count="7" type="cond" truecount="4" falsecount="0"/>
        <line num="213" count="1" type="stmt"/>
        <line num="216" count="6" type="stmt"/>
        <line num="218" count="6" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="1" type="stmt"/>
        <line num="265" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="266" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="291" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.deforestation-eudr-form">
      <metrics statements="10" coveredstatements="7" conditionals="8" coveredconditionals="4" methods="5" coveredmethods="3"/>
      <file name="DeforestationEUDRForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/deforestation-eudr-form/DeforestationEUDRForm.tsx">
        <metrics statements="10" coveredstatements="7" conditionals="8" coveredconditionals="4" methods="5" coveredmethods="3"/>
        <line num="11" count="70" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="3" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.fields-drawer">
      <metrics statements="32" coveredstatements="11" conditionals="19" coveredconditionals="10" methods="13" coveredmethods="1"/>
      <file name="FieldsDrawer.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/fields-drawer/FieldsDrawer.tsx">
        <metrics statements="32" coveredstatements="11" conditionals="19" coveredconditionals="10" methods="13" coveredmethods="1"/>
        <line num="44" count="6" type="stmt"/>
        <line num="45" count="6" type="stmt"/>
        <line num="46" count="6" type="stmt"/>
        <line num="47" count="6" type="stmt"/>
        <line num="48" count="6" type="stmt"/>
        <line num="49" count="6" type="stmt"/>
        <line num="66" count="6" type="stmt"/>
        <line num="82" count="6" type="stmt"/>
        <line num="91" count="6" type="stmt"/>
        <line num="99" count="6" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="6" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.fields-drawer.tabs">
      <metrics statements="53" coveredstatements="48" conditionals="30" coveredconditionals="22" methods="29" coveredmethods="22"/>
      <file name="ClickDrawTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/fields-drawer/tabs/ClickDrawTab.tsx">
        <metrics statements="31" coveredstatements="30" conditionals="26" coveredconditionals="20" methods="15" coveredmethods="14"/>
        <line num="17" count="70" type="stmt"/>
        <line num="24" count="13" type="stmt"/>
        <line num="26" count="13" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="69" count="7" type="stmt"/>
        <line num="72" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="73" count="3" type="stmt"/>
        <line num="74" count="3" type="stmt"/>
        <line num="75" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="77" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="83" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="84" count="2" type="stmt"/>
        <line num="85" count="2" type="stmt"/>
        <line num="86" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="88" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="89" count="2" type="stmt"/>
        <line num="92" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="93" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="95" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="97" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="98" count="1" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="109" count="78" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
      </file>
      <file name="FileUploadTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/fields-drawer/tabs/FileUploadTab.tsx">
        <metrics statements="22" coveredstatements="18" conditionals="4" coveredconditionals="2" methods="14" coveredmethods="8"/>
        <line num="17" count="71" type="stmt"/>
        <line num="24" count="4" type="stmt"/>
        <line num="35" count="8" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="55" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="2" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="74" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.file-upload-step">
      <metrics statements="24" coveredstatements="10" conditionals="22" coveredconditionals="8" methods="10" coveredmethods="4"/>
      <file name="FileUploadStep.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/file-upload-step/FileUploadStep.tsx">
        <metrics statements="24" coveredstatements="10" conditionals="22" coveredconditionals="8" methods="10" coveredmethods="4"/>
        <line num="18" count="5" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="23" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="61" count="5" type="stmt"/>
        <line num="83" count="3" type="stmt"/>
        <line num="84" count="3" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.file-upload-verify-step">
      <metrics statements="17" coveredstatements="17" conditionals="2" coveredconditionals="2" methods="7" coveredmethods="7"/>
      <file name="FileUploadVerifyStep.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/file-upload-verify-step/FileUploadVerifyStep.tsx">
        <metrics statements="17" coveredstatements="17" conditionals="2" coveredconditionals="2" methods="7" coveredmethods="7"/>
        <line num="25" count="7" type="stmt"/>
        <line num="26" count="7" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="58" count="7" type="stmt"/>
        <line num="103" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.header-nav-tabs">
      <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="3" coveredmethods="3"/>
      <file name="HeaderNavTabs.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/header-nav-tabs/HeaderNavTabs.tsx">
        <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="3" coveredmethods="3"/>
        <line num="9" count="5" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="22" count="2" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.components.organisms.home">
      <metrics statements="15" coveredstatements="10" conditionals="6" coveredconditionals="5" methods="7" coveredmethods="2"/>
      <file name="Home.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/home/<USER>">
        <metrics statements="15" coveredstatements="10" conditionals="6" coveredconditionals="5" methods="7" coveredmethods="2"/>
        <line num="17" count="4" type="stmt"/>
        <line num="18" count="4" type="stmt"/>
        <line num="19" count="4" type="stmt"/>
        <line num="20" count="4" type="stmt"/>
        <line num="21" count="4" type="stmt"/>
        <line num="23" count="4" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="4" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="4" type="stmt"/>
        <line num="38" count="3" type="stmt"/>
        <line num="41" count="4" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.inspection-form">
      <metrics statements="85" coveredstatements="68" conditionals="111" coveredconditionals="85" methods="39" coveredmethods="24"/>
      <file name="InspectionForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/inspection-form/InspectionForm.tsx">
        <metrics statements="85" coveredstatements="68" conditionals="111" coveredconditionals="85" methods="39" coveredmethods="24"/>
        <line num="46" count="54" type="stmt"/>
        <line num="47" count="54" type="stmt"/>
        <line num="50" count="54" type="stmt"/>
        <line num="52" count="54" type="stmt"/>
        <line num="54" count="54" type="stmt"/>
        <line num="66" count="54" type="stmt"/>
        <line num="75" count="54" type="stmt"/>
        <line num="87" count="54" type="stmt"/>
        <line num="88" count="26" type="stmt"/>
        <line num="89" count="26" type="stmt"/>
        <line num="90" count="26" type="stmt"/>
        <line num="94" count="10" type="stmt"/>
        <line num="101" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="105" count="10" type="stmt"/>
        <line num="109" count="18" type="stmt"/>
        <line num="110" count="18" type="stmt"/>
        <line num="111" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="112" count="18" type="cond" truecount="3" falsecount="1"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="120" count="5" type="stmt"/>
        <line num="121" count="5" type="stmt"/>
        <line num="124" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="125" count="5" type="stmt"/>
        <line num="126" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="127" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="133" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="134" count="3" type="stmt"/>
        <line num="136" count="33" type="cond" truecount="2" falsecount="0"/>
        <line num="139" count="27" type="stmt"/>
        <line num="141" count="3" type="stmt"/>
        <line num="142" count="3" type="stmt"/>
        <line num="144" count="3" type="stmt"/>
        <line num="146" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="147" count="1" type="stmt"/>
        <line num="150" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="151" count="2" type="stmt"/>
        <line num="159" count="2" type="stmt"/>
        <line num="160" count="2" type="stmt"/>
        <line num="163" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="164" count="3" type="stmt"/>
        <line num="166" count="3" type="stmt"/>
        <line num="167" count="3" type="stmt"/>
        <line num="168" count="3" type="stmt"/>
        <line num="180" count="3" type="stmt"/>
        <line num="182" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="183" count="3" type="stmt"/>
        <line num="184" count="3" type="stmt"/>
        <line num="187" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="189" count="1" type="stmt"/>
        <line num="195" count="54" type="stmt"/>
        <line num="196" count="5" type="cond" truecount="2" falsecount="2"/>
        <line num="197" count="5" type="stmt"/>
        <line num="202" count="5" type="stmt"/>
        <line num="203" count="5" type="stmt"/>
        <line num="206" count="54" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="54" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="239" count="5" type="stmt"/>
        <line num="240" count="5" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="273" count="2" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="317" count="1" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="348" count="108" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="379" count="324" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="394" count="1" type="stmt"/>
        <line num="405" count="1" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="425" count="486" type="stmt"/>
        <line num="429" count="486" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.new-property-drawer">
      <metrics statements="25" coveredstatements="16" conditionals="10" coveredconditionals="7" methods="8" coveredmethods="3"/>
      <file name="NewPropertyDrawer.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/new-property-drawer/NewPropertyDrawer.tsx">
        <metrics statements="25" coveredstatements="16" conditionals="10" coveredconditionals="7" methods="8" coveredmethods="3"/>
        <line num="43" count="15" type="stmt"/>
        <line num="44" count="15" type="stmt"/>
        <line num="45" count="15" type="stmt"/>
        <line num="59" count="15" type="stmt"/>
        <line num="73" count="15" type="stmt"/>
        <line num="82" count="15" type="stmt"/>
        <line num="88" count="15" type="stmt"/>
        <line num="90" count="15" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="99" count="15" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="125" count="5" type="stmt"/>
        <line num="127" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="128" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.new-property-drawer.tabs">
      <metrics statements="96" coveredstatements="71" conditionals="40" coveredconditionals="26" methods="35" coveredmethods="16"/>
      <file name="CarTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/new-property-drawer/tabs/CarTab.tsx">
        <metrics statements="18" coveredstatements="18" conditionals="4" coveredconditionals="2" methods="2" coveredmethods="2"/>
        <line num="16" count="71" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="24" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="50" count="5" type="stmt"/>
      </file>
      <file name="ClickDrawTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/new-property-drawer/tabs/ClickDrawTab.tsx">
        <metrics statements="31" coveredstatements="28" conditionals="26" coveredconditionals="19" methods="14" coveredmethods="11"/>
        <line num="15" count="71" type="stmt"/>
        <line num="20" count="10" type="stmt"/>
        <line num="21" count="10" type="stmt"/>
        <line num="23" count="10" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="68" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="74" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="79" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="80" count="1" type="stmt"/>
        <line num="83" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="88" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="89" count="1" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="100" count="20" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
      </file>
      <file name="FileUploadTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/new-property-drawer/tabs/FileUploadTab.tsx">
        <metrics statements="23" coveredstatements="1" conditionals="4" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="20" count="70" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
      </file>
      <file name="IncraTab.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/new-property-drawer/tabs/IncraTab.tsx">
        <metrics statements="24" coveredstatements="24" conditionals="6" coveredconditionals="5" methods="3" coveredmethods="3"/>
        <line num="25" count="71" type="stmt"/>
        <line num="26" count="11" type="stmt"/>
        <line num="27" count="11" type="stmt"/>
        <line num="29" count="11" type="stmt"/>
        <line num="30" count="4" type="stmt"/>
        <line num="31" count="4" type="stmt"/>
        <line num="32" count="4" type="stmt"/>
        <line num="33" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="34" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="1" type="stmt"/>
        <line num="56" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="61" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="62" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="74" count="11" type="stmt"/>
        <line num="81" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.portifolio-diagnosis-form">
      <metrics statements="17" coveredstatements="17" conditionals="12" coveredconditionals="12" methods="5" coveredmethods="5"/>
      <file name="PortifolioDiagnosisForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/portifolio-diagnosis-form/PortifolioDiagnosisForm.tsx">
        <metrics statements="17" coveredstatements="17" conditionals="12" coveredconditionals="12" methods="5" coveredmethods="5"/>
        <line num="11" count="10" type="stmt"/>
        <line num="19" count="10" type="stmt"/>
        <line num="21" count="10" type="stmt"/>
        <line num="22" count="9" type="stmt"/>
        <line num="37" count="9" type="stmt"/>
        <line num="40" count="9" type="stmt"/>
        <line num="41" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="42" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="5" type="stmt"/>
        <line num="44" count="5" type="stmt"/>
        <line num="45" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="47" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="48" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="53" count="9" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.portifolio-diagnosis-form-batch">
      <metrics statements="23" coveredstatements="23" conditionals="18" coveredconditionals="17" methods="12" coveredmethods="10"/>
      <file name="PortifolioDiagnosisFormBatch.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/portifolio-diagnosis-form-batch/PortifolioDiagnosisFormBatch.tsx">
        <metrics statements="23" coveredstatements="23" conditionals="18" coveredconditionals="17" methods="12" coveredmethods="10"/>
        <line num="24" count="11" type="stmt"/>
        <line num="31" count="11" type="stmt"/>
        <line num="32" count="10" type="stmt"/>
        <line num="38" count="11" type="stmt"/>
        <line num="41" count="76" type="stmt"/>
        <line num="43" count="14" type="stmt"/>
        <line num="44" count="12" type="stmt"/>
        <line num="50" count="12" type="stmt"/>
        <line num="53" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="57" count="5" type="stmt"/>
        <line num="58" count="5" type="stmt"/>
        <line num="59" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="60" count="1" type="stmt"/>
        <line num="62" count="4" type="stmt"/>
        <line num="66" count="10" type="stmt"/>
        <line num="68" count="10" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="115" count="6" type="stmt"/>
        <line num="117" count="6" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="186" count="1" type="stmt"/>
        <line num="209" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.portifolio-diagnosis-form-mannually">
      <metrics statements="28" coveredstatements="28" conditionals="18" coveredconditionals="15" methods="20" coveredmethods="18"/>
      <file name="PortiflioDiagnosisFormManually.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/portifolio-diagnosis-form-mannually/PortiflioDiagnosisFormManually.tsx">
        <metrics statements="28" coveredstatements="28" conditionals="18" coveredconditionals="15" methods="20" coveredmethods="18"/>
        <line num="16" count="76" type="stmt"/>
        <line num="18" count="76" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="24" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="27" count="76" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
        <line num="33" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="36" count="76" type="stmt"/>
        <line num="37" count="16" type="stmt"/>
        <line num="48" count="16" type="stmt"/>
        <line num="50" count="16" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="116" count="2" type="stmt"/>
        <line num="119" count="2" type="stmt"/>
        <line num="122" count="2" type="stmt"/>
        <line num="125" count="2" type="stmt"/>
        <line num="156" count="2" type="stmt"/>
        <line num="159" count="2" type="stmt"/>
        <line num="162" count="2" type="stmt"/>
        <line num="165" count="2" type="stmt"/>
        <line num="190" count="2" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="210" count="2" type="stmt"/>
        <line num="216" count="1" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
        <line num="233" count="1" type="stmt"/>
        <line num="235" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.protocol-eudr-cecafe-form">
      <metrics statements="5" coveredstatements="5" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
      <file name="ProtocolEudrCecafeForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/protocol-eudr-cecafe-form/ProtocolEudrCecafeForm.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="12" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="19" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.protocol-eudr-form">
      <metrics statements="7" coveredstatements="7" conditionals="16" coveredconditionals="16" methods="4" coveredmethods="4"/>
      <file name="ProtocolEudrForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/protocol-eudr-form/ProtocolEudrForm.tsx">
        <metrics statements="7" coveredstatements="7" conditionals="16" coveredconditionals="16" methods="4" coveredmethods="4"/>
        <line num="8" count="4" type="stmt"/>
        <line num="9" count="4" type="stmt"/>
        <line num="17" count="12" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="30" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="52" count="1" type="stmt"/>
        <line num="59" count="1" type="cond" truecount="2" falsecount="0"/>
      </file>
    </package>
    <package name="src.components.organisms.report-file-upload">
      <metrics statements="58" coveredstatements="58" conditionals="34" coveredconditionals="30" methods="14" coveredmethods="14"/>
      <file name="ReportFileUpload.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/report-file-upload/ReportFileUpload.tsx">
        <metrics statements="58" coveredstatements="58" conditionals="34" coveredconditionals="30" methods="14" coveredmethods="14"/>
        <line num="35" count="31" type="stmt"/>
        <line num="36" count="31" type="stmt"/>
        <line num="37" count="31" type="stmt"/>
        <line num="40" count="31" type="stmt"/>
        <line num="41" count="31" type="stmt"/>
        <line num="42" count="31" type="stmt"/>
        <line num="43" count="31" type="stmt"/>
        <line num="44" count="31" type="stmt"/>
        <line num="45" count="31" type="stmt"/>
        <line num="46" count="31" type="stmt"/>
        <line num="47" count="31" type="stmt"/>
        <line num="50" count="3" type="stmt"/>
        <line num="51" count="3" type="stmt"/>
        <line num="53" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="54" count="2" type="stmt"/>
        <line num="55" count="2" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="75" count="3" type="stmt"/>
        <line num="76" count="3" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="87" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="98" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="99" count="1" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="3" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="2" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="119" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="120" count="6" type="stmt"/>
        <line num="121" count="6" type="stmt"/>
        <line num="123" count="3" type="stmt"/>
        <line num="127" count="31" type="stmt"/>
        <line num="130" count="31" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="31" type="cond" truecount="2" falsecount="0"/>
        <line num="134" count="31" type="cond" truecount="2" falsecount="0"/>
        <line num="135" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="139" count="2" type="stmt"/>
        <line num="142" count="31" type="stmt"/>
        <line num="151" count="7" type="stmt"/>
        <line num="152" count="7" type="stmt"/>
        <line num="168" count="2" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.report-request-filters">
      <metrics statements="35" coveredstatements="13" conditionals="19" coveredconditionals="2" methods="9" coveredmethods="2"/>
      <file name="ReportRequestFilter.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/report-request-filters/ReportRequestFilter.tsx">
        <metrics statements="35" coveredstatements="13" conditionals="19" coveredconditionals="2" methods="9" coveredmethods="2"/>
        <line num="23" count="70" type="stmt"/>
        <line num="26" count="13" type="stmt"/>
        <line num="27" count="13" type="stmt"/>
        <line num="35" count="13" type="stmt"/>
        <line num="37" count="13" type="stmt"/>
        <line num="38" count="13" type="stmt"/>
        <line num="39" count="13" type="stmt"/>
        <line num="40" count="13" type="stmt"/>
        <line num="41" count="13" type="stmt"/>
        <line num="43" count="13" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="77" count="13" type="stmt"/>
        <line num="78" count="13" type="cond" truecount="1" falsecount="1"/>
        <line num="79" count="0" type="stmt"/>
        <line num="86" count="13" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.report-request-form">
      <metrics statements="26" coveredstatements="7" conditionals="19" coveredconditionals="15" methods="18" coveredmethods="5"/>
      <file name="ReportRequestForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/report-request-form/ReportRequestForm.tsx">
        <metrics statements="26" coveredstatements="7" conditionals="19" coveredconditionals="15" methods="18" coveredmethods="5"/>
        <line num="38" count="10" type="stmt"/>
        <line num="79" count="10" type="stmt"/>
        <line num="81" count="10" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="99" count="11" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="116" count="3" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.report-requests-table">
      <metrics statements="105" coveredstatements="48" conditionals="48" coveredconditionals="23" methods="38" coveredmethods="23"/>
      <file name="ReportRequestsTable.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/report-requests-table/ReportRequestsTable.tsx">
        <metrics statements="90" coveredstatements="33" conditionals="46" coveredconditionals="22" methods="24" coveredmethods="9"/>
        <line num="43" count="16" type="stmt"/>
        <line num="44" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="45" count="2" type="stmt"/>
        <line num="47" count="14" type="cond" truecount="4" falsecount="0"/>
        <line num="48" count="9" type="stmt"/>
        <line num="54" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="55" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="14" type="stmt"/>
        <line num="61" count="12" type="stmt"/>
        <line num="70" count="12" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="169" count="12" type="stmt"/>
        <line num="177" count="35" type="stmt"/>
        <line num="178" count="35" type="cond" truecount="2" falsecount="0"/>
        <line num="179" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="182" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="186" count="17" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="26" type="stmt"/>
        <line num="198" count="26" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="210" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="218" count="9" type="stmt"/>
        <line num="222" count="17" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="238" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="241" count="26" type="stmt"/>
        <line num="244" count="17" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="264" count="26" type="stmt"/>
        <line num="274" count="26" type="cond" truecount="1" falsecount="1"/>
        <line num="275" count="26" type="stmt"/>
        <line num="294" count="26" type="stmt"/>
        <line num="299" count="12" type="stmt"/>
        <line num="301" count="12" type="stmt"/>
        <line num="302" count="2" type="stmt"/>
        <line num="309" count="35" type="stmt"/>
        <line num="315" count="12" type="stmt"/>
        <line num="341" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="360" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="361" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
      </file>
      <file name="columns.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/report-requests-table/columns.tsx">
        <metrics statements="15" coveredstatements="15" conditionals="2" coveredconditionals="1" methods="14" coveredmethods="14"/>
        <line num="15" count="70" type="stmt"/>
        <line num="21" count="37" type="stmt"/>
        <line num="29" count="37" type="stmt"/>
        <line num="30" count="74" type="stmt"/>
        <line num="36" count="25" type="stmt"/>
        <line num="40" count="37" type="stmt"/>
        <line num="47" count="25" type="stmt"/>
        <line num="52" count="37" type="stmt"/>
        <line num="61" count="25" type="stmt"/>
        <line num="66" count="37" type="stmt"/>
        <line num="68" count="37" type="stmt"/>
        <line num="76" count="25" type="stmt"/>
        <line num="81" count="37" type="stmt"/>
        <line num="88" count="25" type="stmt"/>
        <line num="93" count="37" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.report-select">
      <metrics statements="17" coveredstatements="13" conditionals="14" coveredconditionals="14" methods="12" coveredmethods="8"/>
      <file name="ReportSelect.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/report-select/ReportSelect.tsx">
        <metrics statements="17" coveredstatements="13" conditionals="14" coveredconditionals="14" methods="12" coveredmethods="8"/>
        <line num="46" count="33" type="stmt"/>
        <line num="50" count="33" type="stmt"/>
        <line num="51" count="17" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="64" count="4" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="76" count="33" type="stmt"/>
        <line num="82" count="6" type="stmt"/>
        <line num="83" count="6" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.reports-table">
      <metrics statements="85" coveredstatements="80" conditionals="18" coveredconditionals="13" methods="32" coveredmethods="30"/>
      <file name="ReportsTable.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/reports-table/ReportsTable.tsx">
        <metrics statements="70" coveredstatements="65" conditionals="16" coveredconditionals="11" methods="19" coveredmethods="17"/>
        <line num="28" count="24" type="stmt"/>
        <line num="29" count="24" type="stmt"/>
        <line num="30" count="24" type="stmt"/>
        <line num="36" count="24" type="stmt"/>
        <line num="37" count="24" type="stmt"/>
        <line num="39" count="24" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="24" type="stmt"/>
        <line num="45" count="12" type="stmt"/>
        <line num="48" count="24" type="stmt"/>
        <line num="49" count="12" type="stmt"/>
        <line num="50" count="24" type="stmt"/>
        <line num="52" count="12" type="cond" truecount="1" falsecount="1"/>
        <line num="53" count="12" type="stmt"/>
        <line num="57" count="24" type="stmt"/>
        <line num="58" count="12" type="stmt"/>
        <line num="59" count="12" type="stmt"/>
        <line num="60" count="12" type="stmt"/>
        <line num="64" count="24" type="stmt"/>
        <line num="72" count="24" type="stmt"/>
        <line num="73" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="24" type="stmt"/>
        <line num="79" count="36" type="stmt"/>
        <line num="81" count="24" type="stmt"/>
        <line num="82" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="83" count="24" type="stmt"/>
        <line num="87" count="24" type="stmt"/>
        <line num="94" count="2" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="121" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="133" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="138" count="12" type="stmt"/>
        <line num="141" count="2" type="stmt"/>
        <line num="145" count="24" type="cond" truecount="1" falsecount="1"/>
        <line num="148" count="24" type="stmt"/>
        <line num="152" count="2" type="stmt"/>
        <line num="153" count="2" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="178" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="187" count="12" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="201" count="1" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="221" count="24" type="stmt"/>
        <line num="244" count="2" type="stmt"/>
        <line num="245" count="2" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="257" count="24" type="stmt"/>
        <line num="262" count="24" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
      </file>
      <file name="columns.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/reports-table/columns.tsx">
        <metrics statements="15" coveredstatements="15" conditionals="2" coveredconditionals="2" methods="13" coveredmethods="13"/>
        <line num="15" count="68" type="stmt"/>
        <line num="21" count="24" type="stmt"/>
        <line num="24" count="24" type="stmt"/>
        <line num="28" count="24" type="stmt"/>
        <line num="29" count="48" type="stmt"/>
        <line num="31" count="24" type="stmt"/>
        <line num="42" count="24" type="stmt"/>
        <line num="46" count="24" type="stmt"/>
        <line num="52" count="24" type="stmt"/>
        <line num="57" count="24" type="stmt"/>
        <line num="61" count="24" type="stmt"/>
        <line num="66" count="24" type="stmt"/>
        <line num="91" count="24" type="stmt"/>
        <line num="100" count="24" type="stmt"/>
        <line num="106" count="24" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.sinister-form">
      <metrics statements="27" coveredstatements="21" conditionals="42" coveredconditionals="21" methods="16" coveredmethods="11"/>
      <file name="SinisterForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/sinister-form/SinisterForm.tsx">
        <metrics statements="27" coveredstatements="21" conditionals="42" coveredconditionals="21" methods="16" coveredmethods="11"/>
        <line num="17" count="16" type="stmt"/>
        <line num="18" count="16" type="stmt"/>
        <line num="25" count="16" type="stmt"/>
        <line num="32" count="96" type="stmt"/>
        <line num="38" count="16" type="stmt"/>
        <line num="39" count="8" type="stmt"/>
        <line num="42" count="16" type="stmt"/>
        <line num="43" count="7" type="stmt"/>
        <line num="50" count="7" type="stmt"/>
        <line num="53" count="16" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="16" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="16" type="stmt"/>
        <line num="76" count="64" type="stmt"/>
        <line num="83" count="4" type="stmt"/>
        <line num="99" count="16" type="stmt"/>
        <line num="104" count="48" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="123" count="16" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="152" count="128" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.socioenvironmental-form">
      <metrics statements="46" coveredstatements="13" conditionals="20" coveredconditionals="4" methods="19" coveredmethods="4"/>
      <file name="SocioenvironmentalForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/socioenvironmental-form/SocioenvironmentalForm.tsx">
        <metrics statements="46" coveredstatements="13" conditionals="20" coveredconditionals="4" methods="19" coveredmethods="4"/>
        <line num="22" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="28" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="1" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.soy-deforestation-form">
      <metrics statements="46" coveredstatements="15" conditionals="30" coveredconditionals="5" methods="12" coveredmethods="5"/>
      <file name="SoyDeforestationForm.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/soy-deforestation-form/SoyDeforestationForm.tsx">
        <metrics statements="46" coveredstatements="15" conditionals="30" coveredconditionals="5" methods="12" coveredmethods="5"/>
        <line num="20" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="31" count="2" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="41" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="134" count="2" type="stmt"/>
        <line num="135" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="136" count="0" type="stmt"/>
        <line num="141" count="2" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.soy-deforestation-form-batch">
      <metrics statements="29" coveredstatements="28" conditionals="6" coveredconditionals="5" methods="10" coveredmethods="9"/>
      <file name="SoyDeforestationFormBatch.style.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/soy-deforestation-form-batch/SoyDeforestationFormBatch.style.ts">
        <metrics statements="8" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="3" count="76" type="stmt"/>
        <line num="9" count="76" type="stmt"/>
        <line num="20" count="76" type="stmt"/>
        <line num="24" count="76" type="stmt"/>
        <line num="31" count="76" type="stmt"/>
        <line num="38" count="76" type="stmt"/>
        <line num="45" count="76" type="stmt"/>
        <line num="52" count="76" type="stmt"/>
      </file>
      <file name="SoyDeforestationFormBatch.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/soy-deforestation-form-batch/SoyDeforestationFormBatch.tsx">
        <metrics statements="21" coveredstatements="20" conditionals="6" coveredconditionals="5" methods="10" coveredmethods="9"/>
        <line num="33" count="7" type="stmt"/>
        <line num="34" count="7" type="stmt"/>
        <line num="35" count="7" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="4" type="stmt"/>
        <line num="41" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="42" count="1" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="48" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="49" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="55" count="2" type="stmt"/>
        <line num="61" count="2" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="76" count="7" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="130" count="7" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.organisms.soy-deforestation-form-manual">
      <metrics statements="33" coveredstatements="11" conditionals="22" coveredconditionals="16" methods="19" coveredmethods="6"/>
      <file name="SoyDeforestationFormManual.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/soy-deforestation-form-manual/SoyDeforestationFormManual.tsx">
        <metrics statements="33" coveredstatements="11" conditionals="22" coveredconditionals="16" methods="19" coveredmethods="6"/>
        <line num="41" count="6" type="stmt"/>
        <line num="42" count="6" type="stmt"/>
        <line num="44" count="6" type="stmt"/>
        <line num="45" count="6" type="stmt"/>
        <line num="46" count="6" type="stmt"/>
        <line num="47" count="2" type="stmt"/>
        <line num="50" count="6" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="197" count="6" type="stmt"/>
        <line num="202" count="5" type="stmt"/>
        <line num="225" count="2" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="242" count="2" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.contexts">
      <metrics statements="87" coveredstatements="81" conditionals="25" coveredconditionals="17" methods="28" coveredmethods="19"/>
      <file name="AnalyticsDataLayerContext.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/contexts/AnalyticsDataLayerContext.tsx">
        <metrics statements="40" coveredstatements="40" conditionals="18" coveredconditionals="17" methods="11" coveredmethods="10"/>
        <line num="4" count="72" type="stmt"/>
        <line num="9" count="17" type="stmt"/>
        <line num="27" count="17" type="stmt"/>
        <line num="28" count="17" type="stmt"/>
        <line num="30" count="17" type="stmt"/>
        <line num="31" count="9" type="stmt"/>
        <line num="32" count="9" type="stmt"/>
        <line num="36" count="17" type="stmt"/>
        <line num="37" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="38" count="6" type="stmt"/>
        <line num="39" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="46" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="47" count="5" type="stmt"/>
        <line num="56" count="5" type="stmt"/>
        <line num="57" count="5" type="stmt"/>
        <line num="58" count="5" type="stmt"/>
        <line num="59" count="5" type="stmt"/>
        <line num="60" count="5" type="stmt"/>
        <line num="61" count="5" type="stmt"/>
        <line num="62" count="5" type="stmt"/>
        <line num="66" count="9" type="stmt"/>
        <line num="67" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="68" count="1" type="stmt"/>
        <line num="72" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="73" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="81" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="82" count="2" type="stmt"/>
        <line num="86" count="2" type="cond" truecount="4" falsecount="0"/>
        <line num="87" count="1" type="stmt"/>
        <line num="95" count="2" type="stmt"/>
        <line num="96" count="2" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="102" count="17" type="stmt"/>
        <line num="109" count="72" type="stmt"/>
        <line num="110" count="20" type="stmt"/>
      </file>
      <file name="DataContext.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/contexts/DataContext.tsx">
        <metrics statements="45" coveredstatements="39" conditionals="7" coveredconditionals="0" methods="15" coveredmethods="8"/>
        <line num="10" count="80" type="stmt"/>
        <line num="45" count="3" type="stmt"/>
        <line num="46" count="3" type="stmt"/>
        <line num="48" count="3" type="stmt"/>
        <line num="49" count="3" type="stmt"/>
        <line num="50" count="3" type="stmt"/>
        <line num="51" count="3" type="stmt"/>
        <line num="52" count="3" type="stmt"/>
        <line num="53" count="3" type="stmt"/>
        <line num="54" count="3" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="57" count="3" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="74" count="2" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
        <line num="79" count="2" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
        <line num="81" count="2" type="stmt"/>
        <line num="85" count="2" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
        <line num="88" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="94" count="2" type="stmt"/>
        <line num="99" count="2" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="101" count="2" type="stmt"/>
        <line num="104" count="3" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="3" type="stmt"/>
        <line num="110" count="2" type="stmt"/>
        <line num="111" count="2" type="stmt"/>
        <line num="112" count="2" type="stmt"/>
        <line num="113" count="2" type="stmt"/>
        <line num="114" count="2" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="118" count="3" type="stmt"/>
        <line num="143" count="80" type="stmt"/>
        <line num="144" count="2" type="stmt"/>
      </file>
      <file name="MapContext.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/contexts/MapContext.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="79" type="stmt"/>
      </file>
      <file name="RequestFormContext.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/contexts/RequestFormContext.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
        <line num="5" count="79" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.account.get-details">
      <metrics statements="14" coveredstatements="14" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="6"/>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/account/get-details/reducer.ts">
        <metrics statements="12" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="5" count="7" type="stmt"/>
        <line num="46" count="7" type="stmt"/>
        <line num="49" count="7" type="stmt"/>
        <line num="50" count="3" type="stmt"/>
        <line num="52" count="7" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="55" count="2" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="61" count="7" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="63" count="2" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/account/get-details/thunks.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="7" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.account.socio-environment-criterias">
      <metrics statements="8" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
      <file name="actions.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/account/socio-environment-criterias/actions.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="7" type="stmt"/>
        <line num="9" count="7" type="stmt"/>
      </file>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/account/socio-environment-criterias/reducer.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
        <line num="9" count="7" type="stmt"/>
        <line num="18" count="7" type="stmt"/>
        <line num="21" count="7" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="28" count="7" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.card.list">
      <metrics statements="12" coveredstatements="12" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="6"/>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/card/list/reducer.ts">
        <metrics statements="10" coveredstatements="10" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="5" count="7" type="stmt"/>
        <line num="11" count="7" type="stmt"/>
        <line num="12" count="7" type="stmt"/>
        <line num="13" count="3" type="stmt"/>
        <line num="16" count="7" type="stmt"/>
        <line num="17" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="21" count="7" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/card/list/thunks.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="5" count="7" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.certification-2bsvs">
      <metrics statements="39" coveredstatements="39" conditionals="0" coveredconditionals="0" methods="14" coveredmethods="14"/>
      <file name="actions.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/certification-2bsvs/actions.ts">
        <metrics statements="8" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="79" type="stmt"/>
        <line num="11" count="79" type="stmt"/>
        <line num="15" count="79" type="stmt"/>
        <line num="20" count="79" type="stmt"/>
        <line num="22" count="79" type="stmt"/>
        <line num="27" count="79" type="stmt"/>
        <line num="31" count="79" type="stmt"/>
        <line num="35" count="79" type="stmt"/>
      </file>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/certification-2bsvs/reducer.ts">
        <metrics statements="26" coveredstatements="26" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="12"/>
        <line num="21" count="7" type="stmt"/>
        <line num="38" count="7" type="stmt"/>
        <line num="41" count="7" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="45" count="7" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="49" count="7" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="53" count="7" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="57" count="7" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="64" count="7" type="stmt"/>
        <line num="65" count="2" type="stmt"/>
        <line num="68" count="7" type="stmt"/>
        <line num="69" count="2" type="stmt"/>
        <line num="75" count="7" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="82" count="7" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="89" count="7" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="95" count="7" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/certification-2bsvs/thunks.ts">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="9" count="79" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.certification-renova-bio-form">
      <metrics statements="44" coveredstatements="44" conditionals="6" coveredconditionals="5" methods="14" coveredmethods="14"/>
      <file name="actions.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/certification-renova-bio-form/actions.ts">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="79" type="stmt"/>
        <line num="14" count="79" type="stmt"/>
        <line num="18" count="79" type="stmt"/>
        <line num="22" count="79" type="stmt"/>
        <line num="26" count="79" type="stmt"/>
      </file>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/certification-renova-bio-form/reducer.ts">
        <metrics statements="21" coveredstatements="21" conditionals="0" coveredconditionals="0" methods="9" coveredmethods="9"/>
        <line num="15" count="7" type="stmt"/>
        <line num="33" count="7" type="stmt"/>
        <line num="36" count="7" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="43" count="7" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="47" count="7" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="52" count="7" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="56" count="7" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="63" count="7" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="66" count="7" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="71" count="7" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/certification-renova-bio-form/thunks.ts">
        <metrics statements="18" coveredstatements="18" conditionals="6" coveredconditionals="5" methods="5" coveredmethods="5"/>
        <line num="10" count="79" type="stmt"/>
        <line num="13" count="5" type="stmt"/>
        <line num="14" count="5" type="stmt"/>
        <line num="15" count="5" type="stmt"/>
        <line num="16" count="5" type="stmt"/>
        <line num="21" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="22" count="2" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="4" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="29" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="30" count="2" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="3" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="39" count="3" type="stmt"/>
        <line num="41" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.portifolio-diagnosis">
      <metrics statements="57" coveredstatements="57" conditionals="2" coveredconditionals="2" methods="20" coveredmethods="20"/>
      <file name="actions.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/portifolio-diagnosis/actions.ts">
        <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="11" count="46" type="stmt"/>
        <line num="14" count="46" type="stmt"/>
        <line num="18" count="46" type="stmt"/>
        <line num="22" count="46" type="stmt"/>
        <line num="26" count="46" type="stmt"/>
        <line num="30" count="46" type="stmt"/>
        <line num="35" count="46" type="stmt"/>
        <line num="39" count="46" type="stmt"/>
        <line num="43" count="46" type="stmt"/>
        <line num="47" count="46" type="stmt"/>
        <line num="52" count="46" type="stmt"/>
        <line num="57" count="46" type="stmt"/>
        <line num="61" count="46" type="stmt"/>
      </file>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/portifolio-diagnosis/reducer.ts">
        <metrics statements="40" coveredstatements="40" conditionals="2" coveredconditionals="2" methods="18" coveredmethods="18"/>
        <line num="28" count="8" type="stmt"/>
        <line num="55" count="8" type="stmt"/>
        <line num="58" count="8" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="65" count="8" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="71" count="8" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="78" count="8" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="85" count="8" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="89" count="8" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="95" count="8" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="101" count="8" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="109" count="8" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="116" count="8" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="123" count="8" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="130" count="8" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="136" count="8" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="143" count="8" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="150" count="8" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="157" count="8" type="stmt"/>
        <line num="160" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="161" count="2" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/portifolio-diagnosis/thunks.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="8" count="45" type="stmt"/>
        <line num="14" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.report-requests.list">
      <metrics statements="33" coveredstatements="33" conditionals="4" coveredconditionals="4" methods="9" coveredmethods="9"/>
      <file name="actions.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/report-requests/list/actions.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="8" type="stmt"/>
        <line num="14" count="8" type="stmt"/>
        <line num="18" count="8" type="stmt"/>
      </file>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/report-requests/list/reducer.ts">
        <metrics statements="25" coveredstatements="25" conditionals="4" coveredconditionals="4" methods="7" coveredmethods="7"/>
        <line num="10" count="8" type="stmt"/>
        <line num="30" count="8" type="stmt"/>
        <line num="33" count="8" type="stmt"/>
        <line num="34" count="3" type="stmt"/>
        <line num="35" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="38" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="1" type="stmt"/>
        <line num="43" count="8" type="stmt"/>
        <line num="44" count="2" type="stmt"/>
        <line num="45" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="49" count="2" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="56" count="8" type="stmt"/>
        <line num="57" count="2" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="62" count="8" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="69" count="8" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="76" count="8" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/report-requests/list/thunks.ts">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="7" count="8" type="stmt"/>
        <line num="12" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.report.list">
      <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="6"/>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/report/list/reducer.ts">
        <metrics statements="11" coveredstatements="11" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="5" count="6" type="stmt"/>
        <line num="14" count="6" type="stmt"/>
        <line num="15" count="6" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="19" count="6" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="25" count="6" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/report/list/thunks.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="6" count="6" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.store.modules.soy-deforestation-form">
      <metrics statements="33" coveredstatements="33" conditionals="6" coveredconditionals="6" methods="11" coveredmethods="11"/>
      <file name="actions.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/soy-deforestation-form/actions.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="80" type="stmt"/>
        <line num="9" count="80" type="stmt"/>
        <line num="13" count="80" type="stmt"/>
        <line num="17" count="80" type="stmt"/>
      </file>
      <file name="reducer.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/soy-deforestation-form/reducer.ts">
        <metrics statements="23" coveredstatements="23" conditionals="6" coveredconditionals="6" methods="8" coveredmethods="8"/>
        <line num="13" count="82" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="48" count="82" type="stmt"/>
        <line num="51" count="80" type="stmt"/>
        <line num="52" count="80" type="stmt"/>
        <line num="53" count="80" type="stmt"/>
        <line num="54" count="80" type="stmt"/>
        <line num="55" count="4" type="stmt"/>
        <line num="56" count="4" type="stmt"/>
        <line num="58" count="80" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="62" count="80" type="stmt"/>
        <line num="65" count="4" type="stmt"/>
        <line num="66" count="4" type="stmt"/>
        <line num="67" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="68" count="2" type="stmt"/>
        <line num="70" count="2" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
        <line num="75" count="80" type="stmt"/>
      </file>
      <file name="thunks.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/store/modules/soy-deforestation-form/thunks.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
        <line num="7" count="82" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="16" count="4" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="162" coveredstatements="158" conditionals="78" coveredconditionals="77" methods="61" coveredmethods="59"/>
      <file name="account.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/account.ts">
        <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="5" count="2" type="stmt"/>
        <line num="6" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="7" count="1" type="stmt"/>
      </file>
      <file name="car.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/car.ts">
        <metrics statements="11" coveredstatements="11" conditionals="4" coveredconditionals="4" methods="3" coveredmethods="3"/>
        <line num="1" count="79" type="stmt"/>
        <line num="3" count="9" type="stmt"/>
        <line num="4" count="9" type="stmt"/>
        <line num="7" count="79" type="stmt"/>
        <line num="8" count="14" type="stmt"/>
        <line num="9" count="14" type="stmt"/>
        <line num="10" count="14" type="stmt"/>
        <line num="11" count="14" type="stmt"/>
        <line num="12" count="14" type="cond" truecount="4" falsecount="0"/>
        <line num="15" count="79" type="stmt"/>
        <line num="16" count="8" type="stmt"/>
      </file>
      <file name="crops.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/crops.ts">
        <metrics statements="7" coveredstatements="7" conditionals="9" coveredconditionals="9" methods="3" coveredmethods="3"/>
        <line num="33" count="11" type="stmt"/>
        <line num="36" count="88" type="stmt"/>
        <line num="37" count="88" type="stmt"/>
        <line num="49" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="50" count="3" type="cond" truecount="5" falsecount="0"/>
        <line num="51" count="1" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
      </file>
      <file name="csv.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/csv.ts">
        <metrics statements="10" coveredstatements="10" conditionals="6" coveredconditionals="6" methods="4" coveredmethods="4"/>
        <line num="7" count="7" type="cond" truecount="4" falsecount="0"/>
        <line num="8" count="2" type="stmt"/>
        <line num="11" count="5" type="stmt"/>
        <line num="13" count="5" type="stmt"/>
        <line num="14" count="5" type="stmt"/>
        <line num="16" count="5" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="32" count="5" type="stmt"/>
      </file>
      <file name="date.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/date.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="4" count="66" type="stmt"/>
        <line num="5" count="66" type="stmt"/>
        <line num="9" count="66" type="stmt"/>
        <line num="10" count="66" type="stmt"/>
      </file>
      <file name="document.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/document.ts">
        <metrics statements="35" coveredstatements="35" conditionals="18" coveredconditionals="18" methods="12" coveredmethods="12"/>
        <line num="2" count="29" type="stmt"/>
        <line num="3" count="29" type="cond" truecount="4" falsecount="0"/>
        <line num="4" count="2" type="stmt"/>
        <line num="6" count="27" type="cond" truecount="2" falsecount="0"/>
        <line num="7" count="14" type="stmt"/>
        <line num="8" count="13" type="stmt"/>
        <line num="14" count="6" type="stmt"/>
        <line num="15" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="3" type="stmt"/>
        <line num="20" count="8" type="stmt"/>
        <line num="21" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="5" type="stmt"/>
        <line num="23" count="5" type="stmt"/>
        <line num="24" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="29" count="5" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="32" count="5" type="stmt"/>
        <line num="33" count="5" type="stmt"/>
        <line num="37" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="95" type="stmt"/>
        <line num="42" count="10" type="stmt"/>
        <line num="43" count="95" type="stmt"/>
        <line num="44" count="85" type="stmt"/>
        <line num="45" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="49" count="10" type="stmt"/>
        <line num="50" count="10" type="stmt"/>
        <line num="52" count="125" type="stmt"/>
        <line num="53" count="10" type="stmt"/>
        <line num="54" count="125" type="stmt"/>
        <line num="55" count="125" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="125" type="stmt"/>
        <line num="58" count="10" type="stmt"/>
        <line num="59" count="10" type="cond" truecount="2" falsecount="0"/>
      </file>
      <file name="file.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/file.ts">
        <metrics statements="8" coveredstatements="8" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="4"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
      </file>
      <file name="geo.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/geo.ts">
        <metrics statements="13" coveredstatements="13" conditionals="1" coveredconditionals="1" methods="9" coveredmethods="9"/>
        <line num="10" count="102" type="stmt"/>
        <line num="14" count="102" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="21" count="15" type="stmt"/>
        <line num="25" count="3" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="33" count="12" type="stmt"/>
        <line num="35" count="12" type="stmt"/>
        <line num="40" count="12" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="60" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
      </file>
      <file name="incra.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/incra.ts">
        <metrics statements="7" coveredstatements="7" conditionals="8" coveredconditionals="8" methods="7" coveredmethods="7"/>
        <line num="2" count="1" type="stmt"/>
        <line num="9" count="11" type="stmt"/>
        <line num="16" count="23" type="stmt"/>
        <line num="20" count="15" type="stmt"/>
        <line num="24" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="28" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="32" count="5" type="cond" truecount="2" falsecount="0"/>
      </file>
      <file name="kml.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/kml.ts">
        <metrics statements="27" coveredstatements="23" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="5"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
      </file>
      <file name="number.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/number.ts">
        <metrics statements="5" coveredstatements="5" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="1" count="71" type="stmt"/>
        <line num="2" count="6" type="stmt"/>
        <line num="3" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="5" count="6" type="stmt"/>
        <line num="12" count="3" type="stmt"/>
      </file>
      <file name="parse-object.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/parse-object.ts">
        <metrics statements="4" coveredstatements="4" conditionals="4" coveredconditionals="4" methods="2" coveredmethods="2"/>
        <line num="2" count="3" type="stmt"/>
        <line num="3" count="7" type="cond" truecount="4" falsecount="0"/>
        <line num="4" count="3" type="stmt"/>
        <line num="7" count="3" type="stmt"/>
      </file>
      <file name="shp.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/shp.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="7" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
      </file>
      <file name="status.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/status.ts">
        <metrics statements="23" coveredstatements="23" conditionals="22" coveredconditionals="21" methods="3" coveredmethods="3"/>
        <line num="6" count="70" type="stmt"/>
        <line num="15" count="58" type="stmt"/>
        <line num="19" count="41" type="stmt"/>
        <line num="20" count="41" type="cond" truecount="2" falsecount="0"/>
        <line num="21" count="10" type="stmt"/>
        <line num="22" count="31" type="cond" truecount="2" falsecount="0"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="30" type="cond" truecount="2" falsecount="0"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="29" type="cond" truecount="2" falsecount="0"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="32" count="1" type="stmt"/>
        <line num="34" count="41" type="stmt"/>
        <line num="43" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="44" count="4" type="stmt"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="48" count="4" type="stmt"/>
        <line num="51" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="53" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="54" count="2" type="stmt"/>
        <line num="57" count="2" type="stmt"/>
      </file>
      <file name="text.ts" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/utils/text.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="2" count="13" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
