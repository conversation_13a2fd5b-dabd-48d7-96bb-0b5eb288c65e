<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748900126896" clover="3.2.0">
  <project timestamp="1748900126896" name="All files">
    <metrics statements="11" coveredstatements="10" conditionals="13" coveredconditionals="13" methods="4" coveredmethods="4" elements="28" coveredelements="27" complexity="0" loc="11" ncloc="11" packages="1" files="1" classes="1"/>
    <file name="ContactSection.tsx" path="/Users/<USER>/Library/CloudStorage/OneDrive-EXPERIANSERVICESCORP/Documentos/brain-ag/agro-report/pb-agro-report-frontend/src/components/organisms/base-request-form/components/ContactSection.tsx">
      <metrics statements="11" coveredstatements="10" conditionals="13" coveredconditionals="13" methods="4" coveredmethods="4"/>
      <line num="31" count="1" type="stmt"/>
      <line num="41" count="21" type="stmt"/>
      <line num="43" count="21" type="stmt"/>
      <line num="49" count="42" type="stmt"/>
      <line num="60" count="3" type="stmt"/>
      <line num="61" count="3" type="cond" truecount="2" falsecount="0"/>
      <line num="62" count="1" type="stmt"/>
      <line num="63" count="1" type="stmt"/>
      <line num="65" count="2" type="stmt"/>
      <line num="68" count="0" type="stmt"/>
      <line num="71" count="1" type="stmt"/>
    </file>
  </project>
</coverage>
