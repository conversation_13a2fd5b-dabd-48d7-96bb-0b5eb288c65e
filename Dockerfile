FROM dockerhub.agribusiness-brain.br.experian.eeca/library/node:20-alpine
ENV NODE_OPTIONS=--openssl-legacy-provider
WORKDIR /app
COPY . .

# Definir argumento de ambiente
ARG ENV=
COPY .env.${ENV} .env

ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy
ARG NODE_CA_CERTS

ENV NODE_EXTRA_CA_CERTS /app/${NODE_CA_CERTS}
ENV HTTP_PROXY=${HTTP_PROXY:-$http_proxy}
ENV HTTPS_PROXY=${HTTPS_PROXY:-$https_proxy}
ENV NO_PROXY=${NO_PROXY:-$no_proxy}
ENV http_proxy=${http_proxy:-$HTTP_PROXY}
ENV https_proxy=${https_proxy:-$HTTPS_PROXY}
ENV no_proxy=${no_proxy:-$NO_PROXY}

RUN yarn config set network-timeout 600000 && \
    for i in {1..3}; do yarn --update-checksums && yarn install --ignore-scripts && break || sleep 1; done && \
    for i in {1..3}; do yarn upgrade && break || sleep 1; done && \
    for i in {1..3}; do yarn build:webpack && break || sleep 1; done
EXPOSE 3333
RUN for i in {1..3}; do yarn global add pm2 && break || sleep 1; done

ENV FILES="yarn.lock package-lock.json"
RUN for file in "${FILES}"; do rm -f "$file"; done && \
    apk update && \
    apk upgrade

ENTRYPOINT ["pm2-runtime", "start", "ecosystem.config.js"]