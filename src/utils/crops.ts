export enum Crop {
  SOYBEAN = "SOY<PERSON><PERSON>",
  CORN = "CORN",
  COTTON = "COTTON",
  WHEAT = "WHEAT",
  COFFEE = "COFFEE",
  SUGAR_CANE = "SUGAR_CANE",
}

export enum Commodities {
  SOYBEAN = "SOYBEAN",
  COFFEE = "COFFEE",
  BEEF = "BEEF",
}

export enum SoilType {
  AD1 = "AD1",
  AD2 = "AD2",
  AD3 = "AD3",
  AD4 = "AD4",
  AD5 = "AD5",
  AD6 = "AD6",
  SANDY = "SANDY",
  AVERAGE = "AVERAGE",
  CLAYISH = "CLAYISH",
}

export enum EventSinister {
  SECA = "SECA",
}

export function getCropYears(): { label: string; value: string }[] {
  return new Array(2025 - 2017)
    .fill(2017)
    .map((baseYear, index) => {
      const year = baseYear + index;
      return [
        { label: `1ª Safra ${year}/${year + 1}`, value: `${year}/${year + 1}` },
        {
          label: `2ª Safra ${year + 1}/${year + 1}`,
          value: `${year + 1}/${year + 1}`,
        },
      ];
    })
    .flat();
}

export function getSoilByCropYear(crop?: Crop, cropYear?: string): string[] {
  if (!crop || !cropYear) return [];
  if ((crop == Crop.SOYBEAN || crop == Crop.WHEAT) && cropYear >= "2023/2024") {
    return [
      SoilType.AD1,
      SoilType.AD2,
      SoilType.AD3,
      SoilType.AD4,
      SoilType.AD5,
      SoilType.AD6,
    ];
  }
  return [SoilType.SANDY, SoilType.AVERAGE, SoilType.CLAYISH];
}
