import { Map } from "ol";
import Feature, { FeatureLike } from "ol/Feature";
import { Extent } from "ol/extent";
import GeoJSON, { GeoJSONGeometry } from "ol/format/GeoJSON.js";
import { Geometry, Polygon } from "ol/geom";
import { register } from "ol/proj/proj4";
import VectorSource from "ol/source/Vector";
import proj4 from "proj4";

proj4.defs(
  "EPSG:98056",
  "+proj=aea +lat_1=-2 +lat_2=-22 +lat_0=-12 +lon_0=-54 +x_0=5000000 +y_0=10000000 +ellps=GRS80 +units=m +no_defs"
);
register(proj4);

export function geojsonToFeature(geojson: GeoJSONGeometry): Feature {
  return new GeoJSON().readFeature(geojson, {}) as Feature;
}

export function geojsonToFeatures(geojson: GeoJSONGeometry): Feature[] {
  return new GeoJSON().readFeatures(geojson, {}) as Feature[];
}

export function geojsonToGeometry(geojson: GeoJSONGeometry): Geometry {
  return new GeoJSON().readGeometry(geojson, {});
}

export function geometryToGeojson(feature: Geometry): GeoJSONGeometry {
  return new GeoJSON().writeGeometryObject(feature, {});
}

export function getExtent(features: FeatureLike[]): Extent {
  return features
    .map((feature) => {
      return feature.getGeometry()?.getExtent();
    })
    .filter(Boolean)
    .reduce(
      (out, line) => {
        return [
          Math.min(out![0], line![0]),
          Math.min(out![1], line![1]),
          Math.max(out![2], line![2]),
          Math.max(out![3], line![3]),
        ];
      },
      [Infinity, Infinity, -Infinity, -Infinity]
    ) as Extent;
}

export function fitMap(
  map: Map,
  source: VectorSource<FeatureLike>,
  padding: number[] = [100, 100]
) {
  map.getView().fit(source.getExtent(), { padding });
}

export function getArea(geojson: GeoJSONGeometry): number {
  let geometry = geojsonToGeometry(geojson);
  geometry = geometry.transform("EPSG:4326", "EPSG:98056");
  return (geometry as Polygon).getArea();
}
