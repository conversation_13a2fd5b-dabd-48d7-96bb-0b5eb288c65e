export async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(new Error(`FileReader error: ${error}`));
  });
}

export async function base64ToFile(
  base64: string,
  fileName: string,
  type: string
): Promise<File> {
  const response = await fetch(base64);
  const blob = await response.blob();
  return new File([blob], fileName, { type });
}
