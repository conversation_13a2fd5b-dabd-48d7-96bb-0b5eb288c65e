import <PERSON> from "papaparse";

export async function readCSV<T>(
  file: File,
  transformHeader?: (header: string, index: number) => string
): Promise<T[]> {
  if (!file || file.type !== "text/csv") {
    throw new Error("Invalid file");
  }

  const reader = new FileReader();

  const loader = (): Promise<Papa.ParseResult<T>> => {
    reader.readAsText(file);

    return new Promise((resolve) => {
      reader.onload = ({ target }) => {
        resolve(
          Papa.parse<T>(target.result as string, {
            header: true,
            skipEmptyLines: true,
            delimiter: ";",
            ...(transformHeader ? { transformHeader } : {}),
          })
        );
      };
    });
  };

  const { data } = await loader();

  return data;
}
