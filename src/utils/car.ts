export const isCarValid = (value: string) => {
  const pattern =
    /^(\s*(AC|AL|AP|AM|BA|CE|DF|ES|GO|MA|MT|MS|MG|PA|PB|PR|PE|PI|RJ|RN|RS|RO|RR|SC|SP|SE|TO)?)-([0-9]{7})-([A-Z0-9]{32})$/;
  return pattern.test(value);
};

export const maskCAR = (value: string) => {
  const cleaned = value.replace(/[^a-zA-Z0-9]/g, "").toUpperCase();
  const letters = cleaned.substring(0, 2);
  const first = cleaned.substring(2, 9);
  const second = cleaned.substring(9, 41);
  return letters + (first ? "-" + first : "") + (second ? "-" + second : "");
};

export const removeMaskCAR = (value: string) => {
  return value.split("-").join("");
};
