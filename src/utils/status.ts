import { ReportType } from "@types";
import { ReportRequest } from "../types/report-request";
import { ReportStatus } from "../types/report-status";
import { REPORT_CARD_METADATA } from "@constants";

const COLOR = {
  [ReportStatus.ERROR]: "#A8001F",
  [ReportStatus.DONE]: "#00B277",
  [ReportStatus.PENDING]: "#C1C7D0",
  [ReportStatus.PROCESSING]: "#0C7092",
  [ReportStatus.WARNING]: "#FFC400",
};

export function getReportStatusColor(status: ReportStatus): string {
  return COLOR[status];
}

export function getRequestStatusColor(request: ReportRequest): string {
  let _status = ReportStatus.DONE;
  if (request.reports_count == 0) {
    _status = ReportStatus.PROCESSING;
  } else if (request.reports_error == request.reports_count) {
    _status = ReportStatus.ERROR;
  } else if (
    request.reports_error > 0 &&
    request.reports_error != request.reports_count
  ) {
    _status = ReportStatus.WARNING;
  } else if (request.reports_processing > 0) {
    _status = ReportStatus.PROCESSING;
  } else if (request.reports_pending > 0) {
    _status = ReportStatus.PENDING;
  }
  return COLOR[_status];
}

export function getInputStatus(data: {
  reportType: ReportType;
  fieldName: string;
  objectWithField: Record<string, any>;
  isSubmitted: boolean;
}): "" | "error" | "warning" {
  if (!data.isSubmitted) return "";
  const metadata = REPORT_CARD_METADATA[data.reportType];

  let isMandatory = false;
  if (metadata?.requiredFields) {
    isMandatory = metadata.requiredFields?.includes(data.fieldName);
  }

  const dataField = data.objectWithField ?? {};

  if (!dataField[data.fieldName] && isMandatory) {
    return "error";
  }

  return "";
}
