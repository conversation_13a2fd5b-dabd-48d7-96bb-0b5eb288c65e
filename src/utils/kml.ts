import { kml } from "@tmcw/togeojson";
import { Feature } from "ol";
import {
  GeoJSONFeatureCollection,
  GeoJSONGeometryCollection,
  GeoJSONPolygon,
} from "ol/format/GeoJSON";
import KML from "ol/format/KML";
import { geojsonToFeatures } from "./geo";

interface KmlFileLoadResult {
  geojson: GeoJSONFeatureCollection;
  features: Feature[];
  label: string;
}

export function loadKmlFiles(fileList: FileList): Promise<KmlFileLoadResult[]> {
  const files = Array.from(fileList);
  let loadEvents: Promise<KmlFileLoadResult>[] = [];

  for (const file of files) {
    const reader = new FileReader();
    reader.readAsText(file);
    loadEvents.push(
      new Promise((resolve, error) => {
        reader.onload = () => {
          try {
            const parser = new DOMParser();
            // Remove atributo da tag do kml que causa erros durante o parse:
            const fileStr = (reader.result as string).replace(
              /xsi:schemaLocation=".*"/g,
              ""
            );
            const parsedFile = parser.parseFromString(fileStr, "text/xml");
            const geojson = kml(parsedFile);
            const features = geojsonToFeatures(geojson as any);
            const label = file.name.replace(/\.[^/.]+$/, "");

            for (const f of features) {
              f.set("color", "#1677FF");
              f.set("label", label);
            }

            resolve({ geojson, features, label });
          } catch (err) {
            error(err);
          }
        };
      })
    );
  }
  return Promise.all(loadEvents);
}

export function featureToKml(features: Feature[]): string {
  const format = new KML({
    extractStyles: true,
  });
  return format.writeFeatures(features, {
    dataProjection: "EPSG:4326",
    featureProjection: "EPSG:3857",
  });
}

export function convertKMLToJSON(
  file: string | ArrayBuffer
): GeoJSONGeometryCollection | GeoJSONPolygon {
  const newFile = file.toString();
  const parsedFile = new DOMParser().parseFromString(newFile, "text/xml");
  const result = kml(parsedFile);
  return {
    type: "MultiPolygon" as any,
    coordinates: result.features.map(
      (feature: any) => feature.geometry.coordinates
    ),
  };
}
