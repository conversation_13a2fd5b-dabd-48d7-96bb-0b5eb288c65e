export function formatDocument(document: string, documentType: string): string {
  const d = document.replace(/[^\d]/g, "");
  if (documentType === "ru" || documentType === "ci") {
    return document;
  }
  if (d.length === 11)
    return d.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
  return d
    .replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g, "$1.$2.$3/$4-$5")
    .substring(0, 18);
}

export function getDocumentType(document: string): "CPF" | "CNPJ" {
  const doc = document.replace(/[^\d]/g, "");
  if (doc.length == 11) return "CPF";
  return "CNPJ";
}

export function isCPFValid(cpf: string): boolean {
  const document = cpf.replace(/[^\d]/g, "");
  if (document.length != 11) return false;
  let numbers = document.substring(0, 9);
  numbers += CPFVerifierDigit(numbers);
  numbers += CPFVerifierDigit(numbers);
  return numbers.substring(-2) === document.substring(-2);
}

export function isCNPJValid(cnpj: string): boolean {
  const document = cnpj.replace(/[^\d]/g, "");
  let numbers = document.substring(0, 12);
  numbers += CNPJVerifierDigit(numbers);
  numbers += CNPJVerifierDigit(numbers);
  return numbers.substring(-2) === document.substring(-2);
}

export function isDocumentValid(document: string): boolean {
  return isCPFValid(document) || isCNPJValid(document);
}

function CPFVerifierDigit(digits: string): number {
  const numbers = digits.split("").map((number) => parseInt(number, 10));
  const length = numbers.length + 1;
  const multiplied = numbers.map((n, idx) => n * (length - idx));
  const mod = multiplied.reduce((buffer, number) => buffer + number) % 11;
  return mod < 2 ? 0 : 11 - mod;
}

function CNPJVerifierDigit(digits: string): number {
  let index = 2;
  const reverse = digits
    .split("")
    .reduce((buffer, number) => [parseInt(number, 10)].concat(buffer), []);
  const sum = reverse.reduce((buffer, number) => {
    buffer += number * index;
    index = index === 9 ? 2 : index + 1;
    return buffer;
  }, 0);
  const mod = sum % 11;
  return mod < 2 ? 0 : 11 - mod;
}
