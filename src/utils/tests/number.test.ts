import { formatNumber, toHectares } from "../number";

describe("formatNumber", () => {
  it("should format number with specified range", () => {
    expect(formatNumber(1234.567, 2)).toBe("1.234,57");
    expect(formatNumber(1234, 2)).toBe("1.234,00");
  });

  it("should format string number with specified range", () => {
    expect(formatNumber("1234.567", 2)).toBe("1.234,57");
    expect(formatNumber("1234", 2)).toBe("1.234,00");
  });

  it("should return 0.00 when value is not a number", () => {
    expect(formatNumber("abc", 2)).toBe("0,00");
    expect(formatNumber(NaN, 2)).toBe("0,00");
  });
});

describe("toHectares", () => {
  it("should convert square meters to hectares", () => {
    expect(toHectares(10000)).toBe(1);
    expect(toHectares(5000)).toBe(0.5);
  });

  it("should return 0 for 0 square meters", () => {
    expect(toHectares(0)).toBe(0);
  });
});
