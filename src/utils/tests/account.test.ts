import { hasResource } from "../account";
import { Resource } from "../../constants/resources";
import { useAccountDetails } from "../../hooks/use-account-details";

jest.mock("../../hooks/use-account-details", () => {
  return {
    useAccountDetails: jest.fn(() => ({
      handleGetDetailsAccount: jest.fn(),
      account: {
        resources: ["valuation-report-signed"],
      },
      hasResource: jest.fn(),
    })),
  };
});

describe("Utils [Account]", () => {
  it("should validate if has resource", () => {
    expect(hasResource(Resource.ValuationReportSigned)).toBeTruthy();
  });

  it("should validate if has any resource", () => {
    (useAccountDetails as jest.Mock).mockReturnValue(() => ({
      account: undefined,
    }));
    expect(hasResource(Resource.ValuationReportSigned)).toBeFalsy();
  });
});
