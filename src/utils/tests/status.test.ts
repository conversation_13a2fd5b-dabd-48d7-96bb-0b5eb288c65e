import {
  getInputStatus,
  getReportStatusColor,
  getRequestStatusColor,
} from "../status";
import { ReportStatus } from "../../types/report-status";
import { ReportRequest } from "../../types/report-request";
import { ReportType } from "../../types/report-type";

describe("getReportStatusColor", () => {
  it("should return the correct color for each status", () => {
    expect(getReportStatusColor(ReportStatus.ERROR)).toBe("#A8001F");
    expect(getReportStatusColor(ReportStatus.DONE)).toBe("#00B277");
    expect(getReportStatusColor(ReportStatus.PENDING)).toBe("#C1C7D0");
    expect(getReportStatusColor(ReportStatus.PROCESSING)).toBe("#0C7092");
    expect(getReportStatusColor(ReportStatus.WARNING)).toBe("#FFC400");
  });
});

describe("getRequestStatusColor", () => {
  const request: ReportRequest = {
    id: 1,
    key: "key1",
    user: {
      email: "user-email",
      name: "user-name",
    },
    tags_names: [],
    reports_count: 1,
    reports_error: 0,
    reports_processing: 0,
    reports_pending: 0,
    created: "2020-05-05 00:00:00" as unknown as Date,
    modified: "2020-05-05 00:00:00" as unknown as Date,
    reports_done: 0,
    reports_types: [],
    reports_waiting: 0,
    reports_warning: 0,
    exports: [],
  };
  it("should return the correct color when reports_count is 0", () => {
    expect(getRequestStatusColor({ ...request, reports_count: 0 })).toBe(
      "#0C7092"
    );
  });

  it("should return the correct color when report error exists", () => {
    expect(
      getRequestStatusColor({ ...request, reports_count: 2, reports_error: 2 })
    ).toBe("#A8001F");
  });

  it("should return the correct color when report warning exists", () => {
    expect(
      getRequestStatusColor({
        ...request,
        reports_count: 2,
        reports_error: 1,
      })
    ).toBe("#FFC400");
  });

  it("should return the correct color when there are reports processing", () => {
    expect(
      getRequestStatusColor({
        ...request,
        reports_count: 2,
        reports_pending: 0,
        reports_done: 1,
        reports_processing: 1,
      })
    ).toBe("#0C7092");
  });

  it("should return the correct color when there are reports pending", () => {
    expect(
      getRequestStatusColor({
        ...request,
        reports_count: 2,
        reports_pending: 1,
        reports_done: 1,
      })
    ).toBe("#C1C7D0");
  });

  it("should return the correct color when all reports are done", () => {
    expect(
      getRequestStatusColor({
        ...request,
        reports_count: 2,
        reports_done: 2,
      })
    ).toBe("#00B277");
  });
});

describe("getInputStatus", () => {
  it("should return an empty string if not submitted", () => {
    expect(
      getInputStatus({
        reportType: ReportType.INSPECTION,
        fieldName: "cropGrop",
        objectWithField: {},
        isSubmitted: false,
      })
    ).toBe("");
  });

  it("should handle with undefined instead object with field", () => {
    expect(
      getInputStatus({
        reportType: ReportType.INSPECTION,
        fieldName: "totalArea",
        objectWithField: undefined as any,
        isSubmitted: true,
      })
    ).toBe("error");
  });

  it("should return 'error' if the field is mandatory and not provided", () => {
    expect(
      getInputStatus({
        reportType: ReportType.INSPECTION,
        fieldName: "municipality",
        objectWithField: {},
        isSubmitted: true,
      })
    ).toBe("error");
  });

  it("should return an empty string if the field is provided", () => {
    expect(
      getInputStatus({
        reportType: ReportType.INSPECTION,
        fieldName: "municipality",
        objectWithField: { municipality: "value" },
        isSubmitted: true,
      })
    ).toBe("");
  });

  it("should return an empty string if the field is not mandatory", () => {
    expect(
      getInputStatus({
        reportType: ReportType.INSPECTION,
        fieldName: "cropGroup",
        objectWithField: {},
        isSubmitted: true,
      })
    ).toBe("");
  });
});
