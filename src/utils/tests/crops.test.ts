import { Crop, getCropYears, getSoilByCropYear, SoilType } from "../crops";

const globalDate = new Date("2018-05-05 00:00:00");

jest.useFakeTimers().setSystemTime(globalDate);

describe("Utils [crops]", () => {
  it("should get crop years", () => {
    const expected = [
      {
        label: "1ª Safra 2017/2018",
        value: "2017/2018",
      },
      {
        label: "2ª Safra 2018/2018",
        value: "2018/2018",
      },
      {
        label: "1ª Safra 2018/2019",
        value: "2018/2019",
      },
      {
        label: "2ª Safra 2019/2019",
        value: "2019/2019",
      },
      {
        label: "1ª Safra 2019/2020",
        value: "2019/2020",
      },
      {
        label: "2ª Safra 2020/2020",
        value: "2020/2020",
      },
      {
        label: "1ª Safra 2020/2021",
        value: "2020/2021",
      },
      {
        label: "2ª Safra 2021/2021",
        value: "2021/2021",
      },
      {
        label: "1ª Safra 2021/2022",
        value: "2021/2022",
      },
      {
        label: "2ª Safra 2022/2022",
        value: "2022/2022",
      },
      {
        label: "1ª Safra 2022/2023",
        value: "2022/2023",
      },
      {
        label: "2ª Safra 2023/2023",
        value: "2023/2023",
      },
      {
        label: "1ª Safra 2023/2024",
        value: "2023/2024",
      },
      {
        label: "2ª Safra 2024/2024",
        value: "2024/2024",
      },
      {
        label: "1ª Safra 2024/2025",
        value: "2024/2025",
      },
      {
        label: "2ª Safra 2025/2025",
        value: "2025/2025",
      },
    ];
    const result = getCropYears();

    expect(result).toEqual(expected);
  });

  it("should get soil by crop year", () => {
    const result = getSoilByCropYear(Crop.SOYBEAN, "2023/2024");

    expect(result).toEqual([
      SoilType.AD1,
      SoilType.AD2,
      SoilType.AD3,
      SoilType.AD4,
      SoilType.AD5,
      SoilType.AD6,
    ]);
  });

  it.each([Crop.SOYBEAN, Crop.WHEAT])(
    "should get soil by crop year different from 2023/2024",
    (cropType) => {
      const result = getSoilByCropYear(cropType, "2020/2022");
      const expected = [SoilType.SANDY, SoilType.AVERAGE, SoilType.CLAYISH];

      expect(result).toEqual(expected);
    }
  );

  it("should return empty if get soil by crop year does not have params", () => {
    const result = getSoilByCropYear();
    const expected = [];

    expect(result).toEqual(expected);
  });
});
