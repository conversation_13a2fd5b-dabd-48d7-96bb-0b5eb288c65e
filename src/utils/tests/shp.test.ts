import { convertSHPToJSON } from "../shp";
import shp from "shpjs";

jest.mock("shpjs");

describe("convertSHPToJSON", () => {
  it("should convert SHP file to GeoJSON", async () => {
    const mockFile = "mockFile";
    const mockGeoJSON = { type: "Polygon", coordinates: [] };
    (shp.parseShp as jest.Mock).mockResolvedValue(mockGeoJSON);

    const result = await convertSHPToJSON(mockFile);

    expect(result).toEqual(mockGeoJSON);
    expect(shp.parseShp).toHaveBeenCalledWith(mockFile);
  });

  it("should throw an error if conversion fails", async () => {
    const mockFile = "mockFile";
    (shp.parseShp as jest.Mock).mockRejectedValue(
      new Error("Conversion failed")
    );

    await expect(convertSHPToJSON(mockFile)).rejects.toThrow(
      "Erro ao converter arquivo verifique e tente novamente."
    );
  });
});
