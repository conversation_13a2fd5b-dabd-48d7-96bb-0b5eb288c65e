import { removeSpecialCharacters } from "../text";

describe("removeSpecialCharacters", () => {
  it("should remove accents and special characters", () => {
    const input = "Café com açúcar!";
    const expectedOutput = "cafecomacucar";
    expect(removeSpecial<PERSON>haracters(input)).toBe(expectedOutput);
  });

  it("should handle empty strings", () => {
    const input = "";
    const expectedOutput = "";
    expect(removeSpecialCharacters(input)).toBe(expectedOutput);
  });

  it("should handle strings with only special characters", () => {
    const input = "!@#$%^&*()";
    const expectedOutput = "";
    expect(removeSpecialCharacters(input)).toBe(expectedOutput);
  });

  it("should handle strings with numbers", () => {
    const input = "12345";
    const expectedOutput = "12345";
    expect(removeSpecialCharacters(input)).toBe(expectedOutput);
  });

  it("should handle strings with mixed characters", () => {
    const input = "Hello, World! 123";
    const expectedOutput = "helloworld123";
    expect(removeSpecial<PERSON>haracters(input)).toBe(expectedOutput);
  });
});
