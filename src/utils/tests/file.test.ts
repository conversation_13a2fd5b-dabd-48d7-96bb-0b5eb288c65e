import { fileToBase64, base64ToFile } from "../file";

global.fetch = jest.fn();

describe("base64ToFile", () => {
  it("should convert base64 string to a File object", async () => {
    const base64 = "data:text/plain;base64,SGVsbG8sIFdvcmxkIQ=="; // "Hello, World!" in base64
    const fileName = "hello.txt";
    const type = "text/plain";

    (fetch as jest.Mock).mockResolvedValueOnce({
      blob: () => new Blob(["Hello, World!"], { type }),
    });

    const file = await base64ToFile(base64, fileName, type);

    expect(file).toBeInstanceOf(File);
    expect(file.name).toBe(fileName);
    expect(file.type).toBe(type);
  });

  it("should throw an error if the base64 string is invalid", async () => {
    const invalidBase64 = "invalid_base64";
    const fileName = "invalid.txt";
    const type = "text/plain";

    await expect(base64ToFile(invalidBase64, fileName, type)).rejects.toThrow();
  });

  describe("fileToBase64", () => {
    it("should convert a File object to a base64 string", async () => {
      const content = "Hello, World!";
      const blob = new Blob([content], { type: "text/plain" });
      const file = new File([blob], "hello.txt", { type: "text/plain" });

      const base64 = await fileToBase64(file);

      expect(base64).toBe("data:text/plain;base64,SGVsbG8sIFdvcmxkIQ==");
    });

    it("should throw an error if the file cannot be read", async () => {
      await expect(fileToBase64({} as File)).rejects.toThrow();
    });
  });
});
