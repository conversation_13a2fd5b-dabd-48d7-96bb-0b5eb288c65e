import {
  isSigef,
  formatSnci,
  isSnci,
  isIncra,
  getIncraType,
  formatIncra,
} from "../incra";

describe("isSigef", () => {
  test("should return true for valid SIGEF code with dashes", () => {
    const code = "12345678-1234-1234-1234-123456789012";
    expect(isSigef(code)).toBe(true);
  });

  test("should return true for valid SIGEF code without dashes", () => {
    const code = "12345678123412341234123456789012";
    expect(isSigef(code)).toBe(true);
  });

  test("should return false for SIGEF code with less than 32 characters", () => {
    const code = "12345678-1234-1234-1234-1234567890";
    expect(isSigef(code)).toBe(false);
  });

  test("should return false for empty string", () => {
    const code = "";
    expect(isSigef(code)).toBe(false);
  });

  test("should return false for null input", () => {
    const code = null;
    expect(isSigef("")).toBe(false);
  });

  test("should return false for undefined input", () => {
    const code = undefined;
    expect(isSigef("")).toBe(false);
  });
});

describe("formatSnci", () => {
  test("should format SNCI code correctly", () => {
    const code = "12345678901234";
    expect(formatSnci(code)).toBe("123456789012-34");
  });

  test("should trim and format SNCI code correctly", () => {
    const code = "  12345678901234  ";
    expect(formatSnci(code)).toBe("123456789012-34");
  });

  test("should handle SNCI code with existing dash", () => {
    const code = "123456789012-34";
    expect(formatSnci(code)).toBe("123456789012-34");
  });

  test("should handle SNCI code with less than 14 characters", () => {
    const code = "123456789012";
    expect(formatSnci(code)).toBe("123456789012");
  });

  test("should return empty string for empty input", () => {
    const code = "";
    expect(formatSnci(code)).toBe("");
  });

  test("should handle null input gracefully", () => {
    const code = null;
    expect(formatSnci("")).toBe("");
  });

  test("should handle undefined input gracefully", () => {
    const code = undefined;
    expect(formatSnci("")).toBe("");
  });
});

describe("isSnci", () => {
  test("should return true for valid SNCI code", () => {
    const code = "123456789012-34";
    expect(isSnci(code)).toBe(true);
  });

  test("should return false for SNCI code with less than 14 characters", () => {
    const code = "123456789012";
    expect(isSnci(code)).toBe(false);
  });

  test("should return false for empty string", () => {
    const code = "";
    expect(isSnci(code)).toBe(false);
  });

  test("should return false for null input", () => {
    const code = null;
    expect(isSnci("")).toBe(false);
  });

  test("should return false for undefined input", () => {
    const code = undefined;
    expect(isSnci("")).toBe(false);
  });
});

describe("isIncra", () => {
  test("should return true for valid SIGEF code", () => {
    const code = "12345678-1234-1234-1234-123456789012";
    expect(isIncra(code)).toBe(true);
  });

  test("should return true for valid SNCI code", () => {
    const code = "123456789012-34";
    expect(isIncra(code)).toBe(true);
  });

  test("should return false for invalid code", () => {
    const code = "1234567890";
    expect(isIncra(code)).toBe(false);
  });

  test("should return false for empty string", () => {
    const code = "";
    expect(isIncra(code)).toBe(false);
  });

  test("should return false for null input", () => {
    const code = null;
    expect(isIncra("")).toBe(false);
  });

  test("should return false for undefined input", () => {
    const code = undefined;
    expect(isIncra("")).toBe(false);
  });
});

describe("getIncraType", () => {
  test("should return 'SIGEF' for valid SIGEF code", () => {
    const code = "12345678-1234-1234-1234-123456789012";
    expect(getIncraType(code)).toBe("SIGEF");
  });

  test("should return 'SNCI' for valid SNCI code", () => {
    const code = "123456789012-34";
    expect(getIncraType(code)).toBe("SNCI");
  });

  test("should return null for invalid code", () => {
    const code = "1234567890";
    expect(getIncraType(code)).toBe(null);
  });

  test("should return null for empty string", () => {
    const code = "";
    expect(getIncraType(code)).toBe(null);
  });

  test("should return null for null input", () => {
    const code = null;
    expect(getIncraType("")).toBe(null);
  });

  test("should return null for undefined input", () => {
    const code = undefined;
    expect(getIncraType("")).toBe(null);
  });
});

describe("formatIncra", () => {
  test("should format SIGEF code correctly", () => {
    const code = "12345678123412341234123456789012";
    expect(formatIncra(code)).toBe("12345678-1234-1234-1234-123456789012");
  });

  test("should format SNCI code correctly", () => {
    const code = "12345678901234";
    expect(formatIncra(code)).toBe("123456789012-34");
  });

  test("should return empty string for empty input", () => {
    const code = "";
    expect(formatIncra(code)).toBe("");
  });

  test("should handle null input gracefully", () => {
    const code = null;
    expect(formatIncra("")).toBe("");
  });

  test("should handle undefined input gracefully", () => {
    const code = undefined;
    expect(formatIncra("")).toBe("");
  });
});
