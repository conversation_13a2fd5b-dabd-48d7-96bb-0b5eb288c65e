import { Map } from "ol";
import Feature from "ol/Feature";
import { GeoJSONGeometry } from "ol/format/GeoJSON.js";
import { Geometry } from "ol/geom";
import VectorSource from "ol/source/Vector";
import {
  geojsonToFeature,
  geojsonToFeatures,
  geojsonToGeometry,
  geometryToGeojson,
  getExtent,
  fitMap,
  getArea,
} from "../geo";

describe("geo utility functions", () => {
  const sampleGeoJSON: GeoJSONGeometry = {
    type: "Polygon",
    coordinates: [
      [
        [0, 0],
        [0, 1],
        [1, 1],
        [1, 0],
        [0, 0],
      ],
    ],
  };

  test("geojsonToFeature should convert GeoJSON to Feature", () => {
    const feature = geojsonToFeature(sampleGeoJSON);
    expect(feature).toBeInstanceOf(Feature);
  });

  test("geojsonToFeatures should convert GeoJSON to array of Features", () => {
    const features = geojsonToFeatures(sampleGeoJSON);
    expect(features).toBeInstanceOf(Array);
    expect(features[0]).toBeInstanceOf(Feature);
  });

  test("geojsonToGeometry should convert GeoJSON to Geometry", () => {
    const geometry = geojsonToGeometry(sampleGeoJSON);
    expect(geometry).toBeInstanceOf(Geometry);
  });

  test("geometryToGeojson should convert Geometry to GeoJSON", () => {
    const geometry = geojsonToGeometry(sampleGeoJSON);
    const geojson = geometryToGeojson(geometry);
    expect(geojson).toEqual(sampleGeoJSON);
  });

  test("getExtent should return the extent of features", () => {
    const features = geojsonToFeatures(sampleGeoJSON);
    const extent = getExtent(features);
    expect(extent).toEqual([0, 0, 1, 1]);
  });

  test("fitMap should fit the map view to the source extent", () => {
    const map = new Map({});
    const source = new VectorSource({
      features: geojsonToFeatures(sampleGeoJSON),
    });
    const fitSpy = jest.spyOn(map.getView(), "fit");
    fitMap(map, source);
    expect(fitSpy).toHaveBeenCalledWith(source.getExtent(), {
      padding: [100, 100],
    });
  });

  test("getArea should return the area of the GeoJSON geometry", () => {
    const area = getArea(sampleGeoJSON);
    expect(area).toBeGreaterThan(0);
  });

  test("geojsonToFeature should convert GeoJSON to Feature", () => {
    const feature = geojsonToFeature(sampleGeoJSON);
    expect(feature).toBeInstanceOf(Feature);
    expect(feature.getGeometry()?.getType()).toBe("Polygon");
  });

  test("geojsonToFeature should handle invalid GeoJSON gracefully", () => {
    const invalidGeoJSON = {
      type: "InvalidType",
      coordinates: [
        [
          [0, 0],
          [0, 1],
          [1, 1],
          [1, 0],
          [0, 0],
        ],
      ],
    } as any as GeoJSONGeometry;
    expect(() => geojsonToFeature(invalidGeoJSON)).toThrow();
  });
});
