import { shortDate, hourMin } from "../date";

describe('Date Utils', () => {
  describe('shortDate', () => {
    it('should format a Date object to DD/MM/YYYY', () => {
      const date = new Date(2023, 0, 15);
      expect(shortDate(date)).toBe('15/01/2023');
    });

    it('should format a date string to DD/MM/YYYY', () => {
      expect(shortDate('2023-01-15')).toMatch(/\d{2}\/\d{2}\/\d{4}/);
      expect(shortDate('2023-01-15T12:30:00')).toMatch(/\d{2}\/\d{2}\/\d{4}/);
    });
  });

  describe('hourMin', () => {
    it('should format a Date object to HH[h]mm', () => {
      const date = new Date(2023, 0, 15, 14, 30);
      expect(hourMin(date)).toBe('14h30');
    });

    it('should format a date string to HH[h]mm', () => {
      expect(hourMin('2023-01-15T14:30:00')).toBe('14h30');
    });

    it('should handle midnight correctly', () => {
      expect(hourMin('2023-01-15T00:00:00')).toBe('00h00');
    });
  });
});
