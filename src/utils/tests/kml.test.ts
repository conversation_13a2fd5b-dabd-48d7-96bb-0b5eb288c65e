import { loadKmlFiles, featureToKml, convertKMLToJSON } from "../kml";
import { Feature } from "ol";
import { GeoJSONFeatureCollection } from "ol/format/GeoJSON";

describe("KML Utils", () => {
  describe("loadKmlFiles", () => {
    it("should load KML files and return geojson, features, and label", async () => {
      const file = new File(["<kml><Document></Document></kml>"], "test.kml", {
        type: "application/vnd.google-earth.kml+xml",
      });
      const fileList = {
        0: file,
        length: 1,
        item: (index: number) => file,
        [Symbol.iterator]: function* () {
          yield file;
        },
      } as FileList;

      const result = await loadKmlFiles(fileList);
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty("geojson");
      expect(result[0]).toHaveProperty("features");
      expect(result[0]).toHaveProperty("label", "test");
    });
  });

  describe("featureToKml", () => {
    it("should convert features to KML string", () => {
      const feature = new Feature();
      const kmlString = featureToKml([feature]);
      expect(kmlString).toContain("<kml");
      expect(kmlString).toContain("</kml>");
    });
  });

  describe("convertKMLToJSON", () => {
    it("should convert KML string to GeoJSON", () => {
      const kmlString = "<kml><Document></Document></kml>";
      const geojson = convertKMLToJSON(
        kmlString
      ) as unknown as GeoJSONFeatureCollection;
      expect(geojson).toHaveProperty("type", "MultiPolygon");
      expect(geojson).toHaveProperty("coordinates");
    });
  });
});
