import { readCSV } from "../csv";
import <PERSON> from "papaparse";

describe("readCSV", () => {
  let file: File;

  beforeEach(() => {
    const csvContent = "name;age\nJohn <PERSON>;30\n<PERSON><PERSON>;25";
    file = new File([csvContent], "test.csv", { type: "text/csv" });
  });

  it("should parse CSV file correctly", async () => {
    const result = await readCSV<{ name: string; age: string }>(file);
    expect(result).toEqual([
      { name: "<PERSON>", age: "30" },
      { name: "<PERSON>", age: "25" },
    ]);
  });

  it("should throw an error if file is not provided", async () => {
    await expect(readCSV(null as any)).rejects.toThrow("Invalid file");
  });

  it("should throw an error if file type is not text/csv", async () => {
    const invalidFile = new File(["content"], "test.txt", {
      type: "text/plain",
    });
    await expect(readCSV(invalidFile)).rejects.toThrow("Invalid file");
  });

  it("should handle empty CSV file", async () => {
    const emptyFile = new File([""], "empty.csv", { type: "text/csv" });
    const result = await readCSV(emptyFile);
    expect(result).toEqual([]);
  });

  it("should apply transformHeader if provided", async () => {
    const transformHeader = (header: string) => header.toUpperCase();
    const result = await readCSV<{ NAME: string; AGE: string }>(file, transformHeader);
    expect(result).toEqual([
      { NAME: "John Doe", AGE: "30" },
      { NAME: "Jane Doe", AGE: "25" },
    ]);
  });
});
