import { parseObject } from '../parse-object';

describe('parseObject', () => {
  it('should remove null and undefined values from an object', () => {
    const input = {
      name: '<PERSON>',
      age: 30,
      email: null,
      phone: undefined,
      address: 'New York'
    };

    const expected = {
      name: '<PERSON>',
      age: 30,
      address: 'New York'
    };

    const result = parseObject(input);
    expect(result).toEqual(expected);
  });

  it('should return an empty object when all values are null or undefined', () => {
    const input = {
      email: null,
      phone: undefined
    };

    const result = parseObject(input);
    expect(result).toEqual({});
  });

  it('should handle an empty object', () => {
    const input = {};
    const result = parseObject(input);
    expect(result).toEqual({});
  });
});
