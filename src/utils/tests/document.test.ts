import {
  formatDocument,
  getDocumentType,
  isCNPJValid,
  isCPFValid,
  isDocumentValid,
} from "../document";

describe("Utils [document]", () => {
  it("should format document successfully if is CPF", () => {
    const expected = "058.268.591-56";
    const document = formatDocument("05826859156", "CPF");

    expect(document).toEqual(expected);
  });

  it("should format document successfully if is CNPJ", () => {
    const expected = "84.643.712/0001-19";
    const document = formatDocument("84643712000119", "CNPJ");

    expect(document).toEqual(expected);
  });

  it.each(["ci", "ru"])(
    "should return document if type is $d",
    (documentType) => {
      const expected = "123456789";
      const document = formatDocument(expected, documentType);

      expect(document).toEqual(expected);
    }
  );

  it.each([
    { document: "05826859156", type: "CPF" },
    { document: "84643712000119", type: "CNPJ" },
  ])("should be able to get the document type", ({ type, document }) => {
    const result = getDocumentType(document);

    expect(result).toEqual(type);
  });

  it.each([
    {
      cpf: "1105826859156",
      isValid: false,
    },
    {
      cpf: "05826859156",
      isValid: true,
    },
    {
      cpf: "0582a6859156-8",
      isValid: false,
    },
  ])("should validate cpf", ({ cpf, isValid }) => {
    const result = isCPFValid(cpf);

    expect(result).toEqual(isValid);
  });

  it.each([
    {
      cnpj: "1105826859156",
      isValid: false,
    },
    {
      cnpj: "84643712000119",
      isValid: true,
    },
    {
      cnpj: "0582a6859156-8",
      isValid: false,
    },
  ])("should validate cnpj", ({ cnpj, isValid }) => {
    const result = isCNPJValid(cnpj);

    expect(result).toEqual(isValid);
  });

  it.each([{ document: "84643712000119" }, { document: "05826859156" }])(
    "should validate document even if it is a CPF or CNPJ",
    ({ document }) => {
      const isValid = isDocumentValid(document);
      expect(isValid).toBe(true);
    }
  );

  it("should validate cpj verifier digit", () => {
    const valid = isCPFValid("00000000000");
    expect(valid).toBe(true);
  });

  it("should validate cnpj verifier digit", () => {
    const valid = isCNPJValid("00000000000000");
    expect(valid).toBe(true);
  });
});
