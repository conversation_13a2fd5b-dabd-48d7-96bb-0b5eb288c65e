import { isCarValid, maskCAR, removeMaskCAR } from "../car";

describe('CAR Utils', () => {
  describe('isCarValid', () => {
    it('should return true for valid CAR codes', () => {
      expect(isCarValid("**********-8DD0299577184D44BC3CB6CAB7EF8CEC")).toBe(true);
      expect(isCarValid("**********-ABCDEFGHIJKLMNOPQRSTUVWXYZ123456")).toBe(true);
      expect(isCarValid("MG-1234567-12345678901234567890123456789012")).toBe(true);
    });

    it('should return false for invalid CAR codes', () => {
      expect(isCarValid("XA-1502764-8DD0299184D44BC3CB6CAB7EF8CEC")).toBe(false);

      expect(isCarValid("PA-150276-8DD0299577184D44BC3CB6CAB7EF8CEC")).toBe(false);

      expect(isCarValid("**********-8DD0299577184D44BC3CB6CAB7EF8CE")).toBe(false);

      expect(isCarValid("")).toBe(false);
    });
  });

  describe('maskCAR', () => {
    it('should format the CAR code correctly', () => {
      expect(maskCAR("*********8DD0299577184D44BC3CB6CAB7EF8CEC")).toBe("**********-8DD0299577184D44BC3CB6CAB7EF8CEC");
    });

    it('should handle input with special characters', () => {
      expect(maskCAR("**********-8DD0299577184D44BC3CB6CAB7EF8CEC")).toBe("**********-8DD0299577184D44BC3CB6CAB7EF8CEC");
    });

    it('should handle input with lowercase letters', () => {
      expect(maskCAR("pa15027648dd0299577184d44bc3cb6cab7ef8cec")).toBe("**********-8DD0299577184D44BC3CB6CAB7EF8CEC");
    });

    it('should handle partial CAR numbers', () => {
      expect(maskCAR("PA")).toBe("PA");
      expect(maskCAR("*********")).toBe("**********");
    });

    it('should handle empty input', () => {
      expect(maskCAR("")).toBe("");
    });
  });

  describe('removeMaskCAR', () => {
    it('should remove all hyphens from a CAR number', () => {
      expect(removeMaskCAR("**********-8DD0299577184D44BC3CB6CAB7EF8CEC")).toBe("*********8DD0299577184D44BC3CB6CAB7EF8CEC");
      expect(removeMaskCAR("**********-ABCDEFGHIJKLMNOPQRSTUVWXYZ123456")).toBe("SP1234567ABCDEFGHIJKLMNOPQRSTUVWXYZ123456");
    });

    it('should handle CAR numbers without hyphens', () => {
      expect(removeMaskCAR("*********8DD0299577184D44BC3CB6CAB7EF8CEC")).toBe("*********8DD0299577184D44BC3CB6CAB7EF8CEC");
    });

    it('should handle empty strings', () => {
      expect(removeMaskCAR("")).toBe("");
    });
  });
});
