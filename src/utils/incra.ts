export function formatSigef(code: string): string {
  return code
    .trim()
    .replace(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/g, "$1-$2-$3-$4-$5")
    .substring(0, 36);
}

export function formatSnci(code: string): string {
  return code
    .trim()
    .replace(/(\w{12})(\w{2})/g, "$1-$2")
    .substring(0, 36);
}

export function isSigef(code: string): boolean {
  return code.replace(/-/g, "").length >= 32;
}

export function isSnci(code: string): boolean {
  return code.replace(/[,.-]/g, "").length == 14;
}

export function isIncra(code: string): boolean {
  return isSigef(code) || isSnci(code);
}

export function getIncraType(code: string): string {
  return isSigef(code) ? "SIGEF" : isSnci(code) ? "SNCI" : null;
}

export function formatIncra(code: string): string {
  return isSigef(code) ? formatSigef(code) : formatSnci(code);
}
