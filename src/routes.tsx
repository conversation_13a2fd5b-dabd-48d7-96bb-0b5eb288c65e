import DocumentTitle from "react-document-title";
import {
  BrowserRouter,
  Navigate,
  Route,
  Routes as RoutesContainer,
} from "react-router-dom";
import App from "./App";
import "./i18n";

export function Routes() {
  return (
    <DocumentTitle title="AgroReport | BrainAg">
      <BrowserRouter basename="/agroreport">
        <RoutesContainer>
          <Route path="*" element={<Navigate to="/" replace />} />
          <Route path="" element={<App />} />
        </RoutesContainer>
      </BrowserRouter>
    </DocumentTitle>
  );
}
