import { AvailableReport } from "@constants";
import { ReportType } from "@types";
import { createContext } from "react";

export const RequestFormContext = createContext<{
  isRequestFormOpen: boolean;
  reportMetadata: AvailableReport | null;
  openRequestForm: (reportType: ReportType) => void;
  closeRequestForm: () => void;
}>({
  isRequestFormOpen: false,
  reportMetadata: null,
  openRequestForm: (initialReportType: ReportType) => {},
  closeRequestForm: () => {},
});
