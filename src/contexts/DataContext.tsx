import { AvailableReport } from "@constants";
import { getAccountInfo, getUser, listBanners, listTags } from "@services";
import { Account, BannerData, ReportRequest, Tag, User } from "@types";
import { Form, FormInstance, notification } from "antd";
import { createContext, useContext, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { listCards } from "../services/cards";
import { useReportRequestsList } from "@/hooks/use-report-requests-list";

export const DataContext = createContext<{
  accountInfo: Account | null;
  banners: BannerData[];
  form: FormInstance | null;
  requests: ReportRequest[];
  count: number;
  reportsCount: number;
  tags: Tag[];
  isLoadingBanners: boolean;
  user: User | null;
  cards: AvailableReport[];
  isLoadingCards: boolean;
  fetchTags: Function;
  applyFilters: Function;
  reloadReports: Function;
  fetchAccountInfo: Function;
}>({
  accountInfo: null,
  banners: [],
  form: null,
  requests: [],
  count: 0,
  reportsCount: 0,
  tags: [],
  isLoadingBanners: false,
  user: null,
  cards: [],
  isLoadingCards: false,
  fetchTags: () => {},
  applyFilters: () => {},
  reloadReports: () => {},
  fetchAccountInfo: () => {},
});

export function DataProvider({ children }) {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const [filters, setFilters] = useState({});
  const [tags, setTags] = useState<Tag[]>([]);
  const [banners, setBanners] = useState<BannerData[]>([]);
  const [cards, setCards] = useState<AvailableReport[]>([]);
  const [isLoadingBanners, setIsLoadingBanners] = useState<boolean>(true);
  const [isLoadingCards, setIsLoadingCards] = useState<boolean>(true);
  const [accountInfo, setAccountInfo] = useState<Account | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const { count, reportsCount, requests, handleListReportsRequests } =
    useReportRequestsList();

  async function fetchListReportRequests(params, notify: boolean = true) {
    handleListReportsRequests(params);
    if (Object.keys(params || {}).length && notify) {
      notification.success({
        message: t("FILTERS_APPLIED"),
      });
    }
  }

  function reloadReports() {
    fetchListReportRequests(filters, false);
  }

  async function fetchTags() {
    const t = await listTags();
    setTags(t);
  }

  async function fetchCards() {
    setIsLoadingCards(true);
    const cards = await listCards();
    setCards(cards);
    setIsLoadingCards(false);
  }

  async function fetchBanners() {
    setIsLoadingBanners(true);
    const { items } = await listBanners();
    setBanners(items);
    setIsLoadingBanners(false);
  }

  async function fetchAccountInfo() {
    try {
      const data = await getAccountInfo();
      setAccountInfo(data);
    } catch {}
  }

  async function fetchUser() {
    try {
      const data = await getUser();
      setUser(data);
    } catch {}
  }
  const applyFilters = (newFilters: Record<string, any>) => {
    setFilters(newFilters);
    handleListReportsRequests(newFilters);
  };

  useEffect(() => {
    handleListReportsRequests(filters);
    fetchTags();
    fetchBanners();
    fetchCards();
    fetchAccountInfo();
    fetchUser();
  }, []);

  return (
    <DataContext.Provider
      value={{
        accountInfo,
        banners,
        form,
        requests,
        count,
        reportsCount,
        tags,
        isLoadingBanners,
        user,
        cards,
        isLoadingCards,
        fetchTags,
        applyFilters,
        reloadReports,
        fetchAccountInfo,
      }}
    >
      {children}
    </DataContext.Provider>
  );
}

export const useData = () => {
  return useContext(DataContext);
};
