import React from "react";
import { render, waitFor } from "@testing-library/react";
import { DataProvider, DataContext } from "./DataContext";
import { useTranslation } from "react-i18next";
import { Form } from "antd";
import { listTags, listBanners, getAccountInfo, getUser } from "@services";
import { listCards } from "../services/cards";
import { useReportRequestsList } from "@/hooks/use-report-requests-list";

jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(),
}));

jest.mock("antd", () => ({
  Form: {
    useForm: jest.fn(),
  },
  notification: {
    success: jest.fn(),
  },
}));

jest.mock("@services", () => ({
  listTags: jest.fn(),
  listBanners: jest.fn(),
  getAccountInfo: jest.fn(),
  getUser: jest.fn(),
}));

jest.mock("../services/cards", () => ({
  listCards: jest.fn(),
}));

jest.mock("@/hooks/use-report-requests-list", () => ({
  useReportRequestsList: jest.fn(),
}));

describe("DataProvider", () => {
  beforeEach(() => {
    (useTranslation as jest.Mock).mockReturnValue({ t: (key: string) => key });
    (Form.useForm as jest.Mock).mockReturnValue([{}]);
    (useReportRequestsList as jest.Mock).mockReturnValue({
      count: 0,
      reportsCount: 0,
      requests: [],
      handleListReportsRequests: jest.fn(),
    });
    (listBanners as jest.Mock).mockReturnValue({ items: [] });
    (listTags as jest.Mock).mockReturnValue([]);
    (listCards as jest.Mock).mockReturnValue([]);
  });

  it("should provide default values", async () => {
    const { getByText } = render(
      <DataProvider>
        <DataContext.Consumer>
          {(value) => (
            <>
              <div>
                {value.accountInfo === null
                  ? "No Account Info"
                  : "Has Account Info"}
              </div>
              <div>
                {value.banners.length === 0 ? "No Banners" : "Has Banners"}
              </div>
              <div>{value.form ? "Has Form" : "No Form"}</div>
              <div>
                {value.requests.length === 0 ? "No Requests" : "Has Requests"}
              </div>
              <div>
                {value.count === 0 ? "Count is 0" : `Count is ${value.count}`}
              </div>
              <div>
                {value.reportsCount === 0
                  ? "Reports Count is 0"
                  : `Reports Count is ${value.reportsCount}`}
              </div>
              <div>{value.tags.length === 0 ? "No Tags" : "Has Tags"}</div>
              <div>
                {value.isLoadingBanners
                  ? "Loading Banners"
                  : "Not Loading Banners"}
              </div>
              <div>{value.user === null ? "No User" : "Has User"}</div>
              <div>{value.cards.length === 0 ? "No Cards" : "Has Cards"}</div>
              <div>
                {value.isLoadingCards ? "Loading Cards" : "Not Loading Cards"}
              </div>
            </>
          )}
        </DataContext.Consumer>
      </DataProvider>
    );

    await waitFor(() => {
      expect(getByText("No Account Info")).toBeInTheDocument();
      expect(getByText("No Banners")).toBeInTheDocument();
      expect(getByText("Has Form")).toBeInTheDocument();
      expect(getByText("No Requests")).toBeInTheDocument();
      expect(getByText("Count is 0")).toBeInTheDocument();
      expect(getByText("Reports Count is 0")).toBeInTheDocument();
      expect(getByText("No Tags")).toBeInTheDocument();
      expect(getByText("Loading Banners")).toBeInTheDocument();
      expect(getByText("No User")).toBeInTheDocument();
      expect(getByText("No Cards")).toBeInTheDocument();
      expect(getByText("Loading Cards")).toBeInTheDocument();
    });
  });

  it("should fetch data on mount", async () => {
    (listTags as jest.Mock).mockResolvedValue([]);
    (listBanners as jest.Mock).mockResolvedValue({ items: [] });
    (listCards as jest.Mock).mockResolvedValue([]);
    (getAccountInfo as jest.Mock).mockResolvedValue(null);
    (getUser as jest.Mock).mockResolvedValue(null);

    render(
      <DataProvider>
        <div>Test</div>
      </DataProvider>
    );

    await waitFor(() => {
      expect(listTags).toHaveBeenCalled();
      expect(listBanners).toHaveBeenCalled();
      expect(listCards).toHaveBeenCalled();
      expect(getAccountInfo).toHaveBeenCalled();
      expect(getUser).toHaveBeenCalled();
    });
  });
});
