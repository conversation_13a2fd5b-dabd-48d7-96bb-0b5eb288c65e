import React from "react";
import { render, screen } from "@testing-library/react";
import {
  AnalyticsDataLayerContext,
  AnalyticsDataLayerProvider,
  useAnalytics,
} from "../AnalyticsDataLayerContext";
import { useData } from "../DataContext";

jest.mock("../DataContext", () => ({
  useData: jest.fn(),
}));

const mockCreateElement = jest.fn();
const mockGetElementById = jest.fn();
const mockAppendChild = jest.fn();
const mockRemoveChild = jest.fn();
const mockDispatchEvent = jest.fn();

const originalCreateElement = document.createElement;
const originalGetElementById = document.getElementById;
const originalAppendChild = document.head.appendChild;
const originalRemoveChild = document.head.removeChild;
const originalDispatchEvent = document.dispatchEvent;

const originalEnv = process.env;

const TestComponent = () => {
  const { sendPageViewEvent } = useAnalytics();

  return (
    <div>
      <button
        data-testid="send-event-button"
        onClick={() => sendPageViewEvent({ pageName: "TestPage" })}
      >
        Send Event
      </button>
    </div>
  );
};

describe("AnalyticsDataLayerContext", () => {
  beforeEach(() => {
    document.createElement = mockCreateElement;
    document.getElementById = mockGetElementById;
    document.head.appendChild = mockAppendChild;
    document.head.removeChild = mockRemoveChild;
    document.dispatchEvent = mockDispatchEvent;

    mockCreateElement.mockImplementation((tagName) => {
      if (tagName === "script") {
        return {
          setAttribute: jest.fn(),
          id: "",
          src: "",
        };
      }
      return originalCreateElement.call(document, tagName);
    });

    mockGetElementById.mockImplementation((id) => {
      if (id === "agroreport_data_layer") {
        return {
          id: "agroreport_data_layer",
        };
      }
      return null;
    });

    Object.defineProperty(window, "location", {
      value: { href: "http://localhost/" },
      writable: true,
    });

    process.env = { ...originalEnv, DATA_LAYER_URL: "https://example.com/datalayer.js" };

    jest.clearAllMocks();
  });

  afterEach(() => {
    document.createElement = originalCreateElement;
    document.getElementById = originalGetElementById;
    document.head.appendChild = originalAppendChild;
    document.head.removeChild = originalRemoveChild;
    document.dispatchEvent = originalDispatchEvent;

    process.env = originalEnv;
  });

  describe("AnalyticsDataLayerContext", () => {
    it("should be created with default values", () => {
      expect(AnalyticsDataLayerContext).toBeDefined();
      expect(AnalyticsDataLayerContext.Provider).toBeDefined();
      expect(AnalyticsDataLayerContext.Consumer).toBeDefined();
    });
  });

  describe("useAnalytics", () => {
    it("should return the context value", () => {
      expect(useAnalytics).toBeDefined();
      expect(typeof useAnalytics).toBe("function");
    });
  });

  describe("AnalyticsDataLayerProvider", () => {
    it("should setup data layer when user is available", () => {
      (useData as jest.Mock).mockReturnValue({
        user: {
          platformId: "123",
          account: { id: "456" }
        }
      });

      render(
        <AnalyticsDataLayerProvider>
          <div>Test Child</div>
        </AnalyticsDataLayerProvider>
      );

      expect(mockCreateElement).toHaveBeenCalledWith("script");
      expect(mockAppendChild).toHaveBeenCalled();
    });

    it("should not setup data layer when DATA_LAYER_URL is not defined", () => {
      process.env.DATA_LAYER_URL = undefined;

      (useData as jest.Mock).mockReturnValue({
        user: {
          platformId: "123",
          account: { id: "456" }
        }
      });

      render(
        <AnalyticsDataLayerProvider>
          <div>Test Child</div>
        </AnalyticsDataLayerProvider>
      );

      expect(mockCreateElement).not.toHaveBeenCalledWith("script");
      expect(mockAppendChild).not.toHaveBeenCalled();
    });

    it("should clean up script on unmount", () => {
      (useData as jest.Mock).mockReturnValue({
        user: {
          platformId: "123",
          account: { id: "456" }
        }
      });

      const { unmount } = render(
        <AnalyticsDataLayerProvider>
          <div>Test Child</div>
        </AnalyticsDataLayerProvider>
      );

      unmount();

      expect(mockGetElementById).toHaveBeenCalledWith("agroreport_data_layer");
      expect(mockRemoveChild).toHaveBeenCalled();
    });

    it("should not remove script if not found", () => {
      (useData as jest.Mock).mockReturnValue({
        user: {
          platformId: "123",
          account: { id: "456" }
        }
      });

      mockGetElementById.mockReturnValueOnce(null);

      const { unmount } = render(
        <AnalyticsDataLayerProvider>
          <div>Test Child</div>
        </AnalyticsDataLayerProvider>
      );

      unmount();

      expect(mockGetElementById).toHaveBeenCalledWith("agroreport_data_layer");
      expect(mockRemoveChild).not.toHaveBeenCalled();
    });

    it("should queue events when user is not available", () => {
      (useData as jest.Mock).mockReturnValue({ user: null });

      render(
        <AnalyticsDataLayerProvider>
          <TestComponent />
        </AnalyticsDataLayerProvider>
      );

      screen.getByTestId("send-event-button").click();

      expect(mockDispatchEvent).not.toHaveBeenCalled();
    });

    it("should send events when user is available", () => {
      (useData as jest.Mock).mockReturnValue({
        user: {
          platformId: "123",
          account: { id: "456" }
        }
      });

      render(
        <AnalyticsDataLayerProvider>
          <TestComponent />
        </AnalyticsDataLayerProvider>
      );

      screen.getByTestId("send-event-button").click();

      expect(mockDispatchEvent).toHaveBeenCalled();
    });

    it("should process queued events when user becomes available", () => {
      let mockUser = null;
      (useData as jest.Mock).mockImplementation(() => ({ user: mockUser }));

      const { rerender } = render(
        <AnalyticsDataLayerProvider>
          <TestComponent />
        </AnalyticsDataLayerProvider>
      );

      screen.getByTestId("send-event-button").click();

      expect(mockDispatchEvent).not.toHaveBeenCalled();

      mockUser = { platformId: "123", account: { id: "456" } };
      (useData as jest.Mock).mockImplementation(() => ({ user: mockUser }));

      rerender(
        <AnalyticsDataLayerProvider>
          <TestComponent />
        </AnalyticsDataLayerProvider>
      );

      expect(mockDispatchEvent).toHaveBeenCalled();
    });
  });
});
