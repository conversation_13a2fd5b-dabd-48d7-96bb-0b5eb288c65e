import { createContext, useContext, useEffect, useState } from "react";
import { useData } from "./DataContext";

export const AnalyticsDataLayerContext = createContext<{
  sendPageViewEvent: (values: Record<string, string>) => void;
}>({ sendPageViewEvent: () => {} });

export function AnalyticsDataLayerProvider({ children }) {
  const [dataLayer, setDataLayer] = useState<
    Record<"pageInfo" | "userInfo" | "custom" | "rule", any>
  >({
    pageInfo: {
      pageName: "",
      ambiente: "LG",
      siteSection: "Autoatendimento",
      url: location.href,
    },
    userInfo: {
      userId: "",
      account: {},
    },
    custom: {
      events: ["CustomPageView"],
    },
    rule: "pageLoad",
  });
  const { user } = useData();
  const [queue, setQueue] = useState<Record<string, any>[]>([]);

  useEffect(() => {
    return () => {
      clean();
    };
  }, []);

  useEffect(() => {
    if (!user) return;
    setup();
    if (queue.length) {
      queue.forEach((data) => _sendPageViewEvent(data));
      setQueue([]);
    }
  }, [user]);

  function setup(): void {
    if (!process.env.DATA_LAYER_URL) return;
    const updatedDataLayer = {
      ...dataLayer,
      ...{
        userInfo: {
          userId: user.platformId,
          account: user.account,
        },
      },
    };
    (document as any).DataLayer = updatedDataLayer;
    setDataLayer(updatedDataLayer);
    const script = document.createElement("script");
    script.id = "agroreport_data_layer";
    script.src = process.env.DATA_LAYER_URL;
    script.setAttribute("async", "");
    document.head.appendChild(script);
  }

  function clean(): void {
    const script = document.getElementById("agroreport_data_layer");
    if (!script) return;
    document.head.removeChild(script);
  }

  function sendPageViewEvent(values: Record<string, string>): void {
    if (!process.env.DATA_LAYER_URL) return;
    if (!user) {
      setQueue([...queue, values]);
      return;
    }
    _sendPageViewEvent(values);
  }

  function _sendPageViewEvent(values: Record<string, string>): void {
    if (!process.env.DATA_LAYER_URL) return;
    let updatedDataLayer = {
      ...dataLayer,
      pageInfo: { ...dataLayer.pageInfo, ...values },
    };
    if (!dataLayer.userInfo.userId.length && user) {
      updatedDataLayer = {
        ...updatedDataLayer,
        userInfo: {
          userId: user.platformId,
          account: user.account,
        },
      };
    }
    (document as any).DataLayer = updatedDataLayer;
    setDataLayer(updatedDataLayer);
    document.dispatchEvent(
      new CustomEvent("CustomPageView", { detail: (document as any).DataLayer })
    );
  }

  return (
    <AnalyticsDataLayerContext.Provider value={{ sendPageViewEvent }}>
      {children}
    </AnalyticsDataLayerContext.Provider>
  );
}

export const useAnalytics = () => {
  return useContext(AnalyticsDataLayerContext);
};
