import { ReportAbout } from "@types";
import {
  ReportAboutLine,
  ReportAboutWrapper,
} from "./ReportAboutColumn.styles";
import { useMemo } from "react";

const DISPLAY_FIELDS = ["CAR", "SIGEF", "SNCI", "PROPERTY_NAME"];
const FIELD_NAME = {
  CAR: "CAR",
  SIGEF: "SIGEF",
  SNCI: "SNCI",
  PROPERTY_NAME: "Nome da propriedade",
};

function ReportAboutColumn({ reportAbout }: { reportAbout: ReportAbout[] }) {
  const AboutComponent = useMemo(() => {
    const array = [...reportAbout];
    const sorted = array.sort((a, b) => (a.label === "PROPERTY_NAME" ? -1 : 1));

    const result = sorted
      .filter((d) => DISPLAY_FIELDS.includes(d.label))
      .map((d, i) => {
        return (
          <div key={i}>
            <ReportAboutLine
              style={{
                fontWeight: 300,
                fontSize: "12px",
              }}
            >
              {FIELD_NAME[d.label]}
            </ReportAboutLine>
            <ReportAboutLine
              style={{
                fontWeight: 400,
                fontSize: "14px",
              }}
            >
              {d.value}
            </ReportAboutLine>
          </div>
        );
      });

    return result;
  }, [reportAbout]);

  return (
    <ReportAboutWrapper data-testid="pb-t-report-about-col">
      {AboutComponent}
    </ReportAboutWrapper>
  );
}

export default ReportAboutColumn;
