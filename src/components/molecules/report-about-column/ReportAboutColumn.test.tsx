import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import ReportAboutColumn from "./ReportAboutColumn";
import { ReportAbout } from "@types";

describe("ReportAboutColumn component", () => {
  it("should render the component", () => {
    render(<ReportAboutColumn reportAbout={[]} />);
    const component = screen.queryByTestId("pb-t-report-about-col");
    expect(component).toBeInTheDocument();
  });

  afterEach(() => {
    cleanup();
  });

  it("should render the component", () => {
    render(<ReportAboutColumn reportAbout={[]} />);
    const component = screen.queryByTestId("pb-t-report-about-col");
    expect(component).toBeInTheDocument();
  });

  it("should render the correct number of report items", () => {
    const reportAbout: ReportAbout[] = [
      { label: "CAR", value: "12345" },
      { label: "SIGEF", value: "67890" },
      { label: "SNCI", value: "ABCDE" },
      { label: "PROPERTY_NAME", value: "Farm XYZ" },
    ];
    render(<ReportAboutColumn reportAbout={reportAbout} />);
    const items = screen.getAllByText(/CAR|SIGEF|SNCI|Nome da propriedade/);
    expect(items).toHaveLength(4);
  });

  it("should render the correct labels and values", () => {
    const reportAbout: ReportAbout[] = [
      { label: "CAR", value: "12345" },
      { label: "SIGEF", value: "67890" },
      { label: "SNCI", value: "ABCDE" },
      { label: "PROPERTY_NAME", value: "Farm XYZ" },
    ];
    render(<ReportAboutColumn reportAbout={reportAbout} />);

    expect(screen.getByText("CAR")).toBeInTheDocument();
    expect(screen.getByText("12345")).toBeInTheDocument();
    expect(screen.getByText("SIGEF")).toBeInTheDocument();
    expect(screen.getByText("67890")).toBeInTheDocument();
    expect(screen.getByText("SNCI")).toBeInTheDocument();
    expect(screen.getByText("ABCDE")).toBeInTheDocument();
    expect(screen.getByText("Nome da propriedade")).toBeInTheDocument();
    expect(screen.getByText("Farm XYZ")).toBeInTheDocument();
  });

  it("should sort PROPERTY_NAME to the top", () => {
    const reportAbout: ReportAbout[] = [
      { label: "CAR", value: "12345" },
      { label: "PROPERTY_NAME", value: "Farm XYZ" },
      { label: "SIGEF", value: "67890" },
    ];
    render(<ReportAboutColumn reportAbout={reportAbout} />);
    const items = screen.getAllByText(/CAR|SIGEF|Nome da propriedade/);
    expect(items[0]).toHaveTextContent("Nome da propriedade");
  });
});
