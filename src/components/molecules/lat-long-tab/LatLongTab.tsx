import { Col, Form, Row, Select, notification } from "antd";
import { useForm } from "antd/lib/form/Form";
import { Button, Input } from "brainui";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaMap, FaMapMarked } from "react-icons/fa";
import { ButtonsContainer, StyledRow } from "./LatLongTab.styles";

interface LatLongTabProps {
  onGetPropertyByCoordinates: (long: number, lat: number) => Promise<void>;
}

type CoordinateType = "decimal" | "gms";

const gmsInputs = [
  {
    name: "degrees",
    label: "Degree(s)",
  },
  {
    name: "minutes",
    label: "Minute(s)",
  },
  {
    name: "seconds",
    label: "Second(s)",
  },
];

function LatLongTab({ onGetPropertyByCoordinates }: LatLongTabProps) {
  const { t } = useTranslation();
  const [type, setType] = useState<CoordinateType>("decimal");
  const [form] = useForm();

  function handleVariantCoordinateButton(coordType: CoordinateType) {
    return type === coordType ? "standard" : "outlined";
  }

  function getPropertyByCoordinates() {
    let location: { longitude: number; latitude: number };

    const values = form.getFieldsValue();
    delete values.property_name;

    if (hasUndefinedOrEmptyValues(values)) {
      notification.error({
        message: t("FILL_EVERY_FIELD"),
        description: t("ALERT_FILL_COORDINATE_FIELDS"),
      });

      return;
    }

    if (type === "decimal") location = values;
    if (type === "gms") location = convertToDecimal(values);
    onGetPropertyByCoordinates(+location.longitude, +location.latitude);
  }

  function hasUndefinedOrEmptyValues(values) {
    return Object.values(values).some(
      (value) => value === undefined || value === ""
    );
  }

  function convertToDecimal({
    degrees_lat,
    minutes_lat,
    seconds_lat,
    direction_lat,
    degrees_long,
    minutes_long,
    seconds_long,
    direction_long,
  }) {
    let latitude = +degrees_lat + +minutes_lat / 60 + +seconds_lat / 3600;
    if (direction_lat === "S") latitude = -latitude;

    let longitude = +degrees_long + +minutes_long / 60 + +seconds_long / 3600;
    if (direction_long === "W") longitude = -longitude;

    return { latitude, longitude };
  }

  useEffect(() => {
    handleResetLatLongTab();
  }, [type]);

  function handleResetLatLongTab() {
    form.resetFields([
      "latitude",
      "longitude",
      "degrees_lat",
      "minutes_lat",
      "seconds_lat",
      "direction_lat",
      "degrees_long",
      "minutes_long",
      "seconds_long",
      "direction_long",
    ]);
  }

  return (
    <Form form={form}>
      <ButtonsContainer
        gutter={[8, 8]}
        justify="center"
        data-testid="pb-t-lat-long"
      >
        <Col span={7}>
          <Button
            size="small"
            icon={<FaMap />}
            variant={handleVariantCoordinateButton("decimal")}
            onClick={() => setType("decimal")}
          >
            Decimal
          </Button>
        </Col>

        <Col span={7}>
          <Button
            size="small"
            icon={<FaMapMarked />}
            variant={handleVariantCoordinateButton("gms")}
            onClick={() => setType("gms")}
          >
            GMS
          </Button>
        </Col>
      </ButtonsContainer>

      {type === "decimal" && (
        <Row gutter={8} align="middle" data-testid="decimal-container">
          <Col span={12}>
            <Form.Item
              name="latitude"
              rules={[
                {
                  required: true,
                  message: t("ALERT_FILL_LATITUDE"),
                },
              ]}
            >
              <Input
                className="label-input"
                label={t("LATITUDE")}
                placeholder="-17.854862"
                autoComplete="off"
                data-testid="latitude-input"
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="longitude"
              rules={[
                {
                  required: true,
                  message: t("ALERT_FILL_LONGITUDE"),
                },
              ]}
            >
              <Input
                className="label-input"
                label={t("LONGITUDE")}
                placeholder="-55.846819"
                autoComplete="off"
                data-testid="longitude-input"
              />
            </Form.Item>
          </Col>
        </Row>
      )}

      {type === "gms" && (
        <>
          <Col span={3}>Latitude</Col>
          <StyledRow>
            {gmsInputs.map(({ name, label }) => (
              <Col span={6} key={`${name}_lat`}>
                <Form.Item
                  name={`${name}_lat`}
                  rules={[
                    {
                      required: true,
                      message: `${t("FILL_FIELD")} ${label.toLowerCase()}`,
                    },
                  ]}
                >
                  <Input
                    label={label}
                    autoComplete="off"
                    className="label-input"
                    data-testid={`${name}_lat-input`}
                  />
                </Form.Item>
              </Col>
            ))}

            <Col span={3}>
              <Form.Item name="direction_lat" initialValue="N">
                <Select options={[{ label: "N" }, { value: "S" }]} data-testid="direction_lat-select" />
              </Form.Item>
            </Col>
          </StyledRow>

          <Col span={3}>Longitude</Col>
          <StyledRow>
            {gmsInputs.map(({ name, label }) => (
              <Col span={6} key={`${name}_long`}>
                <Form.Item
                  name={`${name}_long`}
                  rules={[
                    {
                      required: true,
                      message: `${t("FILL_FIELD")} ${label.toLowerCase()}`,
                    },
                  ]}
                >
                  <Input
                    label={label}
                    autoComplete="off"
                    className="label-input"
                    data-testid={`${name}_long-input`}
                  />
                </Form.Item>
              </Col>
            ))}

            <Col span={3}>
              <Form.Item name="direction_long" initialValue="W">
                <Select
                  options={[
                    { label: "W", value: "W" },
                    { label: "E", value: "E" },
                  ]}
                  data-testid="direction_long-select"
                />
              </Form.Item>
            </Col>
          </StyledRow>
        </>
      )}

      <Row gutter={[8, 8]} justify="end">
        <Col>
          <Button onClick={getPropertyByCoordinates}>
            {t("VIEW_PROPERTY")}
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

export default LatLongTab;
