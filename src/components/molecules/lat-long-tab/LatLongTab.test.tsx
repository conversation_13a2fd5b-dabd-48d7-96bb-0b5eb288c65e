import { FormContext } from "antd/lib/form/context";
import Lat<PERSON>ongTab from "./LatLongTab";
import { fireEvent, render, screen } from "@testing-library/react";
import { useTranslation } from "react-i18next";
import { notification } from "antd";

jest.mock("react-i18next");
jest.mock("antd", () => {
  const originalModule = jest.requireActual("antd");
  return {
    ...originalModule,
    notification: {
      error: jest.fn(),
    },
  };
});

describe("LatLongTab component", () => {
  beforeEach(() => {
    (useTranslation as jest.Mock).mockReturnValue({
      t: (data: string) => data,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(
      <FormContext.Provider
        value={{ itemRef: (_name) => (<form></form>) as any, vertical: false }}
      >
        <LatLongTab onGetPropertyByCoordinates={async (_long, _lat) => {}} />
      </FormContext.Provider>
    );
    const component = screen.queryByTestId("pb-t-lat-long");
    expect(component).toBeInTheDocument();
  });

  it("should render decimal input fields by default", () => {
    render(
      <FormContext.Provider
        value={{ itemRef: (_name) => (<form></form>) as any, vertical: false }}
      >
        <LatLongTab onGetPropertyByCoordinates={async (_long, _lat) => {}} />
      </FormContext.Provider>
    );

    const decimalContainer = screen.queryByTestId("decimal-container");
    expect(decimalContainer).toBeInTheDocument();
  });

  it("should switch to GMS input fields when GMS button is clicked", () => {
    render(
      <FormContext.Provider
        value={{
          itemRef: (_name) => (<form></form>) as any,
          vertical: false,
        }}
      >
        <LatLongTab onGetPropertyByCoordinates={async (_long, _lat) => {}} />
      </FormContext.Provider>
    );

    const gmsButton = screen.getByText("GMS");
    fireEvent.click(gmsButton);

    const decimalContainer = screen.queryByTestId("decimal-container");
    expect(decimalContainer).not.toBeInTheDocument();

    expect(screen.getByText("Latitude")).toBeInTheDocument();
    expect(screen.getByText("Longitude")).toBeInTheDocument();
  });

  it("should switch back to decimal input fields when Decimal button is clicked", () => {
    render(
      <FormContext.Provider
        value={{
          itemRef: (_name) => (<form></form>) as any,
          vertical: false,
        }}
      >
        <LatLongTab onGetPropertyByCoordinates={async (_long, _lat) => {}} />
      </FormContext.Provider>
    );

    const gmsButton = screen.getByText("GMS");
    fireEvent.click(gmsButton);

    const decimalButton = screen.getByText("Decimal");
    fireEvent.click(decimalButton);

    const decimalContainer = screen.queryByTestId("decimal-container");
    expect(decimalContainer).toBeInTheDocument();
  });

  it("should show error notification if fields are empty", () => {
    render(
      <FormContext.Provider
        value={{
          itemRef: (_name) => (<form></form>) as any,
          vertical: false,
        }}
      >
        <LatLongTab onGetPropertyByCoordinates={async (_long, _lat) => {}} />
      </FormContext.Provider>
    );

    const viewPropertyButton = screen.getByText("VIEW_PROPERTY");
    fireEvent.click(viewPropertyButton);

    expect(notification.error).toHaveBeenCalledWith({
      message: "FILL_EVERY_FIELD",
      description: "ALERT_FILL_COORDINATE_FIELDS",
    });
  });

  it("should call onGetPropertyByCoordinates with correct decimal values", () => {
    const mockOnGetPropertyByCoordinates = jest.fn();
    render(
      <FormContext.Provider
        value={{
          itemRef: (_name) => (<form></form>) as any,
          vertical: false,
        }}
      >
        <LatLongTab
          onGetPropertyByCoordinates={mockOnGetPropertyByCoordinates}
        />
      </FormContext.Provider>
    );

    const latitudeInput = screen.getByTestId("latitude-input");
    const longitudeInput = screen.getByTestId("longitude-input");

    fireEvent.change(latitudeInput, { target: { value: "10" } });
    fireEvent.change(longitudeInput, { target: { value: "20" } });

    const viewPropertyButton = screen.getByText("VIEW_PROPERTY");
    fireEvent.click(viewPropertyButton);

    expect(mockOnGetPropertyByCoordinates).toHaveBeenCalledWith(20, 10);
  });

  it("should call onGetPropertyByCoordinates with correct values from GMS inputs", () => {
    const mockOnGetPropertyByCoordinates = jest.fn();
    render(
      <FormContext.Provider
        value={{
          itemRef: (_name) => (<form></form>) as any,
          vertical: false,
        }}
      >
        <LatLongTab
          onGetPropertyByCoordinates={mockOnGetPropertyByCoordinates}
        />
      </FormContext.Provider>
    );

    const gmsButton = screen.getByText("GMS");
    fireEvent.click(gmsButton);

    const degreesLatInput = screen.getByTestId("degrees_lat-input");
    const minutesLatInput = screen.getByTestId("minutes_lat-input");
    const secondsLatInput = screen.getByTestId("seconds_lat-input");
    const directionLatSelect = screen.getByTestId("direction_lat-select");

    fireEvent.change(degreesLatInput, { target: { value: "10" } });
    fireEvent.change(minutesLatInput, { target: { value: "30" } });
    fireEvent.change(secondsLatInput, { target: { value: "0" } });

    expect(directionLatSelect).toBeInTheDocument();


    const degreesLongInput = screen.getByTestId("degrees_long-input");
    const minutesLongInput = screen.getByTestId("minutes_long-input");
    const secondsLongInput = screen.getByTestId("seconds_long-input");
    const directionLongSelect = screen.getByTestId("direction_long-select");

    fireEvent.change(degreesLongInput, { target: { value: "45" } });
    fireEvent.change(minutesLongInput, { target: { value: "15" } });
    fireEvent.change(secondsLongInput, { target: { value: "30" } });

    expect(directionLongSelect).toBeInTheDocument();

    const viewPropertyButton = screen.getByText("VIEW_PROPERTY");
    fireEvent.click(viewPropertyButton);

    expect(mockOnGetPropertyByCoordinates).toHaveBeenCalled();
  });

  describe("convertToDecimal function", () => {
    it("should convert GMS coordinates to decimal correctly for North and East", () => {
      interface GMSCoordinates {
        degrees_lat: string;
        minutes_lat: string;
        seconds_lat: string;
        direction_lat: string;
        degrees_long: string;
        minutes_long: string;
        seconds_long: string;
        direction_long: string;
      }

      const convertToDecimal = (params: GMSCoordinates) => {
        let latitude = +params.degrees_lat + +params.minutes_lat / 60 + +params.seconds_lat / 3600;
        if (params.direction_lat === "S") latitude = -latitude;

        let longitude = +params.degrees_long + +params.minutes_long / 60 + +params.seconds_long / 3600;
        if (params.direction_long === "W") longitude = -longitude;

        return { latitude, longitude };
      };

      const result = convertToDecimal({
        degrees_lat: "10",
        minutes_lat: "30",
        seconds_lat: "0",
        direction_lat: "N",
        degrees_long: "45",
        minutes_long: "15",
        seconds_long: "30",
        direction_long: "E"
      });

      expect(result.latitude).toBeCloseTo(10.5, 5);
      expect(result.longitude).toBeCloseTo(45.25833, 5);
    });

    it("should convert GMS coordinates to decimal correctly for South and West", () => {
      interface GMSCoordinates {
        degrees_lat: string;
        minutes_lat: string;
        seconds_lat: string;
        direction_lat: string;
        degrees_long: string;
        minutes_long: string;
        seconds_long: string;
        direction_long: string;
      }

      const convertToDecimal = (params: GMSCoordinates) => {
        let latitude = +params.degrees_lat + +params.minutes_lat / 60 + +params.seconds_lat / 3600;
        if (params.direction_lat === "S") latitude = -latitude;

        let longitude = +params.degrees_long + +params.minutes_long / 60 + +params.seconds_long / 3600;
        if (params.direction_long === "W") longitude = -longitude;

        return { latitude, longitude };
      };

      const result = convertToDecimal({
        degrees_lat: "10",
        minutes_lat: "30",
        seconds_lat: "0",
        direction_lat: "S",
        degrees_long: "45",
        minutes_long: "15",
        seconds_long: "30",
        direction_long: "W"
      });

      expect(result.latitude).toBeCloseTo(-10.5, 5);
      expect(result.longitude).toBeCloseTo(-45.25833, 5);
    });
  });
});
