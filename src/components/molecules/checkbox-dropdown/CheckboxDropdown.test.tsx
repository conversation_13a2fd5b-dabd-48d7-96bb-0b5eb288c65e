import React from "react";
import { render, fireEvent } from "@testing-library/react";
import { CheckboxDropdown, CheckboxItem } from "./CheckboxDropdown";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { I18nextProvider } from "react-i18next";
import { i18n } from "../../../i18n/index";

const checkboxItems: CheckboxItem[] = [
  { checked: false, label: "Item 1", value: "1" },
  { checked: true, label: "Item 2", value: "2" },
];

const onChangeCheckAll = jest.fn((event: CheckboxChangeEvent) => {});
const onChangeCheckboxItem = jest.fn(
  (event: CheckboxChangeEvent, item: CheckboxItem) => {}
);
const onCloseDropdown = jest.fn();

describe("CheckboxDropdown", () => {
  it("renders without crashing", () => {
    const { getByPlaceholderText } = render(
      <I18nextProvider i18n={i18n}>
        <CheckboxDropdown
          checkboxItems={checkboxItems}
          onChangeCheckAll={onChangeCheckAll}
          isCheckAllSelected={false}
          onChangeCheckboxItem={onChangeCheckboxItem}
          onCloseDropdown={onCloseDropdown}
          placeholder="Select items"
        />
      </I18nextProvider>
    );

    expect(getByPlaceholderText("Select items")).toBeInTheDocument();
  });

  it("calls onChangeCheckAll when 'Check All' is clicked", () => {
    const { getByTestId } = render(
      <I18nextProvider i18n={i18n}>
        <CheckboxDropdown
          checkboxItems={checkboxItems}
          onChangeCheckAll={onChangeCheckAll}
          isCheckAllSelected={false}
          onChangeCheckboxItem={onChangeCheckboxItem}
          onCloseDropdown={onCloseDropdown}
          placeholder="Select items"
        />
      </I18nextProvider>
    );

    fireEvent.click(getByTestId("check-all-checkbox"));
    expect(onChangeCheckAll).toHaveBeenCalled();
  });

  it("calls onChangeCheckboxItem when an item is clicked", () => {
    const { getByText } = render(
      <I18nextProvider i18n={i18n}>
        <CheckboxDropdown
          checkboxItems={checkboxItems}
          onChangeCheckAll={onChangeCheckAll}
          isCheckAllSelected={false}
          onChangeCheckboxItem={onChangeCheckboxItem}
          onCloseDropdown={onCloseDropdown}
          placeholder="Select items"
        />
      </I18nextProvider>
    );

    fireEvent.click(getByText("Item 1"));
    expect(onChangeCheckboxItem).toHaveBeenCalledWith(
      expect.any(Object),
      checkboxItems[0]
    );
  });

  it("filters items based on input", () => {
    const { getByPlaceholderText, queryByText } = render(
      <I18nextProvider i18n={i18n}>
        <CheckboxDropdown
          checkboxItems={checkboxItems}
          onChangeCheckAll={onChangeCheckAll}
          isCheckAllSelected={false}
          onChangeCheckboxItem={onChangeCheckboxItem}
          onCloseDropdown={onCloseDropdown}
          placeholder="Select items"
        />
      </I18nextProvider>
    );

    const input = getByPlaceholderText("Select items");
    fireEvent.change(input, { target: { value: "Item 1" } });

    expect(queryByText("Item 1")).toBeInTheDocument();
    expect(queryByText("Item 2")).not.toBeInTheDocument();
  });

  it("closes dropdown when clicking outside", () => {
    const { getByPlaceholderText, getByText } = render(
      <I18nextProvider i18n={i18n}>
        <CheckboxDropdown
          checkboxItems={checkboxItems}
          onChangeCheckAll={onChangeCheckAll}
          isCheckAllSelected={false}
          onChangeCheckboxItem={onChangeCheckboxItem}
          onCloseDropdown={onCloseDropdown}
          placeholder="Select items"
        />
      </I18nextProvider>
    );

    const input = getByPlaceholderText("Select items");
    fireEvent.click(input);

    fireEvent.mouseDown(document);
    expect(onCloseDropdown).toHaveBeenCalled();
  });
});
