import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  position: relative;
  width: 100%;
  padding-bottom: 24px;
  max-width: 800px;
`;

export const CheckboxContainer = styled.div<{ isOpen: boolean }>`
  display: ${({ isOpen }) => (isOpen ? "flex" : "none")};
  flex-direction: column;
  margin-top: 8px;
  width: 99%;
  border: 1.5px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  animation: toggle_checkbox_dropdown 0.2s linear normal;
  transform-origin: top;
  position: sticky;
  max-width: 760px;
  z-index: 1000;
  box-shadow: 4px 5px 5px 1px rgba(0, 0, 0, 0.4);

  label {
    width: 100%;
  }

  .ant-checkbox-group {
    width: 100%;
  }

  .row-checked {
    background: rgba(0, 178, 119, 0.1);
  }

  .checkbox-row {
    display: flex;
    padding: 4px 6px;
    width: 100%;
    margin: 0;

    :nth-child(1) {
      border-radius: 6px 6px 0 0;
    }

    &:hover {
      background: var(--surface-color-4);
      cursor: pointer;
    }
  }

  .ant-checkbox-group-item {
    display: block;
    margin-right: 0;
    width: 100%;
  }

  @keyframes toggle_checkbox_dropdown {
    from {
      transform: scaleY(0);
    }

    to {
      transform: scaleY(1);
    }
  }
`;
