import { Checkbox, Input, Row } from "antd";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { FC, useMemo, useRef, useState } from "react";
import { CheckboxContainer, Container } from "./CheckboxDropdown.styles";
import useOnClickOutside from "use-onclickoutside";
import { useTranslation } from "react-i18next";

export type CheckboxItem = {
  checked: boolean;
  label: string;
  value: string;
};

type CheckboxDropdownProps = {
  checkboxItems: Array<CheckboxItem>;
  onChangeCheckAll: (event: CheckboxChangeEvent) => void;
  isCheckAllSelected: boolean;
  onChangeCheckboxItem: (
    event: CheckboxChangeEvent,
    item: CheckboxItem
  ) => void;
  placeholder: string;
  onCloseDropdown: () => void;
  hideInput?: boolean;
  isOpenCheckboxContainerFromOutsideOfComponent?: boolean;
};

export const CheckboxDropdown: FC<CheckboxDropdownProps> = ({
  checkboxItems,
  onChangeCheckAll,
  isCheckAllSelected,
  onChangeCheckboxItem,
  onCloseDropdown,
  placeholder,
  hideInput = false,
  isOpenCheckboxContainerFromOutsideOfComponent = false,
}) => {
  const { t } = useTranslation();

  const [inputFilterCheckbox, setInputFilterCheckbox] = useState<string>("");
  const [isOpenCheckboxContainer, setIsOpenCheckboxContainer] = useState(false);

  const refCheckboxContainer = useRef<HTMLDivElement>(null);

  const inputHash = useMemo(() => {
    return Math.random();
  }, []);

  const checkboxIndeterminate = useMemo(() => {
    const checkedList = checkboxItems.filter((item) => item.checked);
    return checkedList.length > 0 && checkedList.length < checkboxItems.length;
  }, [checkboxItems]);

  function handleClickOutsideCheckboxContainer(event: Event) {
    if (event.target) {
      const target: HTMLElement = event.target as HTMLElement;
      if (target.id === `input-checkbox-dropdown-${placeholder}-${inputHash}`) {
        return;
      }
    }

    if (
      !isOpenCheckboxContainer &&
      !isOpenCheckboxContainerFromOutsideOfComponent
    )
      return;

    setIsOpenCheckboxContainer(false);
    onCloseDropdown();
  }

  useOnClickOutside(refCheckboxContainer, handleClickOutsideCheckboxContainer);

  return (
    <>
      {!hideInput && (
        <Input
          id={`input-checkbox-dropdown-${placeholder}-${inputHash}`}
          placeholder={placeholder}
          onChange={(event) => {
            setInputFilterCheckbox(event.target.value);
          }}
          onClick={() => setIsOpenCheckboxContainer(true)}
        />
      )}

      <Container>
        <CheckboxContainer
          isOpen={
            isOpenCheckboxContainer ||
            isOpenCheckboxContainerFromOutsideOfComponent
          }
          ref={refCheckboxContainer}
        >
          <Row className="checkbox-row">
            <Checkbox
              onChange={onChangeCheckAll}
              indeterminate={checkboxIndeterminate}
              checked={isCheckAllSelected}
              data-testid="check-all-checkbox"
            >
              {t("CHECK_ALL")}
            </Checkbox>
          </Row>
          {checkboxItems
            .filter((item) =>
              t(item.label)
                .toLowerCase()
                .includes(inputFilterCheckbox.toLowerCase())
            )
            .map((item) => (
              <Row
                key={item.value}
                className={`checkbox-row ${item.checked ? "row-checked" : ""}`}
              >
                <Checkbox
                  key={item.value}
                  onChange={(event) => onChangeCheckboxItem(event, item)}
                  checked={item.checked}
                  indeterminate={false}
                >
                  {t(item.label)}
                </Checkbox>
              </Row>
            ))}
        </CheckboxContainer>
      </Container>
    </>
  );
};
