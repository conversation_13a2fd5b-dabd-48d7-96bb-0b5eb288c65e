import { DataContext, RequestFormContext } from "@contexts";
import { BannerData } from "@types";
import { useContext } from "react";
import { useTranslation } from "react-i18next";
import {
  BannerAction,
  BannerActions,
  BannerHeader,
  BannerImage,
  BannerLink,
  BannerWrapper,
} from "./Banner.styles";
import { useCardsList } from "../../../hooks/use-cards-list";
import { useRequestForm } from "../../../hooks/use-request-form";

function Banner({
  title,
  description,
  color,
  image,
  link,
  report_type,
}: BannerData) {
  const { handleOpenRequestForm } = useRequestForm();
  const { cardsList } = useCardsList();
  const { t } = useTranslation();

  const reportMetadata = cardsList.find((r) => r.type == report_type);

  return (
    <BannerWrapper style={{ backgroundColor: color }} data-testid="pb-t-banner">
      <BannerImage src={image} alt="" />
      <BannerHeader>
        <h1>{title}</h1>
        <p>{description}</p>
      </BannerHeader>
      <BannerActions>
        {link.length > 0 && (
          <BannerLink
            href={link}
            target="_blank"
            title={t("READ_MORE")}
            data-testid="pb-t-banner-read-more"
          >
            {t("READ_MORE")}
          </BannerLink>
        )}
        {report_type &&
          !reportMetadata?.commingSoon &&
          reportMetadata?.hasPermission && (
            <BannerAction
              onClick={() => handleOpenRequestForm(report_type)}
              data-testid="pb-t-banner-request"
            >
              {t("ACTION_REQUEST_REPORT")}
            </BannerAction>
          )}
        {report_type &&
          !reportMetadata?.commingSoon &&
          !reportMetadata?.hasPermission && (
            <a
              href="https://www.serasaexperian.com.br/solucoes/agro-report/#form"
              target="_blank"
              data-testid="link-serasa-unlock-banner"
            >
              <BannerAction>{t("UNLOCK")}</BannerAction>
            </a>
          )}
      </BannerActions>
    </BannerWrapper>
  );
}

export default Banner;
