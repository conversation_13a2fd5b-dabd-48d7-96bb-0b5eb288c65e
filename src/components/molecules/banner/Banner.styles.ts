import styled from "styled-components";

export const BannerWrapper = styled.div`
  height: 204px;
  border-radius: 4px;
  padding: 24px;
  gap: 8px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
`;

export const BannerImage = styled.img`
  position: absolute;
  top: 0;
  height: 100%;
  width: auto;
  right: 0;
  z-index: 0;
`;

export const BannerHeader = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 8px;
  z-index: 1;
  max-width: 550px;
  overflow: hidden;

  h1 {
    font-size: 20px;
    margin: 0;
  }

  p {
    color: #252c34;
    margin: 0;
  }
`;

export const BannerActions = styled.div`
  display: flex;
  gap: 8px;
  z-index: 1;
`;

export const BannerLink = styled.a`
  color: #252c34;
  font-weight: 400;
  background: #ffffff;
  border-color: #c8ccd1;
  height: 34px;
  line-height: 34px;
  border-radius: 4px;
  padding: 0 15px;
`;

export const BannerAction = styled.button`
  color: #252c34;
  font-weight: 400;
  background: none;
  border: none;
  height: 34px;
  box-shadow: none;
  padding: 0 15px;
  cursor: pointer;

  :hover,
  :focus {
    color: #252c34;
    background-color: rgba(0, 0, 0, 0.06);
  }
`;
