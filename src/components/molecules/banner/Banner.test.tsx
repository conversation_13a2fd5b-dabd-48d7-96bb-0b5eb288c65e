import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import Banner from "./Banner";
import { useCardsList } from "../../../hooks/use-cards-list";
import { ReportType } from "@types";

jest.mock("../../../hooks/use-cards-list", () => ({
  useCardsList: jest.fn(() => ({
    cardsList: [{ type: "VALUATION", commingSoon: false, hasPermission: true }],
    handleListCards: jest.fn(),
  })),
}));

describe("Banner component", () => {
  afterEach(() => {
    cleanup();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(
      <Banner
        id={1}
        title="Test"
        description="Test"
        color="red"
        image={null}
        link="http://localhost"
        report_type={ReportType.VALUATION}
      ></Banner>
    );

    const component = screen.queryByTestId("pb-t-banner");
    expect(component).toBeInTheDocument();
  });
  it("should not render the link if it is empty", () => {
    render(
      <Banner
        id={1}
        title="Test"
        description="Test"
        color="red"
        image={null}
        link=""
        report_type={ReportType.VALUATION}
      ></Banner>
    );
    const link = screen.queryByTestId("pb-t-banner-read-more");
    expect(link).toBeNull();
  });
  it("should not render the request report button if the report type is null", () => {
    render(
      <Banner
        id={1}
        title="Test"
        description="Test"
        color="red"
        image={null}
        link=""
        report_type={null}
      ></Banner>
    );
    const link = screen.queryByTestId("pb-t-banner-request");
    expect(link).toBeNull();
  });

  it("should click on button of banner action", () => {
    render(
      <Banner
        id={1}
        title="Test"
        description="Test"
        color="red"
        image={null}
        link="http://localhost"
        report_type={ReportType.VALUATION}
      ></Banner>
    );

    const component = screen.getByTestId("pb-t-banner-request");

    fireEvent.click(component);

    expect(component).toBeTruthy();
  });

  it("should show link if does not have permission", () => {
    (useCardsList as jest.Mock).mockImplementation(() => ({
      cardsList: [
        { type: "VALUATION", commingSoon: false, hasPermission: false },
      ],
      handleListCards: jest.fn(),
    }));

    render(
      <Banner
        id={1}
        title="Test"
        description="Test"
        color="red"
        image={null}
        link="http://localhost"
        report_type={ReportType.VALUATION}
      ></Banner>
    );

    expect(screen.getByTestId("link-serasa-unlock-banner")).toBeTruthy();
  });
});
