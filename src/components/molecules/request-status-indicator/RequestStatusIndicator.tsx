import { REPORT_STATUS_LABELS, ReportRequest, ReportStatus } from "@types";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Indicator<PERSON><PERSON>r,
  Indicator<PERSON>ill,
  IndicatorWrapper,
} from "./RequestStatusIndicator.styles";
import { useListReports } from "@/hooks/use-list-reports";

const COLOR = {
  [ReportStatus.ERROR]: "#A8001F",
  [ReportStatus.DONE]: "#00B277",
  [ReportStatus.PENDING]: "#C1C7D0",
  [ReportStatus.PROCESSING]: "#0C7092",
  [ReportStatus.WARNING]: "#FFC400",
};

function RequestStatusIndicator({
  request,
}: {
  readonly request: ReportRequest;
}) {
  const { t } = useTranslation();
  const [status, setStatus] = useState<ReportStatus>(ReportStatus.DONE);
  const [processed, setProcessed] = useState(20);
  const { reports } = useListReports();

  useEffect(() => {
    let _status = status;
    if (request.reports_count == 0) {
      _status = ReportStatus.PROCESSING;
    } else if (request.reports_error == request.reports_count) {
      _status = ReportStatus.ERROR;
    } else if (
      request.reports_error > 0 &&
      request.reports_error != request.reports_count
    ) {
      _status = ReportStatus.WARNING;
    } else if (request.reports_pending > 0 && request.reports_done == 0) {
      _status = ReportStatus.PENDING;
    } else if (request.reports_done == 0) {
      _status = ReportStatus.PROCESSING;
    } else {
      _status = ReportStatus.DONE;
      setProcessed((request.reports_done * 100) / request.reports_count);
    }
    setStatus(_status);
  }, [request, reports]);

  return (
    <IndicatorWrapper data-testid="pb-t-request-status-indicator">
      {status == ReportStatus.DONE ||
      (status == ReportStatus.WARNING && request.reports_done > 0) ? (
        <p>
          {t("REQUESTS_REPORTS_COUNT", {
            requests: request.reports_done,
            reports: request.reports_count,
          })}
        </p>
      ) : (
        <p>{t(REPORT_STATUS_LABELS[status])}</p>
      )}
      <IndicatorContainer>
        <IndicatorFill
          data-testid="indicator-fill"
          style={{ background: COLOR[status], height: `${processed}%` }}
        />
      </IndicatorContainer>
    </IndicatorWrapper>
  );
}

export default RequestStatusIndicator;
