import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import RequestStatusIndicator from "./RequestStatusIndicator";
import { useTranslation } from "react-i18next";
import { REPORT_STATUS_LABELS, ReportStatus } from "@types";
import { useListReports } from "@/hooks/use-list-reports";

afterEach(() => {
  cleanup();
});

jest.mock("react-i18next");
jest.mock("@/hooks/use-list-reports", () => ({
  useListReports: jest.fn(),
}));

describe("RequestStatusIndicator component", () => {
  const translationMock = jest.fn((key, options) => {
    if (key === "REQUESTS_REPORTS_COUNT") {
      return `${options.requests} of ${options.reports} reports`;
    }
    return key;
  });
  const mockReports = [];

  beforeEach(() => {
    jest.clearAllMocks();
    (useTranslation as jest.Mock).mockReturnValue({
      t: translationMock,
    });

    (useListReports as jest.Mock).mockReturnValue({
      reports: mockReports,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const request = {
    id: 0,
    key: 0,
    user: { name: "", email: "" },
    tags_names: [],
    created: new Date(),
    modified: new Date(),
    reports_count: 0,
    reports_done: 0,
    reports_error: 0,
    reports_pending: 0,
    reports_processing: 0,
    reports_types: [],
    reports_waiting: 0,
    reports_warning: 0,
  };

  it("should render the component", () => {
    render(<RequestStatusIndicator request={request} />);
    const component = screen.queryByTestId("pb-t-request-status-indicator");
    expect(component).toBeInTheDocument();
  });

  it("should render request reports count when status is DONE", () => {
    const {} = render(
      <RequestStatusIndicator
        request={{ ...request, reports_count: 1, reports_done: 1 }}
      />
    );

    expect(translationMock).toHaveBeenCalledWith("REQUESTS_REPORTS_COUNT", {
      requests: 1,
      reports: 1,
    });
  });

  it("should render ERROR status when all reports have error", () => {
    const {} = render(
      <RequestStatusIndicator
        request={{ ...request, reports_count: 1, reports_error: 1 }}
      />
    );

    expect(translationMock).toHaveBeenCalledWith(REPORT_STATUS_LABELS[ReportStatus.ERROR]);
  });

  it("should render PROCESSING status when reports are processing", () => {
    const {} = render(
      <RequestStatusIndicator
        request={{ ...request, reports_count: 2, reports_done: 0 }}
      />
    );

    expect(translationMock).toHaveBeenCalledWith(REPORT_STATUS_LABELS[ReportStatus.PROCESSING]);
  });

  it("should render PENDING status when reports are pending", () => {
    const {} = render(
      <RequestStatusIndicator
        request={{
          ...request,
          reports_count: 2,
          reports_pending: 2,
          reports_error: 0,
          reports_done: 0,
        }}
      />
    );

    expect(translationMock).toHaveBeenCalledWith(REPORT_STATUS_LABELS[ReportStatus.PENDING]);
  });

  it("should render reports count when status is WARNING and some reports are done", () => {
    const {} = render(
      <RequestStatusIndicator
        request={{
          ...request,
          reports_count: 2,
          reports_pending: 1,
          reports_error: 1,
          reports_done: 1,
        }}
      />
    );

    expect(translationMock).toHaveBeenCalledWith("REQUESTS_REPORTS_COUNT", {
      requests: 1,
      reports: 2,
    });
  });

  it("should render WARNING status when some reports have errors", () => {
    render(
      <RequestStatusIndicator
        request={{
          ...request,
          reports_count: 2,
          reports_error: 1,
          reports_done: 0,
        }}
      />
    );

    expect(translationMock).toHaveBeenCalledWith(REPORT_STATUS_LABELS[ReportStatus.WARNING]);
  });

  it("should calculate processed percentage correctly for DONE status", () => {
    const { container } = render(
      <RequestStatusIndicator
        request={{
          ...request,
          reports_count: 4,
          reports_done: 3,
        }}
      />
    );

    const indicatorFill = container.querySelector('[data-testid="indicator-fill"]');
    expect(indicatorFill).toHaveStyle("height: 75%"); // 3/4 * 100 = 75%
  });
});