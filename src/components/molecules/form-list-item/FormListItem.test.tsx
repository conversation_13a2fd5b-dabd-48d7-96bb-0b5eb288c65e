import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import FormListItem from "./FormListItem";

afterEach(() => {
  cleanup();
});

describe("FormListItem component", () => {
  it("should render the component", () => {
    render(
      <FormListItem multiple count={0}>
        <div></div>
      </FormListItem>
    );
    const component = screen.queryByTestId("pb-t-form-list-item");
    expect(component).toBeInTheDocument();
  });
  it("should call onAdd when the button is pressed", () => {
    const mockOnAdd = jest.fn();
    render(
      <FormListItem multiple count={0} onAdd={mockOnAdd}>
        <div></div>
      </FormListItem>
    );
    const actionButton = screen.queryByTestId("pb-t-form-list-item-action")!;
    fireEvent.click(actionButton);
    expect(mockOnAdd).toHaveBeenCalled();
  });
  it("should call onRemove when the button is pressed and the item is removable", () => {
    const mockOnRemove = jest.fn();
    render(
      <FormListItem multiple count={0} onRemove={mockOnRemove} canRemove={true}>
        <div></div>
      </FormListItem>
    );
    const actionButton = screen.queryByTestId("pb-t-form-list-item-action")!;
    fireEvent.click(actionButton);
    expect(mockOnRemove).toHaveBeenCalled();
  });
});
