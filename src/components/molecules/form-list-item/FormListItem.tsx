import { useTranslation } from "react-i18next";
import {
  ActionButtonWrapper,
  ButtonAction,
  Content,
  CountContainer,
  ItemContainer,
  SmallText,
} from "./FormListItem.styles";

interface FormListItemProps {
  count?: number;
  multiple?: boolean;
  canRemove?: boolean;
  disabled?: boolean;
  onAdd?: () => void;
  onRemove?: () => void;
  children: any;
}

function FormListItem({
  count,
  multiple,
  canRemove,
  disabled,
  onAdd,
  onRemove,
  children,
}: FormListItemProps) {
  const { t } = useTranslation();

  return (
    <ItemContainer data-testid="pb-t-form-list-item">
      <CountContainer>
        {count ? (count <= 9 ? "0" + count : count) : ""}
      </CountContainer>
      <Content>{children}</Content>
      {multiple && (
        <ActionButtonWrapper>
          <ButtonAction
            data-testid="pb-t-form-list-item-action"
            onClick={() =>
              canRemove ? onRemove && onRemove() : onAdd && onAdd()
            }
            disabled={disabled}
          >
            {canRemove ? "-" : "+"}
          </ButtonAction>
          <SmallText>{canRemove ? t("REMOVE_ITEM") : t("ADD_ITEM")}</SmallText>
        </ActionButtonWrapper>
      )}
    </ItemContainer>
  );
}

export default FormListItem;
