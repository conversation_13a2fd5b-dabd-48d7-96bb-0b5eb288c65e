import { useTranslation } from "react-i18next";
import {
  ActionButtonWrapper,
  ButtonAction,
  Content,
  CountContainer,
  ItemContainer,
  OverlineText,
  SmallText,
  Wrapper,
} from "./RemovableListFile.styles";

interface RemovableListFileProps {
  index: number;
  filename: string;
  fileDetails: string;
  onRemove: (index: number) => void;
}

function RemovableListFile({
  index,
  filename,
  fileDetails,
  onRemove,
}: RemovableListFileProps) {
  const { t } = useTranslation();
  return (
    <Wrapper data-testid="pb-t-removable-list-file">
      <ItemContainer>
        <CountContainer>{index <= 9 ? "0" + index : index}</CountContainer>
        <Content>
          <div>
            <OverlineText>{t("SELECTED_FILE")}</OverlineText>
            <p>{filename}</p>
          </div>

          <div>
            <OverlineText>{t("FILE_TOTAL")}</OverlineText>
            <p>{fileDetails}</p>
          </div>
        </Content>
      </ItemContainer>
      <ActionButtonWrapper>
        <ButtonAction
          onClick={() => onRemove && onRemove(index)}
          data-testid="pb-t-removable-list-file-button"
        >
          -
        </ButtonAction>
        <SmallText>{t("REMOVE_ITEM")}</SmallText>
      </ActionButtonWrapper>
    </Wrapper>
  );
}

export default RemovableListFile;
