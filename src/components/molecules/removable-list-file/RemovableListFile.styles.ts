import { Button } from "brainui";
import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  align-items: center;
`;

export const CountContainer = styled.div`
  background: #b6b6b6;
  border-radius: 8px 0 0 8px;
  color: #fff;
  display: flex;
  align-items: flex-end;
  padding: 3px;
`;

export const ItemContainer = styled.div`
  border: 1px solid #ebebeb;
  border-radius: 8px;
  color: #707070;
  display: flex;
  width: 100%;

  small {
    font-size: 12px;
    font-weight: 400;
  }
`;

export const ButtonAction = styled(Button)`
  border-radius: 100% !important;
  font-weight: bold !important;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
  padding: 0 !important;
  height: 30px !important;
  width: 30px !important;
`;

export const SmallText = styled.p`
  font-size: 11px;
  font-weight: 300;
  line-height: 11px;
  text-align: center;
  margin-bottom: 0;
`;

export const ActionButtonWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

export const Content = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  width: 100%;
  background-color: var(--surface-color-4);

  p {
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 0;
  }
`;

export const OverlineText = styled.span`
  display: block;
  margin-bottom: 0;
  font-weight: 300;
  font-size: 12px;
  line-height: 1.5;
`;
