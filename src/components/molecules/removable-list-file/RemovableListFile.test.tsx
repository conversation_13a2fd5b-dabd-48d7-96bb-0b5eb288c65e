import { cleanup, render, screen, fireEvent } from "@testing-library/react";
import React from "react";
import RemovableListFile from "./RemovableListFile";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

afterEach(() => {
  cleanup();
});

describe("RemovableListFile component", () => {

  it("should render the component", () => {
    render(
      <RemovableListFile
        fileDetails="10 items"
        filename="test.csv"
        index={1}
        onRemove={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-removable-list-file");
    expect(component).toBeInTheDocument();
  });

  it("should display the correct filename and file details", () => {
    render(
      <RemovableListFile
        fileDetails="10 items"
        filename="test.csv"
        index={1}
        onRemove={() => {}}
      />
    );

    expect(screen.getByText("test.csv")).toBeInTheDocument();
    expect(screen.getByText("10 items")).toBeInTheDocument();
  });

  it("should call onRemove with the correct index when remove button is clicked", () => {
    const mockOnRemove = jest.fn();
    render(
      <RemovableListFile
        fileDetails="10 items"
        filename="test.csv"
        index={3}
        onRemove={mockOnRemove}
      />
    );

    const removeButton = screen.getByTestId("pb-t-removable-list-file-button");
    fireEvent.click(removeButton);

    expect(mockOnRemove).toHaveBeenCalledWith(3);
  });

  it("should display the index with leading zero when index is less than 10", () => {
    render(
      <RemovableListFile
        fileDetails="10 items"
        filename="test.csv"
        index={5}
        onRemove={() => {}}
      />
    );

    expect(screen.getByText("05")).toBeInTheDocument();
  });

  it("should display the index without leading zero when index is 10 or greater", () => {
    render(
      <RemovableListFile
        fileDetails="10 items"
        filename="test.csv"
        index={10}
        onRemove={() => {}}
      />
    );

    expect(screen.getByText("10")).toBeInTheDocument();
  });

  it("should display the correct labels", () => {
    render(
      <RemovableListFile
        fileDetails="10 items"
        filename="test.csv"
        index={1}
        onRemove={() => {}}
      />
    );

    expect(screen.getByText("SELECTED_FILE")).toBeInTheDocument();
    expect(screen.getByText("FILE_TOTAL")).toBeInTheDocument();
    expect(screen.getByText("REMOVE_ITEM")).toBeInTheDocument();
  });
});
