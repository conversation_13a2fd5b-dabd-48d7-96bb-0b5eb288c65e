import { GeometryIcon } from "../../atoms";
import { <PERSON><PERSON>, Tooltip } from "antd";
import { DeleteOutlined, ExpandOutlined } from "@ant-design/icons";
import { GeometryCardGroup, GeometryCardWrapper } from "./GeometryCard.styles";
import { PointerEventHandler, useState } from "react";
import { useTranslation } from "react-i18next";

interface GeometryCardProps {
  uid: string;
  geometry: any;
  name: string;
  selected?: boolean;
  onPointerEnter?: (uid: string) => void;
  onPointerleave?: (uid: string) => void;
  onClick?: (uid: string) => void;
  onRemove?: (uid: string) => void;
  onFocus?: (uid: string) => void;
}

function GeometryCard({
  uid,
  geometry,
  name,
  selected,
  onPointerEnter,
  onPointerleave,
  onClick,
  onRemove,
  onFocus,
}: GeometryCardProps) {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);

  const handlePointerEnter: PointerEventHandler = (_event) => {
    setIsHovered(true);
    onPointerEnter && onPointerEnter(uid);
  };

  const handlePointerLeave: PointerEventHandler = (_event) => {
    setIsHovered(false);
    onPointerleave && onPointerleave(uid);
  };

  return (
    <GeometryCardWrapper
      data-testid="pb-t-geometry-card"
      onPointerEnter={handlePointerEnter}
      onPointerLeave={handlePointerLeave}
      onClick={() => onClick && onClick(uid)}
      className={selected ? "selected" : ""}
    >
      <GeometryCardGroup>
        <GeometryIcon
          geometry={geometry}
          fill={selected || isHovered ? "#00b277" : undefined}
          onClick={() => {}}
        />
        <span>{name}</span>
      </GeometryCardGroup>
      <GeometryCardGroup>
        {onFocus && (
          <Tooltip
            title={t("FOCUS_ON_MAP")}
            overlayClassName="ar-tooltip-overlay"
          >
            <Button
              shape="circle"
              size="small"
              type="ghost"
              icon={<ExpandOutlined />}
              onClick={(event) => {
                onFocus(uid);
                event.stopPropagation();
              }}
            />
          </Tooltip>
        )}
        {onRemove && (
          <Tooltip title={t("REMOVE")} overlayClassName="ar-tooltip-overlay">
            <Button
              shape="circle"
              size="small"
              icon={<DeleteOutlined />}
              onClick={(event) => {
                onRemove(uid);
                event.stopPropagation();
              }}
            />
          </Tooltip>
        )}
      </GeometryCardGroup>
    </GeometryCardWrapper>
  );
}

export default GeometryCard;
