import styled from "styled-components";

export const GeometryCardWrapper = styled.div`
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s ease-in, box-shadow 0.2s ease-in;

  &:hover,
  &.selected {
    border-color: #00b277;
  }
`;

export const GeometryCardGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;
