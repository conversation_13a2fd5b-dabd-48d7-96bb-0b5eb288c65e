import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import GeometryCard from "./GeometryCard";

const MOCK_GEOMETRY = {
  coordinates: [
    [
      [-45.**************, -12.997222865500703],
      [-45.**************, -12.998973850770838],
      [-45.**************, -12.998973850770838],
      [-45.**************, -12.997222865500703],
      [-45.**************, -12.997222865500703],
    ],
  ],
  type: "Polygon",
};

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

afterEach(() => {
  cleanup();
});

describe("GeometryCard component", () => {
  it("should render the component", () => {
    render(<GeometryCard geometry={MOCK_GEOMETRY} name="Test" uid="1" />);
    const component = screen.queryByTestId("pb-t-geometry-card");
    expect(component).toBeInTheDocument();
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("should call onPointerEnter when the component is hovered", () => {
    const mockOnPointerEnter = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onPointerEnter={mockOnPointerEnter}
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-card")!;
    fireEvent.pointerEnter(component);
    expect(mockOnPointerEnter).toHaveBeenCalledWith("1");
  });

  it("should call onPointerleave when the pointer leaves the component", () => {
    const mockOnPointerleave = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onPointerleave={mockOnPointerleave}
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-card")!;
    fireEvent.pointerLeave(component);
    expect(mockOnPointerleave).toHaveBeenCalledWith("1");
  });

  it("should call onClick when the component is clicked", () => {
    const mockOnClick = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onClick={mockOnClick}
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-card")!;
    fireEvent.click(component);
    expect(mockOnClick).toHaveBeenCalledWith("1");
  });

  it("should apply selected class when selected prop is true", () => {
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        selected={true}
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-card")!;
    expect(component).toHaveClass("selected");
  });

  it("should render focus button when onFocus prop is provided", () => {
    const mockOnFocus = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onFocus={mockOnFocus}
      />
    );

    const focusButton = screen.getByRole("button", {
      name: /expand/i
    });
    expect(focusButton).toBeInTheDocument();

    fireEvent.click(focusButton);
    expect(mockOnFocus).toHaveBeenCalledWith("1");
  });

  it("should render remove button when onRemove prop is provided", () => {
    const mockOnRemove = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onRemove={mockOnRemove}
      />
    );

    const removeButton = screen.getByRole("button", {
      name: /delete/i
    });
    expect(removeButton).toBeInTheDocument();

    fireEvent.click(removeButton);
    expect(mockOnRemove).toHaveBeenCalledWith("1");
  });

  it("should render both focus and remove buttons when both props are provided", () => {
    const mockOnFocus = jest.fn();
    const mockOnRemove = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onFocus={mockOnFocus}
        onRemove={mockOnRemove}
      />
    );

    const focusButton = screen.getByRole("button", { name: /expand/i });
    const removeButton = screen.getByRole("button", { name: /delete/i });

    expect(focusButton).toBeInTheDocument();
    expect(removeButton).toBeInTheDocument();

    fireEvent.click(focusButton);
    expect(mockOnFocus).toHaveBeenCalledWith("1");

    fireEvent.click(removeButton);
    expect(mockOnRemove).toHaveBeenCalledWith("1");
  });

  it("should stop event propagation when clicking focus button", () => {
    const mockOnFocus = jest.fn();
    const mockOnClick = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onFocus={mockOnFocus}
        onClick={mockOnClick}
      />
    );

    const focusButton = screen.getByRole("button", { name: /expand/i });

    fireEvent.click(focusButton);

    expect(mockOnFocus).toHaveBeenCalledWith("1");

    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it("should stop event propagation when clicking remove button", () => {
    const mockOnRemove = jest.fn();
    const mockOnClick = jest.fn();
    render(
      <GeometryCard
        geometry={MOCK_GEOMETRY}
        name="Test"
        uid="1"
        onRemove={mockOnRemove}
        onClick={mockOnClick}
      />
    );

    const removeButton = screen.getByRole("button", { name: /delete/i });

    fireEvent.click(removeButton);

    expect(mockOnRemove).toHaveBeenCalledWith("1");

    expect(mockOnClick).not.toHaveBeenCalled();
  });
});
