import styled from "styled-components";

export const ReportCardWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  background: var(--surface-color-1);
  min-width: 425px;
`;

export const ReportCardHeader = styled.div`
  display: flex;
  min-height: 56px;
  border-bottom: 1px solid #f0f0f0;
  padding: 10px 24px;
  justify-content: space-between;
  gap: 10px;

  h1 {
    font-size: 1rem;
    font-weight: 500;
    margin: auto 0;
  }
`;

export const ReportCardTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  overflow: hidden;
`;

export const ReportCardContent = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 4px;
  width: 100%;
  flex-grow: 1;
  color: #5e6976;

  li,
  p {
    font-size: 14px;
    line-height: 22px;
  }
`;

export const BadgeContainer = styled.div`
  display: flex;
  gap: 4px;
`;

export const NewBadge = styled.div`
  color: #fff;
  background-color: #e80070;
  display: block;
  margin: auto 0;
  padding: 1px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 20px;
`;

export const ComingSoonBadge = styled.div`
  color: #fa8c16;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  display: block;
  margin: auto 0;
  padding: 1px 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 20px;
  text-transform: uppercase;
`;

export const FormatBadge = styled.div`
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  width: fit-content;
  padding: 1px 8px;
  font-size: 12px;
`;

export const ReportCardActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px 24px;

  .btn-disabled-request-report {
    font-weight: normal;
    border-radius: 4px;
  }
`;
