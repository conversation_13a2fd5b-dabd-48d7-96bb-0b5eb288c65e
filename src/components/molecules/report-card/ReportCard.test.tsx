import { cleanup, fireEvent, render } from "@testing-library/react";
import ReportCard from "./ReportCard";
import { ReportType } from "@/types";
import { useTranslation } from "react-i18next";
import { getFileModel } from "@/services/cards";
import { useAccountDetails } from "@/hooks/use-account-details";

jest.mock("@/services/cards");
jest.mock("@/hooks/use-account-details");

jest.mock("react-i18next");

afterEach(() => {
  cleanup();
});

describe("ReportCard component", () => {
  const getFileModelMock = jest.fn();
  beforeEach(() => {
    (useAccountDetails as jest.Mock).mockReturnValue({
      isAbleToRequestReportByReportType: jest.fn(() => ({
        isAbleToRequestSingleReport: true,
      })),
    });

    (getFileModel as jest.Mock).mockImplementation(getFileModelMock);

    (useTranslation as jest.Mock).mockReturnValue({
      t: (str: string) => `translated-${str}`,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    const props = {} as any;
    const { getByTestId, getByText, baseElement } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={[]}
        models={[]}
        isNew={false}
        commingSoon={false}
        formats={[]}
        hasPermission={true}
        onClick={() => {}}
      />
    );
    const component = getByTestId("pb-t-report-card");
    const ul = getByTestId("ul-list");

    expect(component).toBeInTheDocument();
    expect(ul.childElementCount).toEqual(0);

    expect(getByText("translated-ACTION_REQUEST_REPORT")).toBeInTheDocument();
  });

  it("should download template when click on select button", async () => {
    const props = {} as any;
    const { getByTestId, getByText } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={["value-1", "value-2"]}
        models={[{ file: "model", file_format: "pdf" }]}
        isNew={false}
        commingSoon={false}
        formats={[]}
        hasPermission={false}
        onClick={() => {}}
      />
    );

    const select = getByTestId("select-download-template").querySelector(
      "input"
    );

    fireEvent.mouseDown(select);

    const option = getByText(`.${"pdf".toUpperCase()}`);

    fireEvent.click(option);

    expect(getFileModelMock).toHaveBeenCalledWith("model", "pdf");
    expect(
      getByTestId("select-download-template").querySelector("input").value
    ).toEqual("");
  });

  it("should request report when button is clicked", () => {
    const onClickMock = jest.fn();
    const props = {} as any;

    const { getByTestId } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={["value-1", "value-2"]}
        models={[]}
        isNew={false}
        commingSoon={false}
        formats={[]}
        hasPermission={true}
        onClick={onClickMock}
      />
    );
    const button = getByTestId("button-request-report");
    fireEvent.click(button);
    expect(onClickMock).toHaveBeenCalled();
  });

  it("should render bullet point list by property list", () => {
    const props = {} as any;
    const list = ["value-1", "value-2"];
    const { getByText } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={list}
        models={[{ file: "model", file_format: "pdf" }]}
        isNew={false}
        commingSoon={false}
        formats={[]}
        hasPermission={false}
        onClick={() => {}}
      />
    );

    expect(getByText("translated-value-1")).toBeInTheDocument();
    expect(getByText("translated-value-2")).toBeInTheDocument();
  });

  it("should render comming soon badge", () => {
    const props = {} as any;

    const { getByText } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={[]}
        models={[{ file: "model", file_format: "pdf" }]}
        isNew={false}
        commingSoon={true}
        formats={[]}
        hasPermission={false}
        onClick={() => {}}
      />
    );

    expect(getByText("translated-COMING_SOON")).toBeInTheDocument();
  });

  it("should render is new badge", () => {
    const props = {} as any;

    const { getByText } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={[]}
        models={[{ file: "model", file_format: "pdf" }]}
        isNew={true}
        commingSoon={false}
        formats={[]}
        hasPermission={false}
        onClick={() => {}}
      />
    );

    expect(getByText("translated-REPORT_BADGE_NEW")).toBeInTheDocument();
  });

  it("should not permmit to request a report if is not able to request", () => {
    const props = {} as any;

    (useAccountDetails as jest.Mock).mockReturnValue({
      isAbleToRequestReportByReportType: jest.fn(() => ({
        isAbleToRequestSingleReport: false,
      })),
    });

    const { getByTestId } = render(
      <ReportCard
        {...props}
        type={ReportType.VALUATION}
        title=""
        description=""
        list={[]}
        models={[{ file: "model", file_format: "pdf" }]}
        isNew={true}
        commingSoon={false}
        formats={[]}
        hasPermission={true}
        onClick={() => {}}
      />
    );

    expect(getByTestId("button-request-report-disabled")).toBeDisabled();
  });
});
