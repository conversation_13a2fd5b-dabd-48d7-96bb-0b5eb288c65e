import { AvailableReport } from "@constants";
import { ReportType } from "@types";
import { <PERSON><PERSON>, Too<PERSON>ip, Select } from "antd";
import { useTranslation } from "react-i18next";
import {
  BadgeContainer,
  ComingSoonBadge,
  NewBadge,
  ReportCardActions,
  ReportCardContent,
  ReportCardHeader,
  ReportCardTitle,
  ReportCardWrapper,
} from "./ReportCard.styles";

import { getFileModel } from "../../../services/cards";
import { icon_lock_filled } from "../../../assets";
import { useState } from "react";
import { useAccountDetails } from "../../../hooks/use-account-details";

function ReportCard({
  type,
  title,
  description,
  list,
  isNew,
  commingSoon,
  models,
  hasPermission,
  onClick,
}: AvailableReport & {
  onClick: (type: ReportType) => void;
}) {
  const { t } = useTranslation();
  const { isAbleToRequestReportByReportType } = useAccountDetails();

  const [selected, setSelected] = useState();

  const download = async (file) => {
    const model = models.find((m) => m.file == file);

    if (model) {
      await getFileModel(model.file, model.file_format);
    }

    setSelected(null);
  };

  return (
    <ReportCardWrapper data-testid="pb-t-report-card">
      <ReportCardHeader>
        <ReportCardTitle>
          <h1>{t(title)}</h1>
          {!hasPermission && <img src={icon_lock_filled} alt="Locked" />}
        </ReportCardTitle>
        <BadgeContainer>
          {isNew && <NewBadge>{t("REPORT_BADGE_NEW")}</NewBadge>}
          {commingSoon && <ComingSoonBadge>{t("COMING_SOON")}</ComingSoonBadge>}
        </BadgeContainer>
      </ReportCardHeader>
      <ReportCardContent>
        <p>{t(description)}</p>
        <ul data-testid="ul-list">
          {list &&
            list.length > 0 &&
            list.map((item) => <li key={item}>{t(item)}</li>)}
        </ul>
      </ReportCardContent>
      <ReportCardActions>
        {!commingSoon && (
          <Select
            data-testid="select-download-template"
            placeholder={t("DOWNLOAD_TEMPLATE")}
            bordered={false}
            className="ar-select-placeholder"
            onChange={(value) => download(value)}
            value={selected}
          >
            {models &&
              models.length > 0 &&
              models.map((model) => {
                return (
                  <Select.Option
                    data-testid={`option-${model.file_format}`}
                    key={model.file}
                    value={model.file}
                  >
                    .{model.file_format.toString().toUpperCase()}
                  </Select.Option>
                );
              })}
          </Select>
        )}

        {hasPermission &&
          isAbleToRequestReportByReportType(type)
            .isAbleToRequestSingleReport && (
            <Button
              size="small"
              type="primary"
              className="ar-btn-primary"
              data-testid="button-request-report"
              onClick={() => onClick(type)}
              disabled={commingSoon}
            >
              {t("ACTION_REQUEST_REPORT")}
            </Button>
          )}

        {hasPermission &&
          !isAbleToRequestReportByReportType(type)
            .isAbleToRequestSingleReport && (
            <Tooltip title={t("INSUFICIENT_FUNDS")} placement="top">
              <div>
                <Button
                  size="small"
                  data-testid="button-request-report-disabled"
                  className="btn-disabled-request-report"
                  type="primary"
                  onClick={() => {}}
                  disabled
                >
                  {t("ACTION_REQUEST_REPORT")}
                </Button>
              </div>
            </Tooltip>
          )}

        {!hasPermission && (
          <a
            href="https://www.serasaexperian.com.br/solucoes/agro-report/#form"
            target="_blank"
          >
            <Button size="small" type="ghost" disabled={commingSoon}>
              {t("UNLOCK")}
            </Button>
          </a>
        )}
      </ReportCardActions>
    </ReportCardWrapper>
  );
}

export default ReportCard;
