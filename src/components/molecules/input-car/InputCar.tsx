import { MinusOutlined, PlusOutlined } from "@ant-design/icons";
import { searchCARCode } from "@/services/farmcheck-utils";
import { isCarValid, maskCAR, removeMaskCAR } from "@/utils/car";
import { AutoComplete, notification } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDebouncedCallback } from "use-debounce";
import { CarWrapper, ContainerButtonAction } from "./InputCar.styles";
import { CAR_CODE_LENGTH } from "@constants/properties";

interface InputCarProps {
  value?: string;
  placeholder?: string;
  canRemove?: boolean;
  canAdd?: boolean;
  disabled?: boolean;
  onAdd?: (code: string) => void;
  onRemove?: (code: string) => void;
  onChange?: (code: string) => void;
}

function InputCar({
  value,
  canRemove,
  canAdd,
  disabled,
  onAdd,
  onRemove,
  onChange,
  placeholder = "**********-E6D3395B6D274F42AE22DD56987CDD52",
}: InputCarProps) {
  const { t } = useTranslation();
  const [currentValue, setCurrentValue] = useState("");
  const [imovelCodes, setImovelCodes] = useState([]);
  const [notFoundContent, setNotFoundContent] = useState("");

  const handleFetchImovelCodes = async (data: { maskedValue: string }) => {
    if (!data.maskedValue.length) return;
    if (!isCarValid(data.maskedValue)) {
      notification.error({
        message: t("INVALID_CAR_CODE"),
      });
      setImovelCodes([]);
      return;
    }
    const response: Array<string> = await searchCARCode(data.maskedValue);
    setImovelCodes(response.map((code) => ({ label: code, value: code })));
  };

  const handleFetchImovelCodesDebounced = useDebouncedCallback(
    (data: { maskedValue: string }) => {
      handleFetchImovelCodes(data);
    },
    1000
  );

  const handleSelectCarCodeFromImovelCodes = (value) => {
    const code = imovelCodes.find((c) => c.value === value);
    setCurrentValue(code);
    onChange && onChange(code.value);
    setCurrentValue("");
  };

  const addCAR = () => {
    if (!currentValue.match(/[a-zA-Z0]{2}-\d{7}-[a-zA-Z0-9]{32}/)) {
      notification.error({
        message: t("INVALID_CAR_CODE"),
      });
      return;
    }

    onAdd && onAdd(currentValue);
    setCurrentValue("");
  };

  const removeCAR = () => {
    onRemove && onRemove(value);
  };

  const handleChangeAutoCompleteCARCode = (value: string) => {
    const code = value.replace(/\./g, "").toUpperCase();
    const masked = maskCAR(code);
    setCurrentValue(masked);
    const unmasked = removeMaskCAR(masked);
    if (unmasked.length !== CAR_CODE_LENGTH) return;
    handleFetchImovelCodes({ maskedValue: masked });
  };

  useEffect(() => {
    if (!imovelCodes.length) {
      setNotFoundContent(t("NO_DATA"));
    } else {
      setNotFoundContent("");
    }
  }, [imovelCodes]);

  useEffect(() => {
    handleFetchImovelCodesDebounced({ maskedValue: currentValue });
  }, [currentValue]);

  return (
    <CarWrapper data-testid="pb-t-input-car">
      <AutoComplete
        value={currentValue}
        onChange={handleChangeAutoCompleteCARCode}
        style={{ width: "100%" }}
        options={imovelCodes}
        onSelect={handleSelectCarCodeFromImovelCodes}
        onSearch={(value) => {
          handleFetchImovelCodesDebounced({ maskedValue: maskCAR(value) });
        }}
        placeholder={placeholder}
        notFoundContent={notFoundContent}
        disabled={canRemove || disabled}
        data-testid="pb-t-input-car-autocomplete"
      />
      {canRemove && (
        <ContainerButtonAction
          style={{ backgroundColor: "#ed7669" }}
          onClick={() => removeCAR()}
          data-testid="button-remove-car"
        >
          <MinusOutlined style={{ color: "#fff", fontSize: "14px" }} />
        </ContainerButtonAction>
      )}
      {!canRemove && canAdd && (
        <ContainerButtonAction
          style={{ backgroundColor: "#00B277" }}
          onClick={() => addCAR()}
          data-testid="button-add-car"
        >
          <PlusOutlined style={{ color: "#fff", fontSize: "14px" }} />
        </ContainerButtonAction>
      )}
    </CarWrapper>
  );
}

export default InputCar;
