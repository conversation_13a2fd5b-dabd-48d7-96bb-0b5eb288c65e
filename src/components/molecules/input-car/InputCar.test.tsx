import { cleanup, fireEvent, render, waitFor } from "@testing-library/react";
import InputCar from "./InputCar";
import { searchCARCode } from "@/services/farmcheck-utils";
import { useDebouncedCallback } from "use-debounce";
import { isCarValid } from "@/utils/car";
import { notification } from "antd";

jest.mock("@/utils/car", () => {
  return {
    ...jest.requireActual("@/utils/car"),
    isCarValid: jest.fn(),
  };
});

jest.mock("use-debounce");
jest.mock("@/services/farmcheck-utils");

jest.mock("antd", () => {
  return {
    ...jest.requireActual("antd"),
    notification: { error: jest.fn() },
  };
});

describe("InputCar", () => {
  const notificationErrorMock = jest.fn();
  const searchCarCodeMock = jest.fn();
  beforeEach(() => {
    (searchCARCode as jest.Mock).mockImplementation(searchCarCodeMock);
    (notification.error as jest.Mock).mockImplementation(notificationErrorMock);

    (isCarValid as jest.Mock).mockReturnValue(false);

    (useDebouncedCallback as jest.Mock).mockImplementation(
      (func: Function, num: number) => {
        return (data) => {
          func(data);
        };
      }
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", async () => {
    const { getByTestId } = render(
      <InputCar
        value={""}
        canRemove={false}
        canAdd={false}
        disabled={false}
        onAdd={() => {}}
        onRemove={() => {}}
        onChange={() => {}}
        placeholder={"UF-1302405-E6D3395B6D274F42AE22DD56987CDD52"}
      />
    );

    const autoComplete = getByTestId("pb-t-input-car-autocomplete");

    expect(autoComplete).toBeInTheDocument();
  });

  it("should render a notification if car code is invalid", async () => {
    const { getByTestId } = render(
      <InputCar
        value={""}
        canRemove={false}
        canAdd={false}
        disabled={false}
        onAdd={() => {}}
        onRemove={() => {}}
        onChange={() => {}}
        placeholder={"UF-1302405-E6D3395B6D274F42AE22DD56987CDD52"}
      />
    );

    const autoComplete = getByTestId(
      "pb-t-input-car-autocomplete"
    ).querySelector("input");

    fireEvent.click(autoComplete);
    fireEvent.change(autoComplete, {
      target: { value: "invalid-value" },
    });

    await waitFor(() => {
      expect(notificationErrorMock).toHaveBeenCalled();
    });
  });

  it("should search car code on farmcheck utils if code is valid", async () => {
    (isCarValid as jest.Mock).mockReturnValue(true);
    const carCode = "UF-1302405-E6D3395B6D274F42AE22DD56987CDD52";
    searchCarCodeMock.mockReturnValue([carCode]);

    const { getByTestId } = render(
      <InputCar
        value={""}
        canRemove={false}
        canAdd={false}
        disabled={false}
        onAdd={() => {}}
        onRemove={() => {}}
        onChange={() => {}}
      />
    );

    const autoComplete = getByTestId(
      "pb-t-input-car-autocomplete"
    ).querySelector("input");

    fireEvent.click(autoComplete);
    fireEvent.change(autoComplete, {
      target: { value: carCode },
    });

    await waitFor(() => {
      expect(searchCarCodeMock).toHaveBeenCalled();
    });
  });

  it("should call add car function when button is clicked", () => {
    const onAddMock = jest.fn();
    const { getByTestId } = render(
      <InputCar
        value={""}
        canRemove={false}
        canAdd={true}
        disabled={false}
        onAdd={onAddMock}
        onRemove={() => {}}
        onChange={() => {}}
      />
    );
    const carCode = "UF-1302405-E6D3395B6D274F42AE22DD56987CDD52";
    const button = getByTestId("button-add-car");
    const autoComplete = getByTestId(
      "pb-t-input-car-autocomplete"
    ).querySelector("input");

    fireEvent.click(autoComplete);
    fireEvent.change(autoComplete, {
      target: { value: carCode },
    });

    fireEvent.click(button);
    expect(onAddMock).toHaveBeenCalled();
  });

  it("should show a notification if car is invalid when add car button is clicked", () => {
    const { getByTestId } = render(
      <InputCar
        value={""}
        canRemove={false}
        canAdd={true}
        disabled={false}
        onAdd={() => {}}
        onRemove={() => {}}
        onChange={() => {}}
      />
    );

    const autoComplete = getByTestId(
      "pb-t-input-car-autocomplete"
    ).querySelector("input");

    fireEvent.click(autoComplete);
    fireEvent.change(autoComplete, {
      target: { value: "invalid-value" },
    });

    const button = getByTestId("button-add-car");

    fireEvent.click(button);

    expect(notificationErrorMock).toHaveBeenCalled();
  });

  it("should call remove car function when button is clicked", () => {
    const onRemoveMock = jest.fn();
    const { getByTestId } = render(
      <InputCar
        value={""}
        canRemove={true}
        canAdd={true}
        disabled={false}
        onAdd={() => {}}
        onRemove={onRemoveMock}
        onChange={() => {}}
      />
    );

    const button = getByTestId("button-remove-car");

    fireEvent.click(button);
    expect(onRemoveMock).toHaveBeenCalled();
  });
});
