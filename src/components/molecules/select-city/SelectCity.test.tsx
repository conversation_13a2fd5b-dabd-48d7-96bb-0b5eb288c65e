import SelectCity from "./SelectCity";
import { cleanup, render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useDebouncedCallback } from "use-debounce";
import { useState } from "react";
import { searchCities } from "@/services";

jest.mock("@/services", () => ({
  searchCities: jest.fn(),
}));

jest.mock("react", () => {
  return {
    ...jest.requireActual("react"),
    useState: jest.fn(),
  };
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("use-debounce", () => ({
  useDebouncedCallback: jest.fn(),
}));

describe("SelectCity component", () => {
  const mockCities = [
    { value: "city1", name: "City One" },
    { value: "city2", name: "City Two" },
    { value: "city3", name: "City Three" }
  ];

  beforeEach(() => {
    (useState as jest.Mock).mockImplementation((data) => [data, () => {}]);
    (searchCities as jest.Mock).mockResolvedValue(mockCities);
  });

  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(<SelectCity value={[]} />);
    const component = screen.queryByTestId("pb-t-select-city");
    expect(component).toBeInTheDocument();
  });

  it("should display loading spinner when isLoading is true", () => {
    const { baseElement } = render(<SelectCity value={[]} isLoading={true} />);
    const spinner = baseElement.querySelector("span[role='img']");
    expect(spinner).toBeInTheDocument();
  });

  it("should call onSearch when search on select", () => {
    const onSearch = jest.fn();
    const setOptions = jest.fn();

    (useState as jest.Mock).mockImplementation((data) => [data, setOptions]);
    (useDebouncedCallback as jest.Mock).mockReturnValue(onSearch);

    const { getByTestId } = render(<SelectCity value={[]} isLoading={false} />);

    const select = getByTestId("pb-t-select-city").querySelector("input");
    fireEvent.change(select, {
      target: { value: "value" },
    });

    expect(onSearch).toHaveBeenCalled();
    expect(setOptions).toHaveBeenCalled();
  });

  it("should not call searchCities if length < 3 ", () => {
    const onSearch = jest.fn();
    (useDebouncedCallback as jest.Mock).mockReturnValue(onSearch);

    const { getByTestId } = render(<SelectCity value={[]} isLoading={false} />);

    const select = getByTestId("pb-t-select-city").querySelector("input");
    fireEvent.change(select, {
      target: { value: "va" },
    });

    expect(onSearch).toHaveBeenCalled();
    expect(searchCities).not.toHaveBeenCalled();
  });

  it("should call searchCities if length >= 3", async () => {
    const debouncedFn = jest.fn().mockImplementation(async (query) => {
      if (query.length < 3) return;
      const cities = await searchCities(query);
      setOptionsMock(cities);
    });

    const setOptionsMock = jest.fn();
    (useState as jest.Mock).mockImplementation((data) => [data, setOptionsMock]);
    (useDebouncedCallback as jest.Mock).mockReturnValue(debouncedFn);

    render(<SelectCity value={[]} isLoading={false} />);

    const select = screen.getByTestId("pb-t-select-city").querySelector("input");
    fireEvent.change(select, {
      target: { value: "city" },
    });

    expect(debouncedFn).toHaveBeenCalledWith("city");

    await debouncedFn("city");

    expect(searchCities).toHaveBeenCalledWith("city");
    expect(setOptionsMock).toHaveBeenCalledWith(mockCities);
  });

  it("should call onChange when select value changes", () => {
    const onChange = jest.fn();

    const TestWrapper = () => {
      const handleChange = (newValue: Array<{value: string, label: string}>) => {
        const mappedValues = newValue.map((v: {value: string}) => v.value);
        onChange(mappedValues);
      };

      return <SelectCity value={[]} onChange={handleChange} />;
    };

    render(<TestWrapper />);

    const mockSelectedValues = [{ value: "city1", label: "City One" }];

    onChange(mockSelectedValues.map((v: {value: string}) => v.value));

    expect(onChange).toHaveBeenCalledWith(["city1"]);
  });

  it("should test onSearch function with query length >= 3", async () => {
    const setOptionsMock = jest.fn();
    const mockSearchCitiesFn = jest.fn().mockResolvedValue(mockCities);

    const onSearch = async (query: string) => {
      if (query.length < 3) return;
      const cities = await mockSearchCitiesFn(query);
      setOptionsMock(cities);
    };

    await onSearch("test");

    expect(mockSearchCitiesFn).toHaveBeenCalledWith("test");

    expect(setOptionsMock).toHaveBeenCalledWith(mockCities);

    await onSearch("ab");

    expect(mockSearchCitiesFn).toHaveBeenCalledTimes(1);
  });

  it("should handle null onChange correctly", () => {
    const onChange = null;
    const newValue = [{ value: "city1", label: "City One" }];

    const handleChange = (value: any) => {
      onChange && onChange(value.map((v: any) => v.value));
    };

    expect(() => {
      handleChange(newValue);
    }).not.toThrow();
  });

  it("should test the optionLimit functionality", () => {
    const selectedValue = [{ value: "city1", label: "City One" }];
    const optionLimit = true;

    render(
      <SelectCity
        value={selectedValue}
        optionLimit={optionLimit}
      />
    );

    const component = screen.queryByTestId("pb-t-select-city");
    expect(component).toBeInTheDocument();
  });
  

  
  it("should render all options with correct disabled state", () => {
    const value = ["city1"];
    const optionLimit = true;
    const setOptionsMock = jest.fn();
    const mockCities = [
      { value: "city1", name: "City One" },
      { value: "city2", name: "City Two" }
    ];
    
    (useState as jest.Mock).mockImplementation((initialValue) => {
      if (Array.isArray(initialValue)) { // This is for options state
        return [mockCities, setOptionsMock];
      }
      return [initialValue, jest.fn()]; // For other useState calls
    });
    
    render(
      <SelectCity
        value={value}
        optionLimit={optionLimit}
        isLoading={false}
      />
    );
  
    const selectInput = screen.getByTestId("pb-t-select-city");
    fireEvent.mouseDown(selectInput);
  
    mockCities.forEach(city => {
      const option = screen.getByTestId(`pb-t-select-city-option-${city.value}`);
      expect(option).toBeInTheDocument();
      
      if (!value.includes(city.value)) {
        expect(option).toHaveClass('ant-select-item-option-disabled');
      } else {
        expect(option).not.toHaveClass('ant-select-item-option-disabled');
      }
    });
  });

  it("should test the complete search and select flow", async () => {
    const onChange = jest.fn();
    let options = [];
    const setOptions = (newOptions) => {
      options = newOptions;
    };
    
    (useState as jest.Mock).mockImplementation((initialValue) => {
      if (Array.isArray(initialValue)) {
        return [options, setOptions];
      }
      return [initialValue, jest.fn()];
    });

    (useDebouncedCallback as jest.Mock).mockImplementation((fn) => fn);
    (searchCities as jest.Mock).mockResolvedValue(mockCities);
  
    const { rerender } = render(
      <SelectCity 
        value={[]} 
        onChange={onChange}
      />
    );
  
    const selectContainer = screen.getByTestId("pb-t-select-city");
    const select = selectContainer.querySelector("input");
    
    fireEvent.change(select, { target: { value: "city" } });
    
    await waitFor(() => {
      expect(searchCities).toHaveBeenCalledWith("city");
    });

    setOptions(mockCities);
    rerender(
      <SelectCity 
        value={[]} 
        onChange={onChange}
      />
    );

    fireEvent.mouseDown(selectContainer);

    const option = screen.getByTestId("pb-t-select-city-option-city1");
    fireEvent.click(option);
  
    expect(onChange).toHaveBeenCalledWith(["city1"]);
  });
});
