import { searchCities } from "@services";
import { Select, Spin } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDebouncedCallback } from "use-debounce";
import { SelectCityWrapper } from "./SelectCity.styles";

interface SelectCityProps {
  value: any[];
  optionLimit?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  size?: "large" | "middle" | "small";
  onChange?: (cities) => void;
}

function SelectCity({
  value,
  optionLimit,
  isLoading,
  disabled,
  placeholder,
  size,
  onChange,
}: SelectCityProps) {
  const { t } = useTranslation();
  const [options, setOptions] = useState([]);

  const onSearch = useDebouncedCallback(async (query: string) => {
    if (query.length < 3) return;
    const cities = await searchCities(query);
    setOptions(cities);
  }, 800);

  return (
    <SelectCityWrapper
      data-testid="pb-t-select-city"
      mode="multiple"
      value={value}
      labelInValue
      loading={isLoading}
      onSearch={onSearch}
      onChange={(newValue: Array<any>) => {
        onChange && onChange(newValue.map((v) => v.value));
      }}
      placeholder={placeholder || t("TYPE_CITY_NAME")}
      notFoundContent={
        isLoading ? <Spin size="small" data-testid="loading-component" /> : null
      }
      filterOption={false}
      disabled={disabled}
      size={size || "small"}
    >
      {options.map((city) => (
        <Select.Option
          data-testid={`pb-t-select-city-option-${city.value}`}
          key={city.value}
          label={city.name}
          value={city.value}
          disabled={optionLimit && !value.includes(city.value)}
        >
          {city.name}
        </Select.Option>
      ))}
    </SelectCityWrapper>
  );
}

export default SelectCity;
