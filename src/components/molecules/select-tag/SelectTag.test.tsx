import { cleanup } from "@testing-library/react";
import { v4 as uuidv4 } from "uuid";
import { useState } from "react";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("uuid", () => ({
  v4: jest.fn(() => "new-uuid"),
}));

jest.mock("react", () => {
  const originalReact = jest.requireActual("react");
  return {
    ...originalReact,
    useState: jest.fn(),
  };
});

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe("SelectTag component", () => {
  const mockTags = [
    { id: "tag1", name: "javascript" },
    { id: "tag2", name: "react" },
    { id: "tag3", name: "typescript" }
  ];

  const mockSetSearchText = jest.fn();
  const mockSetOptions = jest.fn();

  beforeEach(() => {
    (useState as jest.Mock).mockImplementation((initialValue) => {
      if (initialValue === "") {
        return ["", mockSetSearchText];
      }
      return [mockTags, mockSetOptions];
    });
  });

  it("should add a new tag to options when search text is not found in tags", () => {
    const onSearch = (search: string) => {
      if (!mockTags.find((t) => t.name === search)) {
        mockSetOptions([...mockTags, { id: uuidv4(), name: search }]);
      }
      mockSetSearchText(search);
    };

    const newTagName = "newTag";
    onSearch(newTagName);

    expect(mockSetOptions).toHaveBeenCalledWith([
      ...mockTags,
      { id: "new-uuid", name: newTagName }
    ]);

    expect(mockSetSearchText).toHaveBeenCalledWith(newTagName);
  });

  it("should not add a new tag to options when search text is found in tags", () => {
    const onSearch = (search: string) => {
      if (!mockTags.find((t) => t.name === search)) {
        mockSetOptions([...mockTags, { id: uuidv4(), name: search }]);
      }
      mockSetSearchText(search);
    };

    const existingTagName = "react";
    onSearch(existingTagName);

    expect(mockSetOptions).not.toHaveBeenCalled();

    expect(mockSetSearchText).toHaveBeenCalledWith(existingTagName);
  });

  it("should call onChange with mapped values when onChange is provided", () => {
    const mockOnChange = jest.fn();

    const onChange = (newValue: Array<any>) => {
      mockOnChange && mockOnChange(newValue.map((v) => v.value));
    };

    const mockNewValue = [
      { value: "javascript" },
      { value: "react" }
    ];
    onChange(mockNewValue);

    expect(mockOnChange).toHaveBeenCalledWith(["javascript", "react"]);
  });

  it("should not throw error when onChange is not provided", () => {
    const onChange = (newValue: Array<any>) => {
      const mockOnChange = undefined;
      mockOnChange && mockOnChange(newValue.map((v) => v.value));
    };

    const mockNewValue = [
      { value: "javascript" },
      { value: "react" }
    ];

    expect(() => {
      onChange(mockNewValue);
    }).not.toThrow();
  });

  it("should use provided placeholder or default", () => {
    const customPlaceholder = "Custom Placeholder";
    const defaultPlaceholder = "TAG_INPUT_HINT";
    const t = (key: string) => key;

    const placeholderWithCustom = customPlaceholder || t(defaultPlaceholder);
    expect(placeholderWithCustom).toBe(customPlaceholder);

    const placeholderWithDefault = t(defaultPlaceholder);
    expect(placeholderWithDefault).toBe(defaultPlaceholder);
  });

  it("should filter options based on searchText and minimum length", () => {
    const filterAndMapOptions = (options: any[], searchText: string) => {
      return options
        .filter((t) => t.name.length >= 3 && t.name.includes(searchText))
        .map((tag) => ({
          key: tag.id,
          label: tag.name,
          value: tag.name
        }));
    };

    const emptySearchResult = filterAndMapOptions(mockTags, "");
    expect(emptySearchResult.length).toBe(3); // All 3 mock tags have length >= 3

    const matchingSearchResult = filterAndMapOptions(mockTags, "script");
    expect(matchingSearchResult.length).toBe(2); // "javascript" and "typescript" contain "script"
    expect(matchingSearchResult[0].value).toBe("javascript");
    expect(matchingSearchResult[1].value).toBe("typescript");

    const nonMatchingSearchResult = filterAndMapOptions(mockTags, "python");
    expect(nonMatchingSearchResult.length).toBe(0);

    const shortTagsResult = filterAndMapOptions([...mockTags, { id: "tag4", name: "js" }], "");
    expect(shortTagsResult.length).toBe(3); // "js" is filtered out because length < 3
  });
});
