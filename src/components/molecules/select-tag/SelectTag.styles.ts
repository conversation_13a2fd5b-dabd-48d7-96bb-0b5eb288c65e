import { Select } from "antd";
import styled from "styled-components";

export const SelectTagWrapper = styled(Select)`
  width: 100%;

  .ant-select-selector {
    padding: 0 !important;
    min-height: 32px;
  }

  .ant-select-selection-overflow {
    padding: 4.5px !important;
    height: 100%;
    gap: 5px;
  }

  .ant-select-selection-overflow-item,
  .ant-select-selection-search,
  .ant-slect .ant-select-selection-item,
  input {
    height: 100% !important;
  }

  .ant-select-selection-item {
    height: 18px !important;
    line-height: 16px !important;
    margin: 0 !important;
  }
`;
