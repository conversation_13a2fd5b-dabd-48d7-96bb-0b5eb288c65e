import { Tag } from "@types";
import { Select, Spin } from "antd";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";
import { SelectTagWrapper } from "./SelectTag.styles";

interface SelectCityProps {
  value: any[];
  tags: Tag[];
  optionLimit?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  onChange?: (tags: string[]) => void;
}

function SelectTag({
  tags,
  value,
  optionLimit,
  isLoading,
  disabled,
  placeholder,
  onChange,
}: SelectCityProps) {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState("");
  const [options, setOptions] = useState([]);

  useEffect(() => {
    setOptions(tags);
  }, []);

  return (
    <SelectTagWrapper
      data-testid="pb-t-select-tag"
      mode="multiple"
      value={value}
      labelInValue
      loading={isLoading}
      onSearch={(search) => {
        if (!tags.find((t) => t.name == search)) {
          setOptions([...tags, { id: uuidv4(), name: search }]);
        }
        setSearchText(search);
      }}
      onChange={(newValue: Array<any>) => {
        onChange && onChange(newValue.map((v) => v.value));
      }}
      placeholder={placeholder || t("TAG_INPUT_HINT")}
      notFoundContent={isLoading ? <Spin size="small" /> : null}
      filterOption={false}
      disabled={disabled}
    >
      {options
        .filter((t) => t.name.length >= 3 && t.name.includes(searchText))
        .map((tag) => (
          <Select.Option
            key={tag.id}
            label={tag.name}
            value={tag.name}
            data-testid={`pb-t-select-tag-option-${tag.name}`}
            disabled={optionLimit && !value.includes(tag.name)}
          >
            {tag.name}
          </Select.Option>
        ))}
    </SelectTagWrapper>
  );
}

export default SelectTag;
