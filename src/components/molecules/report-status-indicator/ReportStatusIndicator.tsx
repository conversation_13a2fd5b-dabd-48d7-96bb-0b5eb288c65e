import {
  REPORT_ERROR_LABELS,
  REPORT_STATUS_LABELS,
  ReportError,
  ReportGenericError,
  ReportStatus,
} from "@types";
import { getReportStatusColor } from "@utils";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import {
  Indicator<PERSON>ontainer,
  IndicatorFill,
  IndicatorWrapper,
} from "./ReportStatusIndicator.styles";

const PROGRESS_BY_STATUS = {
  [ReportStatus.PENDING]: 50,
  [ReportStatus.PROCESSING]: 50,
  [ReportStatus.ERROR]: 20,
  [ReportStatus.WARNING]: 50,
  [ReportStatus.DONE]: 100,
};

function ReportStatusIndicator({
  status,
  errorMessage,
}: {
  readonly status: ReportStatus;
  readonly errorMessage: null | Record<ReportError, string>;
}) {
  const { t } = useTranslation();
  const errorTooltip = Object.keys(errorMessage || {})
    .map((key) => {
      if (key in ReportGenericError) return t(REPORT_ERROR_LABELS[key]);
      return `${t(key.toUpperCase())}: ${t(
        "INVALID_OR_NONEXISTENT_PARAMETER"
      )}`;
    })
    .join("\n");

  return (
    <IndicatorWrapper data-testid="pb-t-report-status-indicator">
      {status == ReportStatus.ERROR && (
        <Tooltip title={errorTooltip} overlayStyle={{ whiteSpace: "pre-line" }}>
          <p data-testid="pb-p-report-status">
            {t(REPORT_STATUS_LABELS[status])}
          </p>
        </Tooltip>
      )}
      {status != ReportStatus.ERROR && <p>{t(REPORT_STATUS_LABELS[status])}</p>}
      <IndicatorContainer>
        <IndicatorFill
          style={{
            background: getReportStatusColor(status),
            height: `${PROGRESS_BY_STATUS[status]}%`,
          }}
        />
      </IndicatorContainer>
    </IndicatorWrapper>
  );
}

export default ReportStatusIndicator;
