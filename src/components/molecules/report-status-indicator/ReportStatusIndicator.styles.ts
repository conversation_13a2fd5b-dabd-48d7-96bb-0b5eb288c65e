import styled from "styled-components";

export const IndicatorWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;

  p {
    text-align: end;
    margin: 0;
    line-height: 1.2;
  }
`;

export const IndicatorContainer = styled.div`
  width: 10px;
  height: 34px;
  background: #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  border: 1px solid #d9d9d9;
`;

export const IndicatorFill = styled.div`
  width: 100%;
  background: var(--antd-wave-shadow-color);
  position: absolute;
  bottom: 0;
`;
