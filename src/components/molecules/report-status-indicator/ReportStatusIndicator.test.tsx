import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import ReportStatusIndicator from "./ReportStatusIndicator";
import { REPORT_STATUS_LABELS, ReportStatus } from "@types";
import { useTranslation } from "react-i18next";

jest.mock("react-i18next");

describe("ReportStatusIndicator component", () => {
  const translationMock = jest.fn();
  beforeEach(() => {
    (useTranslation as jest.Mock).mockReturnValue({
      t: translationMock,
    });
  });
  it("should render the component", () => {
    render(
      <ReportStatusIndicator errorMessage={null} status={ReportStatus.ERROR} />
    );
    const component = screen.queryByTestId("pb-t-report-status-indicator");
    expect(component).toBeInTheDocument();
  });

  it("should render status indicator correctly if status is error", () => {
    render(
      <ReportStatusIndicator
        errorMessage={{} as any}
        status={ReportStatus.ERROR}
      />
    );
    expect(translationMock).toHaveBeenCalledWith(
      REPORT_STATUS_LABELS[ReportStatus.ERROR]
    );
  });

  it("should render error tooltip according with error message prop", () => {
    const expected = "NOT_FOUND_GEOM";
    translationMock.mockReturnValue(expected);
    const { getByText } = render(
      <ReportStatusIndicator
        errorMessage={
          {
            not_found_geom: "not_found_geom",
          } as any
        }
        status={ReportStatus.ERROR}
      />
    );
    expect(getByText(expected)).toBeInTheDocument();
  });

  it("should render error tooltip according with error message prop with not expected key", async () => {
    translationMock.mockImplementation((text) => text);
    const { getByText, getByTestId } = render(
      <ReportStatusIndicator
        errorMessage={
          {
            not_expected_error: "not_expected_error",
          } as any
        }
        status={ReportStatus.ERROR}
      />
    );
    const wrapper = getByTestId("pb-p-report-status");
    fireEvent.mouseEnter(wrapper);

    await waitFor(() => {
      expect(
        getByText("NOT_EXPECTED_ERROR: INVALID_OR_NONEXISTENT_PARAMETER")
      ).toBeInTheDocument();
    });
  });

  it("should render if status is different of error", () => {
    render(
      <ReportStatusIndicator
        errorMessage={null}
        status={ReportStatus.WARNING}
      />
    );

    expect(translationMock).toHaveBeenCalledWith(
      REPORT_STATUS_LABELS[ReportStatus.WARNING]
    );
  });
});
