import { getAutomaticFields } from "@services";
import { AutomaticFieldResponse } from "@types";
import { Spin } from "antd";
import { <PERSON><PERSON> } from "brainui";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ActionWrapper, InfoText } from "./AutoFieldsTab.styles";

interface AutoFieldsTabProps {
  propertyId: string | number;
  onCreateFields: (fields: AutomaticFieldResponse[]) => void;
}

function AutoFieldsTab({ propertyId, onCreateFields }: AutoFieldsTabProps) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  async function createAutomaticFields() {
    setIsLoading(true);
    const data = await getAutomaticFields(propertyId);
    onCreateFields(data);
    setIsLoading(false);
  }

  return (
    <div data-testid="pb-t-auto-fields">
      <ActionWrapper>
        <Button onClick={createAutomaticFields} disabled={isLoading}>
          {t("CREATE_AUTOMATIC_SUBAREAS")}
        </Button>
        {isLoading && <Spin size="small" />}
      </ActionWrapper>
      <InfoText>{t("INFO_AUTOMATIC_SUBAREAS")}</InfoText>
    </div>
  );
}

export default AutoFieldsTab;
