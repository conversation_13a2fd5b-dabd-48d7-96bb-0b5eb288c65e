import {
  cleanup,
  render,
  screen,
  fireEvent,
  waitFor,
} from "@testing-library/react";
import React from "react";
import AutoFieldsTab from "./AutoFieldsTab";

jest.mock("../../../services/fields", () => ({
  getAutomaticFields: async () => {
    return 1;
  },
}));

afterEach(() => {
  cleanup();
});

describe("AutoFieldsTab component", () => {
  it("should render the component", () => {
    render(
      <AutoFieldsTab propertyId={0} onCreateFields={() => {}}></AutoFieldsTab>
    );
    const component = screen.queryByTestId("pb-t-auto-fields");
    expect(component).toBeInTheDocument();
  });
  it("should call onCreateFields when the button is pressed", async () => {
    const mockCreateFields = jest.fn();
    render(
      <AutoFieldsTab
        propertyId={0}
        onCreateFields={mockCreateFields}
      ></AutoFieldsTab>
    );
    const component = screen.queryByTestId("pb-t-auto-fields")!;
    fireEvent.click(component.querySelector("button")!);
    await waitFor(() => {
      expect(mockCreateFields).toHaveBeenCalled();
    });
  });
});
