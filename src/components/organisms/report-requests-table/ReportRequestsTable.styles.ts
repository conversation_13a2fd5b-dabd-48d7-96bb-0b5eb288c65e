import { Tag } from "antd";
import { Table } from "brainui";
import styled from "styled-components";

export const ContainerLoading = styled.div<{ isLoading: boolean }>`
  opacity: ${({ isLoading }) => (isLoading ? 1 : 0)};
  right: 0;
  top: 0;
  width: 60px;
  position: absolute;
  height: 28px;
  background: rgb(243, 243, 243);
  z-index: 2;
  padding: 0 8px;
  transition: opacity 0.3s ease;

  span {
    width: 24px;
    height: 24px;
    animation: rotation 1s linear infinite;
    border: 3px solid #fff;
    border-bottom-color: rgb(0, 178, 119);
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 0.6s linear infinite;

    @keyframes rotation {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }
`;

export const ReportRequestsTableWrapper = styled.div`
  padding: 16px 24px;
`;

export const ColumnCaption = styled.p`
  font-size: 12px;
  font-weight: 300;
  line-height: 1.5;
  margin: 0;
`;

export const ColumnValue = styled.p`
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
`;

export const ColumnAlignRight = styled.div`
  text-align: right;
`;

export const ExpandedRowIndicator = styled.div`
  border-left: 8px solid grey;
`;

export const RequestTable = styled(Table)`
  td,
  th {
    padding: 12px 16px !important;
  }

  tr.ant-table-expanded-row > td:first-child {
    padding: 0px 0px 0px 50.5px !important;
  }
`;

export const RequestTag = styled(Tag)`
  margin: 2px;
`;
