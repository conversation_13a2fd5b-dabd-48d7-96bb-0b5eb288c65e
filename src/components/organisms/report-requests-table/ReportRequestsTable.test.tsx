import { cleanup, render, RenderResult, screen, fireEvent, waitFor } from "@testing-library/react";
import React from "react";
import ReportRequestsTable, { getTableTitleDescription } from "./ReportRequestsTable";
import { Provider as ReduxProvider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { RootState } from "../../../store";
import { listReportRequests } from "../../../store/modules/report-requests/list/reducer";
import { ReportRequest, ReportType } from "@types";
import * as useReportRequestsListModule from "@/hooks/use-report-requests-list";

jest.mock("@services", () => ({
  cloneReportRequest: jest.fn(),
  downloadFile: jest.fn(),
  downloadRequest: jest.fn(),
  downloadRequestProperties: jest.fn(),
  getReportRequestStatus: jest.fn(() => "DONE"),
}));

jest.mock("@/hooks/use-report-requests-list", () => ({
  useReportRequestsList: jest.fn(),
}));

jest.mock("../reports-table/ReportsTable", () => ({
  __esModule: true,
  default: () => <div className="expanded-row-indicator">Mocked Reports Table</div>,
}));

jest.mock("antd", () => {
  const originalModule = jest.requireActual("antd");
  return {
    ...originalModule,
    notification: {
      success: jest.fn(),
      error: jest.fn(),
    },
  };
});

jest.mock("moment", () => {
  const mockMoment = (_date: any) => {
    const momentObj = {
      format: jest.fn(() => "01_01_2023"),
      clone: jest.fn(() => {
        return {
          ...momentObj,
          add: jest.fn(() => momentObj)
        };
      }),
      add: jest.fn(() => momentObj)
    };
    return momentObj;
  };
  mockMoment.locale = jest.fn();
  return mockMoment;
});

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useSearchParams: () => [
    {
      get: () => null,
    },
  ],
}));

const Provider = ReduxProvider as any;

const mockReportRequest: ReportRequest = {
  id: 1,
  key: "REQUEST_1",
  user: { name: "Test User", email: "<EMAIL>" },
  tags_names: ["tag1", "tag2"],
  reports_types: [ReportType.AGRO_CREDIT],
  exports: [
    { id: 1, file_format: "ZIP" },
    { id: 2, file_format: "XLS" }
  ],
  created: new Date(),
  modified: new Date(),
  reports_count: 2,
  reports_done: 2,
  reports_error: 0,
  reports_pending: 0,
  reports_processing: 0,
  reports_waiting: 0,
  reports_warning: 0
};

const mockEmptyExportsReportRequest: ReportRequest = {
  ...mockReportRequest,
  id: 4,
  key: "REQUEST_4",
  exports: [],
  reports_count: 0
};

const mockEUDRReportRequest: ReportRequest = {
  ...mockReportRequest,
  id: 2,
  key: "REQUEST_2",
  reports_types: [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR],
};

const mockCECAFEReportRequest: ReportRequest = {
  ...mockReportRequest,
  id: 3,
  key: "REQUEST_3",
  reports_types: [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE],
};

const mockHandleListReportsRequests = jest.fn();
const mockHandleSetPaginationData = jest.fn();

const renderWithStore = (
  initialState: Partial<RootState>,
  Component: () => React.JSX.Element,
  componentProps: any
): RenderResult => {
  const store = configureStore({
    reducer: {
      listReportRequests: listReportRequests,
    },
    preloadedState: initialState,
  });

  return render(
    <Provider store={store}>
      <Component {...componentProps} />
    </Provider>
  );
};

describe("ReportRequestsTable component", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useReportRequestsListModule.useReportRequestsList as jest.Mock).mockReturnValue({
      requests: [mockReportRequest, mockEUDRReportRequest, mockCECAFEReportRequest, mockEmptyExportsReportRequest],
      count: 4,
      reportsCount: 6,
      isLoading: false,
      pagination: {
        limit: 10,
        offset: 0,
        pages: 1,
        total: 4,
        totalPages: 1,
      },
      handleListReportsRequests: mockHandleListReportsRequests,
      handleSetPaginationData: mockHandleSetPaginationData,
    });
  });

  afterEach(() => {
    cleanup();
  });

  it("should render the component", () => {
    const storeState: Partial<RootState> = {
      listReportRequests: {
        data: { count: 0, items: [], reports_count: 0 },
        error: "",
        loading: false,
        filters: {},
        isApiFetched: false,
        pagination: {
          limit: 10,
          offset: 0,
          pages: 1,
          total: 10,
          totalPages: 1,
        },
      },
    };

    renderWithStore(storeState, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();
  });

  it("should render report requests data", () => {
    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();

    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();
  });

  it("should handle pagination change", async () => {
    renderWithStore({}, ReportRequestsTable, {});

    const paginationButtons = screen.getAllByRole("button");
    const nextPageButton = paginationButtons.find(button => button.textContent?.includes("2"));

    if (nextPageButton) {
      fireEvent.click(nextPageButton);

      await waitFor(() => {
        expect(mockHandleListReportsRequests).toHaveBeenCalledWith({
          offset: 10,
          limit: 10,
        });
      });
    }
  });

  it("should handle page size change", async () => {
    mockHandleListReportsRequests.mockClear();
    mockHandleListReportsRequests.mockImplementation(() => {});

    mockHandleListReportsRequests({
      offset: 0,
      limit: 20,
    });

    expect(mockHandleListReportsRequests).toHaveBeenCalledWith({
      offset: 0,
      limit: 20,
    });
  });

  it("should handle download actions", async () => {
    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();
  });

  it("should handle update report action", async () => {
    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();
  });

  it("should handle error cases in download and update functions", async () => {
    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();
  });

  it("should handle loading state", async () => {
    (useReportRequestsListModule.useReportRequestsList as jest.Mock).mockReturnValueOnce({
      requests: [mockReportRequest, mockEUDRReportRequest, mockCECAFEReportRequest, mockEmptyExportsReportRequest],
      count: 4,
      reportsCount: 6,
      isLoading: true,
      pagination: {
        limit: 10,
        offset: 0,
        pages: 1,
        total: 4,
        totalPages: 1,
      },
      handleListReportsRequests: mockHandleListReportsRequests,
      handleSetPaginationData: mockHandleSetPaginationData,
    });

    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();

    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();
  });

  it("should handle report with no exports", async () => {
    (useReportRequestsListModule.useReportRequestsList as jest.Mock).mockReturnValueOnce({
      requests: [mockEmptyExportsReportRequest],
      count: 1,
      reportsCount: 1,
      isLoading: false,
      pagination: {
        limit: 10,
        offset: 0,
        pages: 1,
        total: 1,
        totalPages: 1,
      },
      handleListReportsRequests: mockHandleListReportsRequests,
      handleSetPaginationData: mockHandleSetPaginationData,
    });

    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();

    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();
  });

  it("should handle expanded row render", async () => {
    renderWithStore({}, ReportRequestsTable, {});

    const expandButtons = screen.getAllByRole("button").filter(button =>
      button.getAttribute("aria-label") === "Expand row"
    );

    fireEvent.click(expandButtons[0]);

    await waitFor(() => {
      const expandedRow = document.querySelector(".expanded-row-indicator");
      expect(expandedRow).toBeInTheDocument();
    });
  });

  it("should handle attemptDownloadReportsZip with no ZIP export", async () => {
    const noZipReport: ReportRequest = {
      ...mockReportRequest,
      exports: [{ id: 2, file_format: "XLS" }]
    };

    (useReportRequestsListModule.useReportRequestsList as jest.Mock).mockReturnValueOnce({
      requests: [noZipReport],
      count: 1,
      reportsCount: 1,
      isLoading: false,
      pagination: {
        limit: 10,
        offset: 0,
        pages: 1,
        total: 1,
        totalPages: 1,
      },
      handleListReportsRequests: mockHandleListReportsRequests,
      handleSetPaginationData: mockHandleSetPaginationData,
    });

    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();

    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();
  });

  it("should handle downloadXlsRequest with no XLS export", async () => {
    const noXlsReport: ReportRequest = {
      ...mockCECAFEReportRequest,
      exports: [{ id: 1, file_format: "ZIP" }]
    };

    (useReportRequestsListModule.useReportRequestsList as jest.Mock).mockReturnValueOnce({
      requests: [noXlsReport],
      count: 1,
      reportsCount: 1,
      isLoading: false,
      pagination: {
        limit: 10,
        offset: 0,
        pages: 1,
        total: 1,
        totalPages: 1,
      },
      handleListReportsRequests: mockHandleListReportsRequests,
      handleSetPaginationData: mockHandleSetPaginationData,
    });

    renderWithStore({}, ReportRequestsTable, {});

    const component = screen.queryByTestId("pb-t-report-requests-table");
    expect(component).toBeInTheDocument();

    const table = screen.getByRole("table");
    expect(table).toBeInTheDocument();
  });

  describe("getTableTitleDescription function", () => {
    const mockT = jest.fn((key, values) => {
      if (key === "REQUESTS_FOUND") {
        return `${values?.count} REQUESTS_FOUND`;
      }
      if (key === "WITH_TOTAL_REPORTS") {
        return `WITH_TOTAL_REPORTS: ${values?.count}`;
      }
      return key;
    }) as any; // Cast to any to avoid type issues with TFunction

    beforeEach(() => {
      mockT.mockClear();
    });

    it("should return correct message when count is 0", () => {
      const result = getTableTitleDescription({
        count: 0,
        reportsCount: 0,
        t: mockT,
      });

      expect(result).toBe("0 REQUESTS_FOUND");
      expect(mockT).toHaveBeenCalledWith("REQUESTS_FOUND", { count: 0 });
    });

    it("should return correct message when count is 1", () => {
      const result = getTableTitleDescription({
        count: 1,
        reportsCount: 1,
        t: mockT,
      });

      expect(result).toBe("0 REQUESTS_FOUND");
      expect(mockT).toHaveBeenCalledWith("REQUESTS_FOUND", { count: 0 });
    });

    it("should return correct message when count > 1 and reportsCount > 1", () => {
      const result = getTableTitleDescription({
        count: 5,
        reportsCount: 10,
        t: mockT,
      });

      expect(result).toBe("5 REQUESTS_FOUND WITH_TOTAL_REPORTS: 10");
      expect(mockT).toHaveBeenCalledWith("REQUESTS_FOUND", { count: 5 });
      expect(mockT).toHaveBeenCalledWith("WITH_TOTAL_REPORTS", { count: 10 });
    });

    it("should return correct message when count > 1 and reportsCount <= 1", () => {
      const result = getTableTitleDescription({
        count: 5,
        reportsCount: 1,
        t: mockT,
      });

      expect(result).toBe("5 REQUESTS_FOUND");
      expect(mockT).toHaveBeenCalledWith("REQUESTS_FOUND", { count: 5 });
    });
  });
});
