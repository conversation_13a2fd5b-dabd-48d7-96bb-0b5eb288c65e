import { ColumnsType } from "antd/lib/table";
import { Trans } from "react-i18next";
import { REPORT_TITLE } from "../../../constants/reports";
import { ReportRequest } from "../../../types/report-request";
import { ReportType } from "../../../types/report-type";
import { hourMin, shortDate } from "../../../utils/date";
import { RequestStatusIndicator } from "../../molecules";
import {
  ColumnAlignRight,
  ColumnCaption,
  ColumnValue,
  RequestTag,
} from "./ReportRequestsTable.styles";

export const REPORT_REQUEST_COLUMNS: ColumnsType<ReportRequest> = [
  {
    title: "ID",
    key: "id",
    dataIndex: "id",
    width: "50px",
    render: (id: number) => <span>{id}</span>,
  },
  {
    title: "Tags",
    key: "tags_names",
    dataIndex: "tags_names",
    width: "150px",
    render: (tags: string[]) =>
      tags.map((tag, i) => (
        <RequestTag color="#00b277" key={tag}>
          {tag}
        </RequestTag>
      )),
  },
  {
    title: () => <Trans i18nKey="REQUESTED_BY" />,
    key: "requested_by",
    dataIndex: "user",
    render: ({ name, email }) => (
      <div>
        <ColumnCaption>{email}</ColumnCaption>
        <ColumnValue>{name}</ColumnValue>
      </div>
    ),
  },
  {
    title: () => <Trans i18nKey="REQUESTED_AT" />,
    key: "created",
    dataIndex: "created",
    align: "right",
    render: (date: Date) => (
      <ColumnAlignRight>
        <ColumnCaption>
          <Trans i18nKey="HOUR_OF" values={{ hour: hourMin(date) }} />
        </ColumnCaption>
        <ColumnValue>{shortDate(date)}</ColumnValue>
      </ColumnAlignRight>
    ),
  },
  {
    title: () => <Trans i18nKey="REPORT_TYPE" />,
    key: "reports_types",
    dataIndex: "reports_types",
    width: "240px",
    render: (types: ReportType[]) => (
      <div>
        {types.map((type) => (
          <div key={type}>
            <Trans i18nKey={REPORT_TITLE[type]} />
          </div>
        ))}
      </div>
    ),
  },
  {
    title: () => <Trans i18nKey="REPORT_COUNT" />,
    key: "reports_count",
    dataIndex: "reports_count",
    width: "240px",
    render: (count: number) => (
      <ColumnValue>
        {count} <Trans i18nKey={count != 1 ? "REPORTS" : "REPORT"} />
      </ColumnValue>
    ),
  },

  {
    title: () => <Trans i18nKey="REPORT_STATUS" />,
    key: "id",
    dataIndex: "id",
    align: "right",
    width: "200px",
    render: (_id, request) => <RequestStatusIndicator request={request} />,
  },
];
