import { DownloadOutlined, MoreOutlined } from "@ant-design/icons";
import { ReportRequestFilter, ReportsTable } from "@components";
import {
  cloneReportRequest,
  downloadFile,
  downloadRequestProperties,
  downloadRequest,
  getReportRequestStatus,
} from "@services";
import { ReportRequest, ReportStatus, ReportType } from "@types";
import { getRequestStatusColor } from "@utils";
import {
  Button,
  Divider,
  Dropdown,
  MenuProps,
  notification,
  Tooltip,
} from "antd";
import moment from "moment";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  icon_folder_zip,
  icon_source_notes,
  icon_xls_download,
} from "../../../assets";
import {
  ContainerLoading,
  ExpandedRowIndicator,
  ReportRequestsTableWrapper,
  RequestTable,
} from "./ReportRequestsTable.styles";
import { REPORT_REQUEST_COLUMNS } from "./columns";
import { useReportRequestsList } from "../../../hooks/use-report-requests-list";
import { TFunction } from "i18next";

export function getTableTitleDescription(props: {
  count: number;
  t: TFunction<"translation", undefined>;
  reportsCount: number;
}): string {
  const { count, reportsCount, t } = props;
  if (!count) {
    return t("REQUESTS_FOUND", { count: 0 });
  }
  if (count > 1 && reportsCount > 1) {
    return (
      t("REQUESTS_FOUND", { count }) +
      " " +
      t("WITH_TOTAL_REPORTS", { count: reportsCount })
    );
  }
  if (count > 1) return t("REQUESTS_FOUND", { count });
  if (count === 1) return t("REQUESTS_FOUND", { count: 0 });
  return "";
}

function ReportRequestsTable() {
  const { t } = useTranslation();
  const zipInterval = useRef<NodeJS.Timeout | undefined>();
  const {
    requests,
    count,
    reportsCount,
    isLoading,
    pagination,
    handleListReportsRequests,
    handleSetPaginationData,
  } = useReportRequestsList();

  function downloadReportsZip(reportRequest: ReportRequest) {
    notification.success({ message: t("DOWNLOAD_STARTED") });
    attemptDownloadReportsZip(reportRequest);
  }

  async function attemptDownloadReportsZip(reportRequest: ReportRequest) {
    try {
      const reportExport = reportRequest.exports.find(
        (exp) => exp.file_format == "ZIP"
      );

      if (!reportExport && !zipInterval.current) {
        zipInterval.current = setInterval(() => {
          attemptDownloadReportsZip(reportRequest);
        }, 30000);
        return;
      }

      if (reportExport && zipInterval) {
        clearInterval(zipInterval.current);
      } else if (!reportExport) {
        return;
      }

      const fileData = await downloadRequest(reportRequest.id, reportExport.id);
      const date = moment().format("DD_MM_YYYY");
      const filename = `REPORTS_${reportRequest.reports_types[0]}_${reportRequest.id}_${date}.zip`;
      downloadFile(fileData, filename, "application/zip");
      notification.success({ message: t("DOWNLOAD_STARTED") });
    } catch {
      notification.error({
        message: t("ERR_REPORTS_DOWNLOAD_ZIP"),
      });
    }
  }

  async function updateReport(reportRequest: ReportRequest) {
    try {
      await cloneReportRequest(reportRequest.id);
      handleListReportsRequests({
        offset: 0,
        limit: pagination.limit,
      });
      notification.success({
        message:
          reportRequest.reports_count == 1
            ? t("REPORT_UPDATE_SUCCESS")
            : t("REPORT_UPDATES_SUCCESS"),
      });
    } catch {
      notification.error({
        message: t("ERROR_UPDATE_REPORTS"),
        description: t("TRY_AGAIN_LATER"),
      });
    }
  }

  async function downloadXlsRequest(reportRequest: ReportRequest) {
    const xlsExport = reportRequest.exports.find((exp) =>
      ["XLS", "XLSX"].includes(exp.file_format)
    );
    if (!xlsExport) return;
    try {
      notification.success({ message: t("DOWNLOAD_STARTED") });
      const date = moment().format("DD-MM-YYYYTHH_mm_SS");
      const filename = `reports_${reportRequest.id}_${date}.xlsx`;
      const fileData = await downloadRequest(reportRequest.id, xlsExport.id);
      downloadFile(
        fileData,
        filename,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      notification.success({
        message: t("DOWNLOAD_FINISHED"),
      });
    } catch {
      notification.error({
        message: t("ERR_REPORTS_DOWNLOAD"),
      });
    }
  }

  async function handlePropertyDownload(request: ReportRequest) {
    try {
      const fileData = await downloadRequestProperties(request.id);
      const filename = `properties_request_${request.id}.geojson`;
      downloadFile(fileData, filename, "application/octet-stream");
      notification.success({
        message: t("DOWNLOAD_FINISHED"),
      });
    } catch {
      notification.error({
        message: t("ERR_PROPERTIES_DOWNLOAD"),
      });
    }
  }

  const columns = [
    ...REPORT_REQUEST_COLUMNS,
    {
      title: t("ACTIONS"),
      key: "action",
      align: "center",
      width: "80px",
      render: (_: any, reportRequest: ReportRequest) => {
        let items: MenuProps["items"] = [];
        if (reportRequest.reports_count == 0) return items;
        if (
          reportRequest.reports_types.every(
            (reportType) =>
              reportType == ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR ||
              reportType == ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
          )
        ) {
          items.unshift({
            key: 3,
            label: t("DOWNLOAD_PROPERTY_UNIFIED"),
            onClick: () => handlePropertyDownload(reportRequest),
            icon: <DownloadOutlined />,
          });
        }
        items = [
          {
            key: 1,
            label: t("DOWNLOAD_REPORTS_ZIP"),
            disabled: !reportRequest.exports.find(
              (exp) => exp.file_format == "ZIP"
            ),
            icon: (
              <img src={icon_folder_zip} width="16" height="16" alt="ZIP" />
            ),
            onClick: () => {
              downloadReportsZip(reportRequest);
            },
          },
          ...items,
        ];

        if (
          reportRequest.reports_types.includes(
            ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
          ) ||
          reportRequest.reports_types.includes(
            ReportType.RENOVABIO_CERTIFICATION
          )
        ) {
          items.push({
            key: 2,
            label: t("REQUEST_REPORTS_XLS_DOWNLOAD"),
            disabled: !reportRequest.exports.find((exp) =>
              ["XLS", "XLSX"].includes(exp.file_format)
            ),
            onClick: () => {
              downloadXlsRequest(reportRequest);
            },
            icon: (
              <img
                src={icon_xls_download}
                width="16"
                height="16"
                alt={t("UPDATE_REPORTS")}
              />
            ),
          });
        }

        if (
          reportRequest.reports_types.every(
            (reportType) =>
              reportType != ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
          )
        ) {
          items.push({
            key: 4,
            label: t("REQUEST_REPORTS_UPDATE"),
            disabled:
              getReportRequestStatus(reportRequest) != ReportStatus.DONE,
            onClick: () => {
              updateReport(reportRequest);
            },
            icon: (
              <img
                src={icon_source_notes}
                width="16"
                height="16"
                alt={t("UPDATE_REPORTS")}
              />
            ),
          });
        }

        let button = (
          <Tooltip title={t("NO_ACTION_FOR_THIS_REQUEST")} placement="left">
            <Button
              type="ghost"
              size="small"
              shape="circle"
              icon={<MoreOutlined />}
            />
          </Tooltip>
        );

        if (items.length) {
          button = (
            <Dropdown
              menu={{
                items,
              }}
              placement="bottomRight"
              trigger={["click"]}
              arrow
            >
              <Button
                type="ghost"
                size="small"
                shape="circle"
                icon={<MoreOutlined />}
              />
            </Dropdown>
          );
        }

        return <>{button}</>;
      },
    },
  ];

  const [currentPage, setCurrentPage] = useState(1);

  const expandedRowRender = (request: ReportRequest & { borderColor: string }) => {
    return (
      <ExpandedRowIndicator style={{ borderLeftColor: request.borderColor }}>
        <ReportsTable requestId={request.id} />
      </ExpandedRowIndicator>
    );
  };

  const dataSource = requests.map((request) => ({
    ...request,
    key: `REQUEST_${request.id}`,
    borderColor: getRequestStatusColor(request),
  }));

  return (
    <div data-testid="pb-t-report-requests-table">
      <ReportRequestFilter />
      <ReportRequestsTableWrapper>
        <Divider
          orientation="left"
          orientationMargin="0"
          style={{ position: "relative" }}
        >
          {getTableTitleDescription({
            count,
            reportsCount,
            t,
          })}
          <ContainerLoading isLoading={isLoading}>
            <span />
          </ContainerLoading>
        </Divider>

        <RequestTable
          columns={columns as any}
          dataSource={dataSource}
          expandable={{
            expandedRowRender,
          }}
          onChange={(data) => {
            if (isLoading) return;
            if (data.current === currentPage) return;
            setCurrentPage(data.current);
            if (data.current === 1) {
              handleListReportsRequests({
                offset: 0,
                limit: pagination.limit,
              });
              return;
            }
            handleListReportsRequests({
              offset: (data.current - 1) * pagination.limit,
              limit: pagination.limit,
            });
            return;
          }}
          pagination={{
            showSizeChanger: true,
            onShowSizeChange: (_, size) => {
              if (isLoading) return;
              handleSetPaginationData({ pagination: { limit: size } });
              handleListReportsRequests({
                offset: 0,
                limit: size,
              });
            },
            onChange: () => {},
            total: pagination.total,
            hideOnSinglePage: true,
            current: currentPage,
          }}
        />
      </ReportRequestsTableWrapper>
    </div>
  );
}

export default ReportRequestsTable;
