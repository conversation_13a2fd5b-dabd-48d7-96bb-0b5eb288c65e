import {
  BaseRequestForm,
  InspectionForm,
  ProtocolEudrForm,
  SocioenvironmentalForm,
  ProtocolEudrCecafeForm,
} from "@components";
import { ReportType } from "@types";
import { Wrapper } from "./ReportSelect.styles";
import DeforestationForm from "../deforestation-analysis-form/DeforestationForm";
import { useEffect, useState } from "react";
import { SinisterForm } from "../sinister-form";
import { DeforestationEUDRForm } from "../deforestation-eudr-form/DeforestationEUDRForm";
import { Property } from "@/types/property";

interface ReportSelectProps {
  readonly data: any;
  readonly properties: Property[];
  readonly subareas: Array<{
    area_name: string;
    name: string;
    coordinates: any;
  }>;
  readonly initialReportType: ReportType;
  readonly isSubmitted: boolean;
  readonly onChange: (value) => void;
  readonly onPropertiesLoad: (value) => void;
  readonly onSubareasLoad: (value) => void;
  readonly onRefBacenSubareasLoad: (value) => void;
}

function ReportSelect({
  data,
  initialReportType,
  isSubmitted,
  subareas,
  properties,
  onChange,
  onPropertiesLoad,
  onSubareasLoad,
  onRefBacenGeometryLoad,
  onRefBacenSubareasLoad,
}: ReportSelectProps & {
  onRefBacenGeometryLoad: (property) => void;
}) {
  const [reportType, setReportType] = useState<ReportType>(
    ReportType.VALUATION
  );

  useEffect(() => {
    setReportType(initialReportType);
  }, [initialReportType]);

  function handleFormChange(formValue) {
    const newValue = {
      ...data,
      ...formValue,
      reportType,
    };
    onChange(newValue);
  }

  function handleChangeDeforestationForm(value: any) {
    onChange({ ...data, ...value, reportType });
  }

  function handleTypeChange(newReportType: ReportType) {
    setReportType(newReportType);
    const newValue = {
      ...data,
      reportType: newReportType,
    };
    onChange(newValue);
  }

  return (
    <Wrapper data-testid="pb-t-report-select">
      <BaseRequestForm
        data={data}
        reportType={initialReportType}
        onChange={handleFormChange}
        properties={properties.map(p => ({ ...p, id: String(p.id) }))}
        subareas={subareas.map(s => ({ ...s, id: s.name }))}
        isSubmitted={isSubmitted}
        hasSubareas={[
          ReportType.INSPECTION,
          ReportType.INSPECTION_FINANCIAL,
          ReportType.SINISTER,
          ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER,
        ].includes(reportType)}
        selectedProperty={data.property}
        selectedSubareas={data.subareas}
        onPropertiesLoad={(newProperties) => onPropertiesLoad(newProperties)}
        onSubareasLoad={(newSubareas) => onSubareasLoad(newSubareas)}
        toIntersect={
          reportType != ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER
        }
      >
        {[ReportType.INSPECTION, ReportType.INSPECTION_FINANCIAL].includes(
          reportType
        ) && (
          <InspectionForm
            data={data}
            properties={properties}
            subareas={subareas}
            isSubmitted={isSubmitted}
            onChange={handleFormChange}
            onTypeChange={(type) => handleTypeChange(type)}
            onRefBacenGeometryLoad={(property) =>
              onRefBacenGeometryLoad(property)
            }
            onRefBacenSubareasLoad={(subareas) =>
              onRefBacenSubareasLoad(subareas)
            }
          />
        )}
        {reportType == ReportType.SINISTER && (
          <SinisterForm
            data={data}
            isSubmitted={isSubmitted}
            onChange={handleFormChange}
          />
        )}
        {reportType == ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR && (
          <ProtocolEudrForm
            data={data}
            isSubmitted={isSubmitted}
            onChange={handleFormChange}
          />
        )}
        {reportType == ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE && (
          <ProtocolEudrCecafeForm
            data={data}
            isSubmitted={isSubmitted}
            onChange={handleFormChange}
          />
        )}
        {reportType == ReportType.SOCIOENVIRONMENT_COMPLIANCE && (
          <SocioenvironmentalForm
            data={data}
            isSubmitted={isSubmitted}
            onChange={handleFormChange}
          />
        )}
        {reportType == ReportType.DETAILED_ANALYSIS_DEFORESTATION && (
          <DeforestationForm
            data={data}
            isSubmitted={isSubmitted}
            onChange={handleChangeDeforestationForm}
          />
        )}

        {reportType == ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR && (
          <DeforestationEUDRForm
            data={data}
            isSubmitted={isSubmitted}
            onChange={handleFormChange}
          />
        )}
      </BaseRequestForm>
    </Wrapper>
  );
}

export default ReportSelect;
