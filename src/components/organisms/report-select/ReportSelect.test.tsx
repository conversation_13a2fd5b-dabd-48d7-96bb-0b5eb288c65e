import { cleanup, render, screen, fireEvent } from "@testing-library/react";
import React from "react";
import { ReportType } from "@types";
import { act } from "react-dom/test-utils";

jest.mock("@components", () => ({
  BaseRequestForm: jest.fn(({ children, onChange, has<PERSON><PERSON><PERSON><PERSON>, toIntersect }) => (
    <div data-testid="mock-base-request-form" data-has-subareas={hasSubareas} data-to-intersect={toIntersect}>
      <button data-testid="trigger-form-change" onClick={() => onChange({ testKey: "testValue" })}>
        Trigger Form Change
      </button>
      {children}
    </div>
  )),
  InspectionForm: jest.fn(({ onTypeChange, onChange }) => (
    <div data-testid="mock-inspection-form">
      <button data-testid="trigger-type-change" onClick={() => onTypeChange("INSPECTION_FINANCIAL")}>
        Change Type
      </button>
      <button data-testid="trigger-inspection-form-change" onClick={() => onChange({ inspectionData: "test" })}>
        Change Form
      </button>
    </div>
  )),
  ProtocolEudrForm: jest.fn(({ onChange }) => (
    <div data-testid="mock-protocol-eudr-form">
      <button data-testid="trigger-protocol-eudr-form-change" onClick={() => onChange({ protocolData: "test" })}>
        Change Form
      </button>
    </div>
  )),
  SocioenvironmentalForm: jest.fn(({ onChange }) => (
    <div data-testid="mock-socioenvironmental-form">
      <button data-testid="trigger-socioenvironmental-form-change" onClick={() => onChange({ socioData: "test" })}>
        Change Form
      </button>
    </div>
  )),
  ProtocolEudrCecafeForm: jest.fn(({ onChange }) => (
    <div data-testid="mock-protocol-eudr-cecafe-form">
      <button data-testid="trigger-protocol-eudr-cecafe-form-change" onClick={() => onChange({ cecafeData: "test" })}>
        Change Form
      </button>
    </div>
  )),
}));

jest.mock("../deforestation-analysis-form/DeforestationForm", () => ({
  __esModule: true,
  default: jest.fn(({ onChange }) => (
    <div data-testid="mock-deforestation-form">
      <button data-testid="trigger-deforestation-form-change" onClick={() => onChange({ deforestationData: "test" })}>
        Change Form
      </button>
    </div>
  )),
}));

jest.mock("../sinister-form", () => ({
  SinisterForm: jest.fn(({ onChange }) => (
    <div data-testid="mock-sinister-form">
      <button data-testid="trigger-sinister-form-change" onClick={() => onChange({ sinisterData: "test" })}>
        Change Form
      </button>
    </div>
  )),
}));

jest.mock("../deforestation-eudr-form/DeforestationEUDRForm", () => ({
  DeforestationEUDRForm: jest.fn(({ onChange }) => (
    <div data-testid="mock-deforestation-eudr-form">
      <button data-testid="trigger-deforestation-eudr-form-change" onClick={() => onChange({ eudrData: "test" })}>
        Change Form
      </button>
    </div>
  )),
}));

import ReportSelect from "./ReportSelect";

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe("ReportSelect component", () => {
  const defaultProps = {
    data: {},
    initialReportType: ReportType.VALUATION,
    isSubmitted: false,
    subareas: [],
    properties: [],
    onChange: jest.fn(),
    onPropertiesLoad: jest.fn(),
    onSubareasLoad: jest.fn(),
    onRefBacenSubareasLoad: jest.fn(),
    onRefBacenGeometryLoad: jest.fn(),
  };

  it("should render the component", () => {
    render(<ReportSelect {...defaultProps} />);
    const component = screen.queryByTestId("pb-t-report-select");
    expect(component).toBeInTheDocument();
  });

  it("should handle form changes correctly", () => {
    render(<ReportSelect {...defaultProps} />);

    fireEvent.click(screen.getByTestId("trigger-form-change"));

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      testKey: "testValue",
      reportType: ReportType.VALUATION,
    });
  });

  it("should handle type changes correctly", () => {
    render(
      <ReportSelect
        {...defaultProps}
        initialReportType={ReportType.INSPECTION}
      />
    );

    expect(screen.getByTestId("mock-inspection-form")).toBeInTheDocument();

    fireEvent.click(screen.getByTestId("trigger-type-change"));

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      reportType: ReportType.INSPECTION_FINANCIAL,
    });
  });

  it("should render different forms based on report type", () => {
    const { rerender } = render(
      <ReportSelect
        {...defaultProps}
        initialReportType={ReportType.SINISTER}
      />
    );

    expect(screen.getByTestId("mock-sinister-form")).toBeInTheDocument();

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR}
        />
      );
    });

    expect(screen.getByTestId("mock-protocol-eudr-form")).toBeInTheDocument();

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE}
        />
      );
    });

    expect(screen.getByTestId("mock-protocol-eudr-cecafe-form")).toBeInTheDocument();

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.SOCIOENVIRONMENT_COMPLIANCE}
        />
      );
    });

    expect(screen.getByTestId("mock-socioenvironmental-form")).toBeInTheDocument();

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.DETAILED_ANALYSIS_DEFORESTATION}
        />
      );
    });

    expect(screen.getByTestId("mock-deforestation-form")).toBeInTheDocument();

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR}
        />
      );
    });

    expect(screen.getByTestId("mock-deforestation-eudr-form")).toBeInTheDocument();
  });

  it("should handle deforestation form changes correctly", () => {
    render(
      <ReportSelect
        {...defaultProps}
        initialReportType={ReportType.DETAILED_ANALYSIS_DEFORESTATION}
      />
    );

    fireEvent.click(screen.getByTestId("trigger-deforestation-form-change"));

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      deforestationData: "test",
      reportType: ReportType.DETAILED_ANALYSIS_DEFORESTATION,
    });
  });

  it("should set hasSubareas prop correctly based on report type", () => {
    const { rerender } = render(
      <ReportSelect
        {...defaultProps}
        initialReportType={ReportType.INSPECTION}
      />
    );

    expect(screen.getByTestId("mock-base-request-form").getAttribute("data-has-subareas")).toBe("true");

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.VALUATION}
        />
      );
    });

    expect(screen.getByTestId("mock-base-request-form").getAttribute("data-has-subareas")).toBe("false");
  });

  it("should set toIntersect prop correctly based on report type", () => {
    const { rerender } = render(
      <ReportSelect
        {...defaultProps}
        initialReportType={ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER}
      />
    );

    expect(screen.getByTestId("mock-base-request-form").getAttribute("data-to-intersect")).toBe("false");

    act(() => {
      rerender(
        <ReportSelect
          {...defaultProps}
          initialReportType={ReportType.VALUATION}
        />
      );
    });

    expect(screen.getByTestId("mock-base-request-form").getAttribute("data-to-intersect")).toBe("true");
  });
});
