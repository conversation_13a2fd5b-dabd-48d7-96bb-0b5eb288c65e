import { useCertificationRenovaBioForm } from "@/hooks/use-certification-renova-bio-form";
import {
  CloudDownloadOutlined,
  DownOutlined,
  UpOutlined,
} from "@ant-design/icons";
import { Alert, Button } from "antd";
import { useTranslation } from "react-i18next";
import {
  ValidDocumentsTitle,
  SummaryCard,
  SummaryContent,
  Wrapper,
  InlineTableButton,
  DetailCarDocument,
  TableDataInvalidCarCode,
  TableListInvalidCARCodes,
} from "./CertificationRenovaBioStepTwo.styles";
import { icon_person_cancel, icon_person_check } from "../../../assets";
export function CertificationRenovaBioFormStepTwo() {
  const {
    handleToggleInvalidDocumentsExpanded,
    documentsData,
    isInvalidDocumentsExpanded,
    handleDownloadInvalidDocumentsXLS,
  } = useCertificationRenovaBioForm();
  const { t } = useTranslation();

  return (
    <Wrapper data-testid="certification-renova-bio-step-two">
      <section>
        <h4>{t("REVIEW_UPLOADED_DOCUMENTS")}</h4>
        <SummaryCard>
          <ValidDocumentsTitle>{t("VALID_CARS")}</ValidDocumentsTitle>
          <DetailCarDocument isValid>
            <img src={icon_person_check} alt="Valid document icon" />{" "}
            <span>{documentsData.carCodes.length}</span>
          </DetailCarDocument>
        </SummaryCard>

        <SummaryCard
          style={{
            display: documentsData.invalidRows.length ? "flex" : "none",
          }}
        >
          <ValidDocumentsTitle>{t("INVALID_CARS")}</ValidDocumentsTitle>
          <DetailCarDocument isValid={false}>
            <img src={icon_person_cancel} alt="Valid document icon" />{" "}
            <span>{documentsData.invalidRows.length}</span>
          </DetailCarDocument>
          <SummaryContent>
            <Alert
              message={t("INVALID_DOCUMENTS_DESCRIPTION")}
              type="warning"
            />
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                size="small"
                icon={<CloudDownloadOutlined />}
                onClick={handleDownloadInvalidDocumentsXLS}
                disabled={!documentsData.invalidRows.length}
              >
                {t("DOWNLOAD_XLS_RELATION")}
              </Button>
            </div>

            <TableListInvalidCARCodes>
              <thead>
                <tr>
                  <th>
                    <InlineTableButton
                      data-testid="button-toggle-documents-car-invalid"
                      onClick={() => {
                        handleToggleInvalidDocumentsExpanded();
                      }}
                    >
                      {isInvalidDocumentsExpanded ? (
                        <DownOutlined />
                      ) : (
                        <UpOutlined />
                      )}
                    </InlineTableButton>
                    <span className="dropdown-documents-invalid">
                      {t("DOCUMENTS")}
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {(isInvalidDocumentsExpanded
                  ? documentsData.invalidRows
                  : []
                ).map((row, index) => {
                  return (
                    <tr key={index}>
                      <TableDataInvalidCarCode
                        data-testid="table-data-invalid-car-code"
                        index={index}
                      >
                        {row.car}
                      </TableDataInvalidCarCode>
                    </tr>
                  );
                })}
              </tbody>
            </TableListInvalidCARCodes>
          </SummaryContent>
        </SummaryCard>
      </section>
    </Wrapper>
  );
}
