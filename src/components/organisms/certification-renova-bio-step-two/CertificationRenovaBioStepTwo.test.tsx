import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { useTranslation } from "react-i18next";
import { CertificationRenovaBioFormStepTwo } from "./CertificationRenovaBioStepTwo";
import { useCertificationRenovaBioForm } from "../../../hooks/use-certification-renova-bio-form";
jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(),
}));

jest.mock("@/hooks/use-certification-renova-bio-form", () => ({
  useCertificationRenovaBioForm: jest.fn(),
}));

describe("CertificationRenovaBioFormStepTwo", () => {
  const mockUseTranslation = useTranslation as jest.Mock;
  const mockUseCertificationRenovaBioForm =
    useCertificationRenovaBioForm as jest.Mock;

  beforeEach(() => {
    mockUseTranslation.mockReturnValue({ t: (key: string) => key });
    mockUseCertificationRenovaBioForm.mockReturnValue({
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      documentsData: {
        carCodes: [],
        invalidRows: [],
      },
      isInvalidDocumentsExpanded: false,
      handleDownloadInvalidDocumentsXLS: jest.fn(),
    });
  });

  it("renders the component", () => {
    render(<CertificationRenovaBioFormStepTwo />);
    expect(screen.getByText("REVIEW_UPLOADED_DOCUMENTS")).toBeInTheDocument();
  });

  it("displays the number of valid documents", () => {
    mockUseCertificationRenovaBioForm.mockReturnValueOnce({
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      documentsData: {
        carCodes: [
          { car: "valid", ano_elegibilidade: 2025, cultura: "SOJA" },
          { car: "valid", ano_elegibilidade: 2025, cultura: "SOJA" },
          { car: "valid", ano_elegibilidade: 2025, cultura: "SOJA" },
        ],
        invalidRows: [],
      },
      isInvalidDocumentsExpanded: false,
      handleDownloadInvalidDocumentsXLS: jest.fn(),
    });

    render(<CertificationRenovaBioFormStepTwo />);
    expect(screen.getByText("3")).toBeInTheDocument();
  });

  it("displays the number of invalid documents", () => {
    mockUseCertificationRenovaBioForm.mockReturnValueOnce({
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      documentsData: {
        carCodes: [],
        invalidRows: [
          { car: "invalid", ano_elegibilidade: 2025, cultura: "SOJA" },
          { car: "invalid", ano_elegibilidade: 2025, cultura: "SOJA" },
        ],
      },
      isInvalidDocumentsExpanded: false,
      handleDownloadInvalidDocumentsXLS: jest.fn(),
    });

    render(<CertificationRenovaBioFormStepTwo />);
    expect(screen.getByText("2")).toBeInTheDocument();
  });

  it("calls handleDownloadInvalidDocumentsXLS when download button is clicked", () => {
    const handleDownloadInvalidDocumentsXLS = jest.fn();
    mockUseCertificationRenovaBioForm.mockReturnValueOnce({
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      documentsData: {
        carCodes: [],
        invalidRows: [
          { car: "invalid", ano_elegibilidade: 2025, cultura: "SOJA" },
        ],
      },
      isInvalidDocumentsExpanded: false,
      handleDownloadInvalidDocumentsXLS,
    });

    render(<CertificationRenovaBioFormStepTwo />);
    fireEvent.click(screen.getByText("DOWNLOAD_XLS_RELATION"));
    expect(handleDownloadInvalidDocumentsXLS).toHaveBeenCalled();
  });

  it("toggles invalid documents list when button is clicked", () => {
    const handleToggleInvalidDocumentsExpanded = jest.fn();
    mockUseCertificationRenovaBioForm.mockReturnValueOnce({
      handleToggleInvalidDocumentsExpanded,
      documentsData: {
        carCodes: [],
        invalidRows: [
          { car: "invalid", ano_elegibilidade: 2025, cultura: "SOJA" },
        ],
      },
      isInvalidDocumentsExpanded: false,
      handleDownloadInvalidDocumentsXLS: jest.fn(),
    });

    render(<CertificationRenovaBioFormStepTwo />);
    fireEvent.click(screen.getByTestId("button-toggle-documents-car-invalid"));
    expect(handleToggleInvalidDocumentsExpanded).toHaveBeenCalled();
  });

  it("should show items with invalid document", () => {
    const handleToggleInvalidDocumentsExpanded = jest.fn();
    mockUseCertificationRenovaBioForm.mockReturnValueOnce({
      handleToggleInvalidDocumentsExpanded,
      documentsData: {
        carCodes: [],
        invalidRows: [
          { car: "invalid", ano_elegibilidade: 2025, cultura: "SOJA" },
        ],
      },
      isInvalidDocumentsExpanded: true,
      handleDownloadInvalidDocumentsXLS: jest.fn(),
    });

    render(<CertificationRenovaBioFormStepTwo />);
    expect(
      screen.getByTestId("table-data-invalid-car-code")
    ).toBeInTheDocument();
  });
});
