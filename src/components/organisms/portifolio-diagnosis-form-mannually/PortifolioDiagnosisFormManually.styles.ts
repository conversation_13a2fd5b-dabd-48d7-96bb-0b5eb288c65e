import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
  width: 100%;

  .auto-complete-municipality {
    width: 100%;
  }

  .radio-select-portifolio-diagnosis-data {
    margin: 8px 0 16px 0;
  }

  .radio-group-select-request-method {
    margin-bottom: 16px;
  }

  .span-selected-data-title {
    margin: 8px 0;
  }

  .p-title {
    margin-bottom: 8px;
  }

  .divider {
    margin: 8px 0;
  }

  .div-content-select-modules {
    p {
      margin-bottom: 16px;
    }
  }
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;

  .link-download-csv {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40%;
    border: 1px solid;
    border-radius: 4px;
    padding: 2px;
    border-color: #00b277;
    color: #00b277;
    transition: opacity 0.3s;
    margin-bottom: 12px;
    background: #fff;

    &:hover {
      cursor: pointer;
      opacity: 0.7;
    }
  }
`;

export const OptionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 12px;
`;
