import { AutoComplete, Radio } from "antd";
import {
  Content,
  OptionsContainer,
  Wrapper,
} from "./PortifolioDiagnosisFormManually.styles";
import { FormListItem } from "@/components/molecules";
import { RequestMethod } from "@types";
import { useTranslation } from "react-i18next";
import { MODEL_BY_REPORT_TYPE } from "@constants";
import { OptionItem } from "@/components/atoms";
import { usePortifolioDiagnosis } from "@/hooks/use-portifolio-diagnosis";
import { ReportPortifolioDiagnosisManualSelectType } from "@/store/modules/portifolio-diagnosis/types";
import { removeSpecialCharacters } from "@/utils/text";
import { MultiSelect } from "@/components/atoms/multi-select/MultiSelect";
const { Option } = AutoComplete;

const getOptionStateClassName = (
  data: { uf: string; name: string }[],
  uf: string
): string => {
  const result = data.find((item) => item.uf === uf);

  return result ? "ant-select-item-option-active" : "";
};

const getOptionMunicipalityClassName = (
  data: { ibgeCode: number }[],
  ibgeCode: number
): string => {
  const result = data.find((item) => item.ibgeCode === ibgeCode);

  return result ? "ant-select-item-option-active" : "";
};

export const PortifolioDiagnosisFormManually = () => {
  const { t } = useTranslation();
  const {
    data,
    handleSetSelectType,
    handleChangeMunicipality,
    handleSearchMunicipality,
    handleClickRemoveState,
    handleClickRemoveMunicipality,
    handleChangeSelectState,
    handleSearchState,
    handleChangeManualSubModules,
  } = usePortifolioDiagnosis();

  return (
    <Wrapper>
      <FormListItem>
        <Content>
          <p className="p-title">{t("INTEREST_AREA")}</p>

          <Radio.Group
            size="small"
            data-testid="radio-group-manual-select-type"
            defaultValue={data.manual.selectType}
            className="radio-select-portifolio-diagnosis-data"
            onChange={(event) => {
              handleSetSelectType(event.target.value);
            }}
          >
            <Radio.Button
              data-testid="radio-button-municipality"
              checked={
                data.manual.selectType ===
                ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY
              }
              value={ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY}
            >
              {t("MUNICIPALITIES")}
            </Radio.Button>
            <Radio.Button
              data-testid="radio-button-state"
              checked={
                data.manual.selectType ===
                ReportPortifolioDiagnosisManualSelectType.STATE
              }
              value={ReportPortifolioDiagnosisManualSelectType.STATE}
            >
              {t("STATE")}
            </Radio.Button>
          </Radio.Group>

          {data.requestMethod === RequestMethod.BATCH && (
            <a
              href={MODEL_BY_REPORT_TYPE[data.reportType] as string}
              download
              target="_blank"
              className="link-download-csv"
            >
              {t("DOWNLOAD_SAMPLE_CSV")}
            </a>
          )}

          {data.manual.selectType ===
            ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY && (
            <AutoComplete
              key={"auto-complete-municipality"}
              className="auto-complete-municipality"
              disabled={
                false || data.manual.municipaliciesSelected.length >= 10
              }
              onChange={(ibgeCode) => {
                handleChangeMunicipality({ ibgeCode: Number(ibgeCode) });
              }}
              onSearch={handleSearchMunicipality}
              placeholder={t("SEARCH_MUNICIPALITY")}
              value={data.manual.searchMunicipality}
              data-testid="input-auto-complete-search-municipality"
            >
              {data.manual.municipalities
                .filter((item) => {
                  const itemName = removeSpecialCharacters(
                    item.name
                  ).toLowerCase();
                  const search = removeSpecialCharacters(
                    data.manual.searchMunicipality
                  ).toLowerCase();
                  return itemName.includes(search);
                })
                .map((municipality) => (
                  <Option
                    key={municipality.ibge_code}
                    label={`${municipality.name} - ${municipality.uf}`}
                    value={municipality.ibge_code}
                    disabled={false}
                    className={getOptionMunicipalityClassName(
                      data.manual.municipaliciesSelected,
                      municipality.ibge_code
                    )}
                    onRemove={() => {}}
                  >
                    {municipality.name} - {municipality.uf}
                  </Option>
                ))}
            </AutoComplete>
          )}

          {data.manual.selectType ===
            ReportPortifolioDiagnosisManualSelectType.STATE && (
            <AutoComplete
              key={"auto-complete-state"}
              className="auto-complete-municipality"
              data-testid="input-auto-complete-search-state"
              value={data.manual.searchState}
              placeholder={t("SEARCH_LOCATION_STATE")}
              disabled={false || data.manual.statesSelected.length === 1}
              onSearch={handleSearchState}
              onChange={handleChangeSelectState}
            >
              {data.manual.states
                .filter((item) => {
                  const itemName = removeSpecialCharacters(
                    item.name
                  ).toLowerCase();
                  const search = removeSpecialCharacters(
                    data.manual.searchState
                  ).toLowerCase();
                  return itemName.includes(search);
                })
                .map((state) => (
                  <Option
                    key={state.uf}
                    label={`${state.name}`}
                    value={state.uf}
                    className={getOptionStateClassName(
                      data.manual.statesSelected,
                      state.uf
                    )}
                    disabled={false}
                    onRemove={() => {}}
                  >
                    {state.name}
                  </Option>
                ))}
            </AutoComplete>
          )}

          {data.manual.selectType ===
            ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY && (
            <OptionsContainer>
              <span className="span-selected-data-title">
                {t("SELECTED_MUNICIPALITIES")}
              </span>

              {data.manual.municipaliciesSelected.map((item) => (
                <OptionItem
                  key={item.ibgeCode}
                  label={`${item.name} - ${item.uf}`}
                  value={item.ibgeCode}
                  disabled={false}
                  onRemove={(ibge_code) => {
                    handleClickRemoveMunicipality(ibge_code);
                  }}
                />
              ))}
            </OptionsContainer>
          )}

          {data.manual.selectType ===
            ReportPortifolioDiagnosisManualSelectType.STATE && (
            <OptionsContainer>
              <span className="span-selected-data-title">
                {t("SELECTED_STATES")}
              </span>
              {data.manual.statesSelected.map((state) => (
                <OptionItem
                  key={state.uf}
                  label={state.name}
                  value={state.uf}
                  disabled={false}
                  onRemove={(uf) => {
                    handleClickRemoveState(uf);
                  }}
                />
              ))}
            </OptionsContainer>
          )}
        </Content>
      </FormListItem>
      <div className="divider" />
      <FormListItem>
        <Content className="div-content-select-modules">
          <p className="p-title">{t("MODULES_TYPES")}</p>
          <MultiSelect
            valuesSelected={data.manual.subModulesSelected.map(
              (item) => item.report_type
            )}
            onChange={(subModulesTypes) =>
              handleChangeManualSubModules(subModulesTypes)
            }
            options={data.manual.subModules.map((item) => ({
              label: item.description,
              value: item.report_type,
            }))}
            placeholder={t("PORTIFOLIO_DIAGNOSIS_SELECT_MODULE_PLACEHOLDER")}
          />
        </Content>
      </FormListItem>
    </Wrapper>
  );
};
