import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { PortifolioDiagnosisFormManually } from "./PortiflioDiagnosisFormManually";
import { useTranslation } from "react-i18next";
import { usePortifolioDiagnosis } from "@/hooks/use-portifolio-diagnosis";
import { ReportPortifolioDiagnosisManualSelectType } from "@/store/modules/portifolio-diagnosis/types";
import { RequestMethod } from "@types";
import { useAccountDetails } from "@/hooks/use-account-details";
import { Resource } from "@constants";

// Mock para o CustomSelect
jest.mock("@/components/atoms/custom-select/CustomSelect", () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(({ children, ...props }) => {
      return <div data-testid="mocked-custom-select" {...props}>{children}</div>;
    }),
  };
});

jest.mock("@/hooks/use-account-details");

jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(),
}));

jest.mock("@/hooks/use-portifolio-diagnosis", () => ({
  usePortifolioDiagnosis: jest.fn(),
}));

describe("PortifolioDiagnosisFormManually", () => {
  const mockUseTranslation = useTranslation as jest.Mock;
  const mockUsePortifolioDiagnosis = usePortifolioDiagnosis as jest.Mock;

  beforeEach(() => {
    mockUseTranslation.mockReturnValue({ t: (key: string) => key });
    mockUsePortifolioDiagnosis.mockReturnValue({
      data: {
        manual: {
          subModulesSelected: [],
          subModules: [],
          selectType: ReportPortifolioDiagnosisManualSelectType.STATE,
          searchMunicipality: "",
          searchMinicipality: "Test Municipality",
          municipalities: [],
          municipaliciesSelected: [],
          searchState: "",
          states: [],
          statesSelected: [],
        },
        requestMethod: "",
      },
      handleSetSelectType: jest.fn(),
      handleChangeMunicipality: jest.fn(),
      handleSearchMunicipality: jest.fn(),
      handleClickRemoveState: jest.fn(),
      handleClickRemoveMunicipality: jest.fn(),
      handleChangeSelectState: jest.fn(),
      handleSearchState: jest.fn(),
    });
  });

  afterEach(() => {
    mockUsePortifolioDiagnosis.mockReset();
  });

  it("should be able to render municipalities list selected", () => {
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          selectType: ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY,
          searchMinicipality: "Test Municipality",
          municipalities: [
            {
              ibgeCode: 123,
              name: "Test Municipality",
              uf: "TS",
            },
          ],
          municipaliciesSelected: [
            {
              ibgeCode: 123,
              name: "Test Municipality",
              uf: "TS",
            },
          ],
        },
      },
    });
    render(<PortifolioDiagnosisFormManually />);
    expect(screen.getByText(`Test Municipality - TS`)).toBeInTheDocument();
  });

  it("should be able to render states list selected", () => {
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          searchState: "Test State",
          states: [
            {
              name: "Test State",
              uf: "TS",
            },
          ],
          statesSelected: [
            {
              name: "Test State",
              uf: "TS",
            },
          ],
        },
      },
    });
    render(<PortifolioDiagnosisFormManually />);
    expect(screen.getByText("Test State")).toBeInTheDocument();
  });

  it("changes select type to MUNICIPALITY", () => {
    const handleSetSelectType = jest.fn();
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      handleSetSelectType,
    });
    render(<PortifolioDiagnosisFormManually />);
    const municipalityRadio = screen.getByTestId("radio-button-municipality");
    fireEvent.click(municipalityRadio);
    expect(handleSetSelectType).toHaveBeenCalledWith(
      ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY
    );
  });

  it("changes select type to STATE", () => {
    const handleSetSelectType = jest.fn();
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          selectType: ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY,
          searchState: "Test State",
        },
      },
      handleSetSelectType,
    });
    render(<PortifolioDiagnosisFormManually />);
    const stateRadio = screen.getByTestId("radio-button-state");
    fireEvent.click(stateRadio);

    expect(handleSetSelectType).toHaveBeenCalledWith(
      ReportPortifolioDiagnosisManualSelectType.STATE
    );
  });

  it("searches for a municipality", () => {
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          selectType: ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY,
        },
      },
    });
    render(<PortifolioDiagnosisFormManually />);
    const searchInput = screen
      .getByTestId("input-auto-complete-search-municipality")
      .querySelector("input") as Element;

    fireEvent.change(searchInput, { target: { value: "Test Municipality" } });
    expect(
      mockUsePortifolioDiagnosis().handleSearchMunicipality
    ).toHaveBeenCalledWith("Test Municipality");
  });

  it("searches for a state", () => {
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          selectType: ReportPortifolioDiagnosisManualSelectType.STATE,
        },
      },
    });
    render(<PortifolioDiagnosisFormManually />);
    const searchInput = screen
      .getByTestId("input-auto-complete-search-state")
      .querySelector("input") as Element;
    fireEvent.change(searchInput, { target: { value: "Test State" } });
    expect(mockUsePortifolioDiagnosis().handleSearchState).toHaveBeenCalledWith(
      "Test State"
    );
  });

  it("should remove state when button remove is clicked", () => {
    const handleClickRemoveState = jest.fn();
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      handleClickRemoveState,
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          searchState: "Test State",

          states: [
            {
              name: "Test State",
              uf: "TS",
            },
          ],
          statesSelected: [
            {
              name: "Test State",
              uf: "TS",
            },
          ],
        },
      },
    });
    const { getByTestId } = render(<PortifolioDiagnosisFormManually />);
    const button = getByTestId("option-action-item-remove");
    fireEvent.click(button);

    expect(handleClickRemoveState).toHaveBeenCalled();
  });
  it("should remove municipality when button remove is clicked", () => {
    const handleClickRemoveMunicipality = jest.fn();
    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...mockUsePortifolioDiagnosis(),
      handleClickRemoveMunicipality,
      data: {
        ...mockUsePortifolioDiagnosis().data,
        manual: {
          ...mockUsePortifolioDiagnosis().data.manual,
          selectType: ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY,
          searchMunicipality: "Test Municipality",
          municipalities: [
            {
              ibgeCode: 123,
              name: "Test Municipality",
              uf: "TS",
            },
          ],
          municipaliciesSelected: [
            {
              ibgeCode: 123,
              name: "Test Municipality",
              uf: "TS",
            },
          ],
        },
      },
    });
    const { getByTestId } = render(<PortifolioDiagnosisFormManually />);
    const button = getByTestId("option-action-item-remove");
    fireEvent.click(button);

    expect(handleClickRemoveMunicipality).toHaveBeenCalled();
  });

  it("should be able to choose options of submodules on a multiselect", () => {
    const subModules = [
      { description: "module1", report_type: "module1" },
      { description: "module2", report_type: "module2" },
    ];

    const handleChangeManualSubModulesMock = jest.fn();

    (useAccountDetails as jest.Mock).mockReturnValueOnce({
      ...useAccountDetails(),
      account: {
        ...useAccountDetails().account,
        resources: [Resource.PortfolioDiagnosisCreditEnableFormManual],
      },
    });

    mockUsePortifolioDiagnosis.mockReturnValueOnce({
      ...usePortifolioDiagnosis(),
      data: {
        ...usePortifolioDiagnosis().data,
        requestMethod: RequestMethod.MANUAL,
        manual: {
          ...usePortifolioDiagnosis().data.manual,
          subModules: subModules,
          subModulesSelected: [subModules[1]],
        },
      },
      handleChangeManualSubModules: handleChangeManualSubModulesMock,
    });

    const { getByTestId, getByLabelText } = render(
      <PortifolioDiagnosisFormManually />
    );

    const select = getByTestId("select-multi-select").querySelector("input");

    fireEvent.mouseDown(select);

    const option = getByTestId(
      `option-multi-select-${subModules[0].report_type}`
    );

    fireEvent.click(option);

    expect(handleChangeManualSubModulesMock).toHaveBeenCalled();
    expect(getByLabelText(subModules[0].description)).toBeInTheDocument();
  });
});
