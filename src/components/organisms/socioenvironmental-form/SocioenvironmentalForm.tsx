import { CloseOutlined } from "@ant-design/icons";
import { ReportFormProps } from "@types";
import { Typography } from "antd";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  CheckboxDropdown,
  CheckboxItem,
} from "../../molecules/checkbox-dropdown/CheckboxDropdown";
import {
  SocioEnvironmentalFormContainer,
  TagsContainer,
} from "./SocioenvironmentalForm.styles";
import { useSocioEnvironmentCriterias } from "@/hooks/use-socio-environment-criterias";

function SocioenvironmentalForm({
  data,
  isSubmitted,
  onChange,
}: ReportFormProps) {
  const { t } = useTranslation();
  const { socioEnvironmentalCriterias, handleSetSocioEnvironmentalCriterias } =
    useSocioEnvironmentCriterias();
  const [isOpenCheckboxContainer, setIsOpenCheckboxContainer] = useState(false);
  const [isCheckAllSelected, setIsCheckAllSelected] = useState(false);

  const hasDataQueries = data?.data?.queries && data?.data?.queries?.length;

  function handleChange() {
    let queries = socioEnvironmentalCriterias
      .filter((item) => item.checked)
      .map((item) => item.value);

    queries.forEach((query) => {
      if (query.startsWith("EMBARGO")) queries.push(`${query}_DOCUMENT`);
    });

    onChange({ ...data, data: { ...data.data, queries } });
  }

  function handleChangeCheckAll(event: CheckboxChangeEvent) {
    setIsCheckAllSelected(event.target.checked);
    const isChecked = event.target.checked === true;

    handleSetSocioEnvironmentalCriterias({
      socioEnvironmentalCriterias: socioEnvironmentalCriterias.map((item) => ({
        ...item,
        checked: isChecked,
      })),
    });
  }

  function handleChangeCheckboxItem(
    event: CheckboxChangeEvent,
    checkboxItem: CheckboxItem
  ) {
    if (isCheckAllSelected) {
      setIsCheckAllSelected(false);

      handleSetSocioEnvironmentalCriterias({
        socioEnvironmentalCriterias: socioEnvironmentalCriterias.map((item) => {
          if (item.value === checkboxItem.value) {
            return { ...item, checked: false };
          }
          return item;
        }),
      });

      return;
    }

    const mapped = socioEnvironmentalCriterias.map((item) => {
      if (checkboxItem.value === item.value) {
        return { ...item, checked: event.target.checked };
      }
      return item;
    });

    if (mapped.some((item) => !item.checked)) {
      setIsCheckAllSelected(false);
    }

    handleSetSocioEnvironmentalCriterias({
      socioEnvironmentalCriterias: mapped,
    });
  }

  function handleClickTag(value: string) {
    const updated = socioEnvironmentalCriterias.map((item) => {
      if (item.value === value) {
        return { ...item, checked: false };
      }
      return item;
    });

    handleSetSocioEnvironmentalCriterias({
      socioEnvironmentalCriterias: updated,
    });
    if (updated.some((item) => !item.checked)) {
      setIsCheckAllSelected(false);
    }
  }

  useEffect(() => {
    handleChange();
    if (!socioEnvironmentalCriterias.length) {
      setIsCheckAllSelected(false);
    }
  }, [socioEnvironmentalCriterias]);

  return (
    <SocioEnvironmentalFormContainer data-testid="pb-t-socioenvironmental-form">
      <TagsContainer
        id="tags-input-container"
        onClick={() => setIsOpenCheckboxContainer(true)}
      >
        {!hasDataQueries && (
          <Typography.Text
            key="SOCIOENVIRONMENTAL_CRITERIA"
            className="text-placeholder"
          >
            {t("SOCIOENVIRONMENTAL_CRITERIA")}
          </Typography.Text>
        )}
        {data.data?.queries?.map((value: string) => (
          <div className="div-close-tag-container">
            {t(value)}
            <button
              key={JSON.stringify(value)}
              type="button"
              onClick={() => handleClickTag(value)}
            >
              <CloseOutlined />
            </button>
          </div>
        ))}
      </TagsContainer>
      <CheckboxDropdown
        checkboxItems={socioEnvironmentalCriterias}
        isCheckAllSelected={isCheckAllSelected}
        onChangeCheckAll={handleChangeCheckAll}
        onChangeCheckboxItem={handleChangeCheckboxItem}
        placeholder={t("SOCIOENVIRONMENTAL_CRITERIA")}
        onCloseDropdown={() => {
          handleChange();
          setIsOpenCheckboxContainer(false);
        }}
        hideInput={true}
        isOpenCheckboxContainerFromOutsideOfComponent={isOpenCheckboxContainer}
      />
    </SocioEnvironmentalFormContainer>
  );
}

export default SocioenvironmentalForm;
