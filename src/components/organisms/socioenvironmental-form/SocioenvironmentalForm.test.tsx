import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import SocioenvironmentalForm from "./SocioenvironmentalForm";

afterEach(() => {
  cleanup();
});

describe("SocioenvironmentalForm component", () => {
  it("should render the component", () => {
    render(
      <SocioenvironmentalForm
        data={{}}
        isSubmitted={false}
        onChange={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-socioenvironmental-form");
    expect(component).toBeInTheDocument();
  });
});
