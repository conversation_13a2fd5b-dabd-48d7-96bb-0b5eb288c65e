import styled from "styled-components";

export const SocioEnvironmentalFormContainer = styled.div`
  display: flex;
  width: 100%;
  position: relative;
  flex-direction: column;
  z-index: 10;
`;
export const TagsContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 8px;
  border: 1.5px solid #d9d9d9;
  width: 100%;
  min-height: 48px;
  padding: 4px 2px;
  flex-wrap: wrap;
  border-radius: 6px;
  background: #fff;
  position: relative;

  .text-placeholder {
    margin-left: 4px;
  }

  .div-close-tag-container {
    display: block;
    position: relative;
    border-radius: 6px;
    padding: 2px 4px 0px 4px;
    width: 94px;
    height: 24px;
    text-justify: center;
    align-content: center;
    justify-content: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    border: 1.5px solid #d9d9d9;
    font-size: 10px;
    margin: 4px;
    background: rgba(0, 0, 0, 0.01);

    button {
      color: #000;
      font-size: 10px;
      position: absolute;
      right: 4px;
      background: transparent;
      border: 0;
      align-content: center;
      z-index: 1;
      background: #fff;
      &:hover {
        cursor: pointer;
      }
    }
  }
`;
