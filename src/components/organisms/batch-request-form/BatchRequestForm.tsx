import { InfoCircleFilled } from "@ant-design/icons";
import { FormListItem } from "@components";
import { MODEL_BY_REPORT_TYPE } from "@constants";
import { FileRequest, ReportType } from "@types";
import { notification, Radio } from "antd";
import <PERSON><PERSON> from "antd/lib/upload/Dragger";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";
import { icon_cloud_download, icon_cloud_upload } from "../../../assets";
import {
  BatchRequestInputRow,
  DraggerHint,
  DraggerText,
  DraggerWrapper,
  HighlightedText,
  ModelDownload,
  UploadedFile,
  UploadedFilesList,
} from "./BatchRequestForm.styles";

interface BatchRequestFormProps {
  reportType: ReportType;
  files: FileRequest[];
  onChange: (files: FileRequest[]) => void;
  onChangeReportType: (reportType: ReportType) => void;
}

function BatchRequestForm({
  reportType,
  files,
  onChange,
  onChangeReportType,
}: BatchRequestFormProps) {
  const { t } = useTranslation();
  const [currentReportType, setCurrentReportType] = useState(
    ReportType.VALUATION
  );

  useEffect(() => {
    setCurrentReportType(reportType);
  }, [reportType]);

  function beforeUpload(file) {
    const reader = new FileReader();
    if (file.size / (1024 * 1000) > 100) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 100 }),
      });
      return;
    }
    if (files.find((f: FileRequest) => f.name == file.name)) {
      notification.warning({
        message: t("ALERT_FILE_SAME_NAME"),
      });
      return;
    }
    reader.onload = async ({ target }) => {
      const lineCount = (target.result as string)
        .trim()
        .replace(/""|^\n/g, "") // Remove "" e linhas em branco antes do cabecalho
        .replace(/"[^"]+"/g, "") // Remove \n entre aspas duplas
        .match(/\n/g).length; // Conta a quantidade de \n

      const newFiles = [
        ...files,
        {
          tempId: uuidv4(),
          name: file.name,
          file,
          lineCount,
        },
      ];
      onChange(newFiles);
    };

    reader.readAsText(file);
  }

  return (
    <div data-testid="pb-t-batch-request-form">
      {[ReportType.INSPECTION, ReportType.INSPECTION_FINANCIAL].includes(
        reportType
      ) && (
        <Radio.Group
          size="small"
          data-testid="radio-choose-bacen-and-standard"
          value={currentReportType}
          onChange={({ target }) => {
            setCurrentReportType(target.value);
            onChangeReportType(target.value);
          }}
          style={{ marginBottom: "16px" }}
        >
          <Radio.Button value={ReportType.INSPECTION_FINANCIAL}>
            {t("BACEN_REPORT")}
          </Radio.Button>
          <Radio.Button value={ReportType.INSPECTION}>
            {t("STANDART")}
          </Radio.Button>
        </Radio.Group>
      )}
      <BatchRequestInputRow>
        <ModelDownload
          href={MODEL_BY_REPORT_TYPE[currentReportType]}
          download
          target="_blank"
        >
          <InfoCircleFilled style={{ color: "#F4BB41", fontSize: "18px" }} />
          <p>
            <HighlightedText>{t("HINT_REPORT_MODEL_DOWNLOAD")}</HighlightedText>
          </p>
          <img src={icon_cloud_download} alt="Download model" />
        </ModelDownload>
        <Dragger
          beforeUpload={beforeUpload}
          customRequest={() => {}}
          fileList={[]}
          data-testid="dragger-file-upload"
        >
          <DraggerWrapper>
            <DraggerText className="ant-upload-text">
              {t("HINT_FILE_UPLOAD")}
            </DraggerText>
            <img
              src={icon_cloud_upload}
              alt="Upload file"
              width="24"
              height="24"
            />
            <DraggerHint className="ant-upload-hint">
              {t("DRAG_DROP_OR")}{" "}
              <HighlightedText>
                {t("CLICK_TO_UPLOAD_FROM_FILES")}
              </HighlightedText>
              .
            </DraggerHint>
          </DraggerWrapper>
        </Dragger>
      </BatchRequestInputRow>

      <UploadedFilesList>
        {files.map((file, index) => (
          <FormListItem
            multiple
            key={file.tempId}
            count={index + 1}
            onRemove={() => {
              onChange(files.filter((f) => f.tempId !== file.tempId));
            }}
            disabled={false}
            canRemove={true}
          >
            <UploadedFile>
              <div>
                <p>{t("SELECTED_FILE")}</p>
                <p>{file.name}</p>
              </div>

              <div>
                <p>{t("FILE_TOTAL")}</p>
                <p>
                  {file.lineCount}{" "}
                  {file.lineCount != 1 ? t("REPORTS") : t("REPORT")}
                </p>
              </div>
            </UploadedFile>
          </FormListItem>
        ))}
      </UploadedFilesList>
    </div>
  );
}

export default BatchRequestForm;
