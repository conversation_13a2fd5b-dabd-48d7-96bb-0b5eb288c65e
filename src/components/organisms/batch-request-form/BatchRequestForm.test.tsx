import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import BatchRequestForm from "./BatchRequestForm";
import { FileRequest, ReportType } from "@types";
import Papa from "papaparse";
import { notification } from "antd";
import { useTranslation } from "react-i18next";

jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(() => ({
    t: (text) => text,
  })),
}));

jest.mock("antd", () => {
  const antd = jest.requireActual("antd");
  return {
    ...antd,
    notification: {
      warning: jest.fn(),
      error: jest.fn(),
    },
  };
});

describe("BatchRequestForm component", () => {
  const notificationWarningMock = jest.fn();

  beforeEach(() => {
    (notification.warning as jest.Mock).mockImplementation(
      notificationWarningMock
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(
      <BatchRequestForm
        files={[]}
        onChange={() => {}}
        onChangeReportType={() => {}}
        reportType={ReportType.VALUATION}
      />
    );
    const component = screen.queryByTestId("pb-t-batch-request-form");
    expect(component).toBeInTheDocument();
  });

  it("should call onChangeReportType when radio button is clicked", () => {
    const onChangeReportTypeMock = jest.fn();
    const { getByTestId, getByText } = render(
      <BatchRequestForm
        files={[]}
        onChange={() => {}}
        onChangeReportType={onChangeReportTypeMock}
        reportType={ReportType.INSPECTION}
      />
    );

    const radio = getByTestId("radio-choose-bacen-and-standard").querySelector(
      "input"
    );

    fireEvent.click(radio);

    expect(onChangeReportTypeMock).toHaveBeenCalled();
  });

  it("should call onChange if dragger receives file", async () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <BatchRequestForm
        files={[]}
        onChange={onChangeMock}
        onChangeReportType={() => {}}
        reportType={ReportType.INSPECTION}
      />
    );

    const dragger = getByTestId("dragger-file-upload");

    const csvData = Papa.unparse(
      [{ documento: "98976362934", "dívida(R$)": "1000" }],
      {
        delimiter: ";",
      }
    );

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [
          new File([csvData.trim()], "filename.csv", { type: "text/csv" }),
        ],
      },
    });

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalled();
    });
  });

  it("should show a notification if file is too large", async () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <BatchRequestForm
        files={[]}
        onChange={onChangeMock}
        onChangeReportType={() => {}}
        reportType={ReportType.INSPECTION}
      />
    );
    const dragger = getByTestId("dragger-file-upload");

    const blob = new Blob([new Uint8Array(101 * 1024 * 1024)], {
      type: "text/csv",
    });

    const file = new File([blob], "filename.csv", {
      type: "text/csv",
    });

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [file],
      },
    });

    await waitFor(() => {
      expect(notificationWarningMock).toHaveBeenCalledWith({
        message: "FILE_NOT_SENT",
        description: "ALERT_FILE_SIZE",
      });
    });
  });

  it("should show notification if file is already on list", async () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <BatchRequestForm
        files={[{ name: "filename.csv" } as FileRequest]}
        onChange={onChangeMock}
        onChangeReportType={() => {}}
        reportType={ReportType.INSPECTION}
      />
    );

    const dragger = getByTestId("dragger-file-upload");

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [new File(["data"], "filename.csv", { type: "text/csv" })],
      },
    });

    await waitFor(() => {
      expect(notificationWarningMock).toHaveBeenCalledWith({
        message: "ALERT_FILE_SAME_NAME",
      });
      expect(onChangeMock).not.toHaveBeenCalled();
    });
  });

  it("should remove file when button is clicked", () => {
    const files: FileRequest[] = [
      { name: "filename", tempId: "123", lineCount: 1, file: {} as File },
    ];

    const onChangeMock = jest.fn();

    const { getByText, getByTestId } = render(
      <BatchRequestForm
        files={files}
        onChange={onChangeMock}
        onChangeReportType={() => {}}
        reportType={ReportType.INSPECTION}
      />
    );

    const button = getByTestId("pb-t-form-list-item-action");

    fireEvent.click(button);

    expect(onChangeMock).toHaveBeenCalled();
  });
});
