import styled from "styled-components";

export const BatchRequestInputRow = styled.div`
  display: flex;
  gap: 10px;
  width: 100%;

  > span {
    flex-grow: 1;
  }
`;

export const ModelDownload = styled.a`
  display: block;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fdfdfd;
  border: 1px solid #f0efef;
  border-radius: 4px;
  width: 200px;
  padding: 16px;
  gap: 10px;

  p {
    text-align: center;
    line-height: 1;
    margin-bottom: 0;
  }
`;

export const HighlightedText = styled.span`
  color: #10a488;
`;

export const DraggerWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
`;

export const DraggerText = styled.p`
  max-width: 26ch;
  text-align: center;
  margin: 0 auto !important;
  line-height: 1.25;
`;

export const DraggerHint = styled.p`
  max-width: 50ch;
  text-align: center;
  margin: 0 auto !important;
  line-height: 1.25;
`;

export const UploadedFilesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
`;

export const UploadedFile = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;

  p {
    line-height: 1.5;
  }
`;
