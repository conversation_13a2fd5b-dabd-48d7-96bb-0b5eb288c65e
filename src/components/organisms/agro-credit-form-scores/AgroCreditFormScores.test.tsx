import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import AgroCreditFormScores from "./AgroCreditFormScores";

afterEach(() => {
  cleanup();
});

describe("AgroCreditFormScores component", () => {
  it("should render the component", () => {
    render(
      <AgroCreditFormScores
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-agro-credit-scores");
    expect(component).toBeInTheDocument();
  });

  it("should render the component 2", () => {
    render(
      <AgroCreditFormScores
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );
    const component = screen.queryByTestId("checkbox-score-select");

    fireEvent.click(component!);
    expect(component).toBeInTheDocument();
  });
});
