import { useTranslation } from "react-i18next";
import {
  Card<PERSON>rapper,
  CheckboxLabel,
  Content,
  Options,
  ScoreCard,
  Wrapper,
} from "./AgroCreditFormScores.style";
import { Button, Checkbox } from "antd";
import { useContext, useState } from "react";
import { RequestFormContext } from "@contexts";
import { AgroCreditFormState } from "../agro-credit-form/AgroCreditForm";

interface AgroCreditFormReviewProps {
  data: AgroCreditFormState;
  onChange: (data?: AgroCreditFormState) => void;
  onBack: () => void;
  onNext: () => void;
}

function AgroCreditFormScores({
  data,
  onBack,
  onChange,
  onNext,
}: AgroCreditFormReviewProps) {
  const { t } = useTranslation();
  const { closeRequestForm } = useContext(RequestFormContext);

  function handleClose() {
    onChange();
    closeRequestForm();
  }

  return (
    <Wrapper data-testid="pb-t-agro-credit-scores">
      <Content>
        <section>
          <h2>{t("REPORT_SCORE_SELECT")}</h2>
          <CardWrapper>
            <ScoreCard>
              <Checkbox
                data-testid="checkbox-score-select"
                checked={data.scores.includes("CREDIT_RISK")}
                onChange={() => {
                  if (data.scores.includes("CREDIT_RISK")) {
                    onChange({
                      ...data,
                      scores: data.scores.filter((x) => x != "CREDIT_RISK"),
                    });
                    return;
                  }
                  onChange({
                    ...data,
                    scores: [...data.scores, "CREDIT_RISK"],
                  });
                }}
              >
                <CheckboxLabel>{t("CREDIT_RISK")}</CheckboxLabel>
              </Checkbox>
              <p>{t("CREDIT_RISK_DESCRIPTION")}</p>
            </ScoreCard>
            <ScoreCard>
              <Checkbox disabled>
                <CheckboxLabel>{t("ESG_RISK")}</CheckboxLabel>
              </Checkbox>
              <p>{t("ESG_RISK_DESCRIPTION")}</p>
            </ScoreCard>
          </CardWrapper>
        </section>
      </Content>
      <Options>
        <Button onClick={handleClose}>{t("CANCEL")}</Button>
        <div style={{ display: "flex", gap: "16px" }}>
          <Button onClick={onBack}>{t("BACK")}</Button>
          <Button
            type="primary"
            onClick={onNext}
            disabled={!data.scores.length}
          >
            {t("NEXT")}
          </Button>
        </div>
      </Options>
    </Wrapper>
  );
}

export default AgroCreditFormScores;
