import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
  gap: 16px;
  margin: 16px 0;

  h1 {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  h2 {
    font-size: 16px;
    font-weight: 500;
  }
`;

export const Options = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const CardWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
`;

export const ScoreCard = styled.div`
  display: flex;
  flex-direction: column;
  border: 1px solid #e2e4e7;
  border-radius: 4px;
  padding: 16px 0px 8px 16px;
  gap: 8px;
`;

export const CheckboxLabel = styled.span`
  font-size: 16px;
  font-weight: 400;
`;
