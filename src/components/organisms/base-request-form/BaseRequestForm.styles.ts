import { Select } from "brainui";
import styled from "styled-components";

export const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
`;

export const IdentificationRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 200px;
  gap: 10px;
  overflow: hidden;
`;

export const InterestAreaRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 200px;
  gap: 10px;
  overflow: hidden;
`;

export const PropertySelect = styled(Select)`
  overflow: hidden;
`;

export const SubareaSelect = styled(Select)`
  overflow: hidden;
`;