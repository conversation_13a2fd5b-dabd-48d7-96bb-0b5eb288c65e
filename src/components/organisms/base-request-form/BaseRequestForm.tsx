import {
  <PERSON><PERSON><PERSON>,
  FieldsDrawer,
  NewPropertyDrawer,
} from "@components";

import { ReactNode, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { FormContainer } from "./BaseRequestForm.styles";
import { ReportType } from "@types";
import { useBaseRequestForm } from "@/hooks/use-base-request-form";
import {
  ContactSection,
  MapSection,
  ObservationsSection,
  PropertySection,
  SubareaSection
} from "./components";

export interface Contact {
  id: string;
  group_id?: number;
  groupId?: string;
  name: string;
  value: string;
  document_type: string;
}

export interface Property {
  id: string;
  area_name?: string;
  external_code?: string;
  area_coordinates?: any;
  isInternal?: boolean;
}

export interface Subarea {
  id: string;
  name: string;
  coordinates?: any;
}

export interface FormData {
  contact?: Contact;
  property?: Property;
  subareas?: Subarea[];
  observation?: string;
  [key: string]: any;
}

export interface BaseRequestFormProps {
  readonly data: FormData;
  readonly reportType: ReportType;
  readonly properties: Property[];
  readonly subareas: Subarea[];
  readonly selectedProperty: Property | null;
  readonly selectedSubareas: Subarea[];
  readonly hasSubareas?: boolean;
  readonly children?: ReactNode;
  readonly isSubmitted?: boolean;
  readonly onChange?: (value: FormData) => void;
  readonly onPropertiesLoad: (properties: Property[]) => void;
  readonly onSubareasLoad: (subareas: Subarea[]) => void;
  readonly toIntersect?: boolean;
}

function BaseRequestForm({
  data,
  reportType,
  hasSubareas,
  selectedProperty,
  selectedSubareas,
  subareas,
  properties,
  children,
  isSubmitted,
  onChange,
  onPropertiesLoad,
  onSubareasLoad,
  toIntersect,
}: BaseRequestFormProps) {
  const { t } = useTranslation();
  const {
    contacts,
    debouncedText,
    isContactFormOpen,
    isFieldsDrawerOpen,
    isLoadingContacts,
    isPropertyDrawerOpen,
    onCreateNewArea,
    propertySource,
    setIsContactFormOpen,
    setIsDrawingSubareas,
    setIsFieldsDrawerOpen,
    setIsPropertyDrawerOpen,
    setMap,
    setShowMap,
    showMap,
    subareaSource,
    fetchProperties,
    fetchSubareas,
  } = useBaseRequestForm({
    onPropertiesLoad,
    selectedSubareas,
    onSubareasLoad,
    properties,
    selectedProperty,
    data,
  });

  const handleContactChange = useCallback((selectedContact: Contact | undefined) => {
    if (!selectedContact) {
      const newValue = {
        ...data,
        contact: undefined,
        property: undefined,
        subareas: undefined,
      };
      onPropertiesLoad([]);
      onSubareasLoad([]);
      propertySource.clear();
      subareaSource.clear();
      onChange?.(newValue);
      return;
    }

    const contactWithStringName = {
      ...selectedContact,
      name: typeof selectedContact.name === 'string' ? selectedContact.name : String(selectedContact.name || '')
    };

    const newValue = {
      ...data,
      contact: contactWithStringName,
      property: undefined,
      subareas: undefined,
    };
    propertySource.clear();
    subareaSource.clear();
    fetchProperties(contactWithStringName);
    onChange?.(newValue);
  }, [data, fetchProperties, onChange, onPropertiesLoad, onSubareasLoad, propertySource, subareaSource]);

  const handlePropertySelect = useCallback((property: Property) => {
    let newValue = { ...data, property };

    if (hasSubareas) {
      newValue = { ...newValue, subareas: [] };
      subareaSource.clear();
      fetchSubareas(data.contact, property);
    }

    onChange?.(newValue);
  }, [data, fetchSubareas, hasSubareas, onChange, subareaSource]);

  const handleSubareasChange = useCallback((selectedIds: string[]) => {
    const newSubareas = selectedIds
      .map(id => subareas.find(x => x.id === id))
      .filter(Boolean);

    const newValue = {
      ...data,
      subareas: newSubareas,
    };

    subareaSource.clear();
    onChange?.(newValue);
  }, [data, onChange, subareas, subareaSource]);

  const handleObservationChange = useCallback((observation: string) => {
    onChange?.({
      ...data,
      observation,
    });
  }, [data, onChange]);

  return (
    <div data-testid="pb-t-base-request-form">
      <ContactForm
        open={isContactFormOpen}
        onClose={() => setIsContactFormOpen(false)}
        onSuccess={() => setIsContactFormOpen(false)}
      />
      <NewPropertyDrawer
        contact={data.contact ? {
          id: Number(data.contact.id),
          group_id: data.contact.group_id || 0
        } : undefined}
        open={isPropertyDrawerOpen}
        onClose={() => setIsPropertyDrawerOpen(false)}
        onSuccess={onCreateNewArea}
      />
      <FieldsDrawer
        open={isFieldsDrawerOpen}
        registedFields={subareas}
        onClose={() => setIsFieldsDrawerOpen(false)}
        onSuccess={() => onCreateNewArea({})}
        initialProperty={data.property}
        toIntersect={toIntersect}
      />
      <FormContainer>
        <ContactSection
          data={data}
          reportType={reportType}
          isSubmitted={isSubmitted}
          isLoadingContacts={isLoadingContacts}
          contacts={contacts}
          debouncedText={debouncedText}
          onContactChange={handleContactChange}
          onOpenContactForm={() => setIsContactFormOpen(true)}
        />

        <PropertySection
          data={data}
          reportType={reportType}
          properties={properties}
          isSubmitted={isSubmitted}
          hasSubareas={hasSubareas}
          onPropertySelect={(id) => {
            const property = properties.find((p) => p.id == id);
            if (property) handlePropertySelect(property);
          }}
          setIsPropertyDrawerOpen={setIsPropertyDrawerOpen}
          setIsDrawingSubareas={setIsDrawingSubareas}
        />

        {hasSubareas && (
          <SubareaSection
            data={data}
            subareas={subareas}
            selectedSubareas={selectedSubareas || []}
            subareaSource={subareaSource}
            onChange={(newData) => {
              if (Array.isArray(newData)) {
                handleSubareasChange(newData);
              } else {
                onChange?.(newData);
              }
            }}
            setIsDrawingSubareas={setIsDrawingSubareas}
            setIsFieldsDrawerOpen={setIsFieldsDrawerOpen}
          />
        )}

        {children}

        <MapSection
          showMap={showMap}
          setShowMap={setShowMap}
          setMap={setMap}
          propertySource={propertySource}
          subareaSource={subareaSource}
        />

        <ObservationsSection
          observation={data.observation || ''}
          onChange={handleObservationChange}
        />
      </FormContainer>
    </div>
  );
}
export default BaseRequestForm;
