import { <PERSON><PERSON> } from "brainui";
import { useTranslation } from "react-i18next";
import { Spin } from "antd";
import { ReportType } from "@types";
import { REPORT_CARD_METADATA } from "@constants";
import { ContactSelectContainer, CustomSelectWrapper } from "./ContactSection.styles";
import { CustomSelect } from "../../../atoms/custom-select";

interface Contact {
  id: string;
  groupId?: string;
  name: string;
  value: string;
  document_type: string;
}

interface ContactSectionProps {
  data: {
    contact?: Contact;
    [key: string]: any;
  };
  reportType: ReportType;
  isSubmitted?: boolean;
  isLoadingContacts: boolean;
  contacts: any[];
  debouncedText: (value: string) => void;
  onContactChange: (contact: Contact | undefined) => void;
  onOpenContactForm: () => void;
}

export const ContactSection = ({
  data,
  reportType,
  isSubmitted,
  isLoadingContacts,
  contacts,
  debouncedText,
  onContactChange,
  onOpenContactForm,
}: ContactSectionProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <ContactSelectContainer>
        <CustomSelectWrapper>
          <CustomSelect
            label={t("DOCUMENT_OR_NAME")}
            options={contacts.map(contact => ({
              label: contact.label,
              value: contact.value,
              document: contact.document,
              document_type: contact.document_type,
              className: ''
            }))}
            filterOption={false}
            showSearch
            onSearch={debouncedText}
            onChange={(value) => {
              try {
                if (value) {
                  const contactValue = JSON.parse(value);
                  onContactChange(contactValue);
                } else {
                  onContactChange(undefined);
                }
              } catch (error) {
                onContactChange(undefined);
              }
            }}
            onClear={() => onContactChange(undefined)}
            notFoundContent={isLoadingContacts ? <Spin size="small" /> : null}
            value={data.contact ? JSON.stringify(data.contact) : ''}
            status={isSubmitted && !data.contact ? "error" : ""}
            disabled={false}
          />
        </CustomSelectWrapper>
        <Button
          type="link"
          onClick={onOpenContactForm}
          style={{ width:'100%' }}
        >
          {t("NEW_CONTACT")}
        </Button>
      </ContactSelectContainer>
      {isSubmitted && !data.contact && (
        <div role="alert" style={{ color: "red", marginBottom: 16 }}>
          {t(REPORT_CARD_METADATA[reportType].requiredContactMessage)}
        </div>
      )}
    </div>
  );
};

export default ContactSection;
