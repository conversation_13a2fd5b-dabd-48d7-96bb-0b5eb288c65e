import MapWrapper from "@/components/atoms/map-wrapper/MapWrapper";
import { PROPERTY_STYLE, SUBAREA_STYLE } from "@constants";
import { Button as AntdButton, Divider } from "antd";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import { memo } from "react";
import { useTranslation } from "react-i18next";
import { Map } from "ol";

interface MapSectionProps {
  showMap: boolean;
  setShowMap: (show: boolean) => void;
  setMap: (map: Map) => void;
  propertySource: VectorSource;
  subareaSource: VectorSource;
}

export const MapSection = ({
  showMap,
  setShowMap,
  setMap,
  propertySource,
  subareaSource,
}: MapSectionProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <Divider orientation="left" orientationMargin="0">
        <AntdButton
          data-testid="button-show-map"
          onClick={() => {
            setShowMap(!showMap);
          }}
          size="small"
          type="text"
        >
          {showMap ? t("HIDE_MAP") : t("SHOW_MAP")}
        </AntdButton>
      </Divider>
      {showMap && (
        <MapWrapper
          onClick={() => {}}
          onMapReady={(map) => {
            const propertyLayer = new VectorLayer({
              source: propertySource,
              style: (feature) => PROPERTY_STYLE(feature),
            });
            const subareaLayer = new VectorLayer({
              source: subareaSource,
              style: SUBAREA_STYLE,
            });
            map.addLayer(propertyLayer);
            map.addLayer(subareaLayer);
            setMap(map);
          }}
        />
      )}
    </div>
  );
};

export default memo(MapSection);
