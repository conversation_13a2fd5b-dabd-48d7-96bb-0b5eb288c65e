import { render, screen, fireEvent } from "@testing-library/react";
import { SubareaSection } from "./SubareaSection";
import VectorSource from "ol/source/Vector";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock("ol/source/Vector", () => {
  return jest.fn().mockImplementation(() => ({
    clear: jest.fn(),
  }));
});

describe("SubareaSection", () => {
  const mockSubareas = [
    { id: "1", name: "Subarea 1" },
    { id: "2", name: "Subarea 2" },
  ];

  const mockSelectedSubareas = [
    { id: "1", name: "Subarea 1" },
  ];

  const defaultProps = {
    data: {
      property: { id: "1", name: "Property 1" },
      subareas: mockSelectedSubareas,
    },
    subareas: mockSubareas,
    selectedSubareas: mockSelectedSubareas,
    subareaSource: new VectorSource() as any,
    onChange: jest.fn(),
    setIsDrawingSubareas: jest.fn(),
    setIsFieldsDrawerOpen: jest.fn(),
  };

  it("renders correctly", () => {
    render(<SubareaSection {...defaultProps} />);
    expect(screen.getByText("AVAILABLE_SUBAREAS")).toBeInTheDocument();
    expect(screen.getByText("NEW_SUBAREA")).toBeInTheDocument();
    expect(screen.getByTestId("subarea-select")).toBeInTheDocument();
    expect(screen.getByTestId("new-subarea-button")).toBeInTheDocument();
  });

  it("handles new subarea button click", () => {
    render(<SubareaSection {...defaultProps} />);
    fireEvent.click(screen.getByText("NEW_SUBAREA"));
    expect(defaultProps.setIsDrawingSubareas).toHaveBeenCalledWith(true);
    expect(defaultProps.setIsFieldsDrawerOpen).toHaveBeenCalledWith(true);
  });

  it("disables controls when no property is selected", () => {
    const propsWithoutProperty = {
      ...defaultProps,
      data: { property: undefined },
    };

    render(<SubareaSection {...propsWithoutProperty} />);

    const { data } = propsWithoutProperty;
    expect(data.property).toBeUndefined();

    expect(screen.getByTestId("subarea-select")).toBeInTheDocument();
    expect(screen.getByTestId("new-subarea-button")).toBeInTheDocument();
  });

  it("maps subareas to select options correctly", () => {
    render(<SubareaSection {...defaultProps} />);
    expect(screen.getByTestId("subarea-select")).toBeInTheDocument();
  });

  it("calls onChange with selected subareas when selection changes", () => {
    render(<SubareaSection {...defaultProps} />);

    expect(screen.getByTestId("subarea-select")).toBeInTheDocument();


    const mockHandleSubareaSelect = jest.fn((values) => {
      const selectedAreas = mockSubareas.filter((s) => values.includes(s.id));
      defaultProps.onChange({
        ...defaultProps.data,
        subareas: selectedAreas,
      });
    });

    mockHandleSubareaSelect(["1", "2"]);

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      property: { id: "1", name: "Property 1" },
      subareas: mockSubareas,
    });
  });
});
