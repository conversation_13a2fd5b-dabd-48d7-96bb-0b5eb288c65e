import { render, screen, fireEvent } from "@testing-library/react";
import { ReportType } from "@types";
import ContactSection from "./ContactSection";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe("ContactSection", () => {
  const defaultProps = {
    data: {},
    isSubmitted: false,
    reportType: ReportType.AGRO_CREDIT,
    contacts: [],
    isLoadingContacts: false,
    isContactFormOpen: false,
    setIsContactFormOpen: jest.fn(),
    onContactChange: jest.fn(),
    onContactClear: jest.fn(),
    debouncedText: jest.fn(),
  };

  it("renders correctly", () => {
    render(<ContactSection {...defaultProps} onOpenContactForm={jest.fn()} />);
    expect(screen.getByText("DOCUMENT_OR_NAME")).toBeInTheDocument();
    expect(screen.getByText("NEW_CONTACT")).toBeInTheDocument();
  });
});