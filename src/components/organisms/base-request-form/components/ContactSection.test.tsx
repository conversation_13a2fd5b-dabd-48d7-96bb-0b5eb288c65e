import { render, screen, fireEvent } from "@testing-library/react";
import { ReportType } from "@types";
import ContactSection from "./ContactSection";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock("@constants", () => ({
  REPORT_CARD_METADATA: {
    INSPECTION: {
      requiredContactMessage: "CONTACT_REQUIRED_MESSAGE",
    },
    AGRO_CREDIT: {
      requiredContactMessage: "CONTACT_REQUIRED_AGRO_MESSAGE",
    },
  },
}));

jest.mock("../../../atoms/custom-select", () => ({
  CustomSelect: ({
    label,
    options,
    onChange,
    onClear,
    onSearch,
    value,
    status,
    disabled,
    notFoundContent
  }: any) => (
    <div data-testid="mock-custom-select">
      <label>{label}</label>
      <select
        data-testid="select-input"
        value={value || ""}
        onChange={(e) => onChange && onChange(e.target.value)}
        disabled={disabled || false}
        data-status={status || ""}
      >
        <option value="">Select contact</option>
        {options?.map((option: any, index: number) => (
          <option key={index} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <button
        data-testid="clear-button"
        onClick={() => onClear && onClear()}
      >
        Clear
      </button>
      <input
        data-testid="search-input"
        onChange={(e) => onSearch && onSearch(e.target.value)}
        placeholder="Search"
      />
      {notFoundContent && <div data-testid="not-found-content">{notFoundContent}</div>}
    </div>
  ),
}));

jest.mock("brainui", () => ({
  Button: ({ children, onClick, type, style }: any) => (
    <button
      data-testid="mock-button"
      onClick={onClick}
      data-type={type}
      style={style}
    >
      {children}
    </button>
  ),
}));

jest.mock("antd", () => ({
  Spin: ({ size }: any) => (
    <div data-testid="mock-spin" data-size={size}>
      Loading...
    </div>
  ),
}));

describe("ContactSection", () => {
  const mockContacts = [
    {
      label: "John Doe",
      value: JSON.stringify({ id: "1", name: "John Doe", document: "12345678901", document_type: "cpf" }),
      document: "12345678901",
      document_type: "cpf",
    },
    {
      label: "Jane Smith",
      value: JSON.stringify({ id: "2", name: "Jane Smith", document: "98765432100", document_type: "cpf" }),
      document: "98765432100",
      document_type: "cpf",
    },
  ];

  const mockContactObjects = [
    { id: "1", name: "John Doe", value: "12345678901", document_type: "cpf" },
    { id: "2", name: "Jane Smith", value: "98765432100", document_type: "cpf" },
  ];

  const defaultProps = {
    data: {},
    isSubmitted: false,
    reportType: ReportType.AGRO_CREDIT,
    contacts: mockContacts,
    isLoadingContacts: false,
    debouncedText: jest.fn(),
    onContactChange: jest.fn(),
    onOpenContactForm: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly", () => {
    render(<ContactSection {...defaultProps} />);
    expect(screen.getByText("DOCUMENT_OR_NAME")).toBeInTheDocument();
    expect(screen.getByText("NEW_CONTACT")).toBeInTheDocument();
  });

  it("should render the component with all elements", () => {
    render(<ContactSection {...defaultProps} />);

    const customSelect = screen.getByTestId("mock-custom-select");
    const newContactButton = screen.getByTestId("mock-button");

    expect(customSelect).toBeInTheDocument();
    expect(newContactButton).toBeInTheDocument();
    expect(screen.getByText("NEW_CONTACT")).toBeInTheDocument();
  });

  it("should call onContactChange when a contact is selected", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const selectInput = screen.getByTestId("select-input");
    const contactValue = mockContacts[0].value;

    fireEvent.change(selectInput, { target: { value: contactValue } });

    expect(onContactChange).toHaveBeenCalledWith(JSON.parse(mockContacts[0].value));
  });

  it("should call onContactChange with undefined when clearing selection", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const clearButton = screen.getByTestId("clear-button");
    fireEvent.click(clearButton);

    expect(onContactChange).toHaveBeenCalledWith(undefined);
  });

  it("should call debouncedText when searching", () => {
    const debouncedText = jest.fn();
    render(<ContactSection {...defaultProps} debouncedText={debouncedText} />);

    const searchInput = screen.getByTestId("search-input");
    fireEvent.change(searchInput, { target: { value: "John" } });

    expect(debouncedText).toHaveBeenCalledWith("John");
  });

  it("should call onOpenContactForm when new contact button is clicked", () => {
    const onOpenContactForm = jest.fn();
    render(<ContactSection {...defaultProps} onOpenContactForm={onOpenContactForm} />);

    const newContactButton = screen.getByTestId("mock-button");
    fireEvent.click(newContactButton);

    expect(onOpenContactForm).toHaveBeenCalled();
  });

  it("should handle onChange with empty value", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "" } });

    expect(onContactChange).toHaveBeenCalledWith(undefined);
  });

  it("should handle onChange with invalid JSON", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "invalid-json" } });

    expect(onContactChange).toHaveBeenCalledWith(undefined);
  });

  it("should show error status when submitted without contact", () => {
    render(<ContactSection {...defaultProps} isSubmitted={true} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveAttribute("data-status", "error");
  });

  it("should not show error status when not submitted", () => {
    render(<ContactSection {...defaultProps} isSubmitted={false} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveAttribute("data-status", "");
  });

  it("should not show error status when submitted with contact", () => {
    const propsWithContact = {
      ...defaultProps,
      data: { contact: mockContactObjects[0] },
      isSubmitted: true,
    };

    render(<ContactSection {...propsWithContact} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveAttribute("data-status", "");
  });

  it("should show error message when submitted without contact", () => {
    render(<ContactSection {...defaultProps} isSubmitted={true} />);

    expect(screen.getByText("CONTACT_REQUIRED_AGRO_MESSAGE")).toBeInTheDocument();
    expect(screen.getByRole("alert")).toBeInTheDocument();
  });

  it("should not show error message when not submitted", () => {
    render(<ContactSection {...defaultProps} isSubmitted={false} />);

    expect(screen.queryByText("CONTACT_REQUIRED_AGRO_MESSAGE")).not.toBeInTheDocument();
    expect(screen.queryByRole("alert")).not.toBeInTheDocument();
  });

  it("should not show error message when submitted with contact", () => {
    const propsWithContact = {
      ...defaultProps,
      data: { contact: mockContactObjects[0] },
      isSubmitted: true,
    };

    render(<ContactSection {...propsWithContact} />);

    expect(screen.queryByText("CONTACT_REQUIRED_AGRO_MESSAGE")).not.toBeInTheDocument();
    expect(screen.queryByRole("alert")).not.toBeInTheDocument();
  });

  it("should show loading spinner when contacts are loading", () => {
    render(<ContactSection {...defaultProps} isLoadingContacts={true} />);

    expect(screen.getByTestId("not-found-content")).toBeInTheDocument();
    expect(screen.getByTestId("mock-spin")).toBeInTheDocument();
  });

  it("should not show loading spinner when contacts are not loading", () => {
    render(<ContactSection {...defaultProps} isLoadingContacts={false} />);

    expect(screen.queryByTestId("not-found-content")).not.toBeInTheDocument();
    expect(screen.queryByTestId("mock-spin")).not.toBeInTheDocument();
  });

  it("should display current contact value when contact is selected", () => {
    const selectedContact = mockContactObjects[0];
    const propsWithContact = {
      ...defaultProps,
      data: { contact: selectedContact },
    };

    render(<ContactSection {...propsWithContact} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveValue(JSON.stringify(selectedContact));
  });

  it("should display empty value when no contact is selected", () => {
    render(<ContactSection {...defaultProps} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveValue("");
  });

  it("should render contacts in options", () => {
    render(<ContactSection {...defaultProps} />);

    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("Jane Smith")).toBeInTheDocument();
  });

  it("should handle different report types for error messages", () => {
    const propsWithInspection = {
      ...defaultProps,
      reportType: ReportType.INSPECTION,
      isSubmitted: true,
    };

    render(<ContactSection {...propsWithInspection} />);

    expect(screen.getByText("CONTACT_REQUIRED_MESSAGE")).toBeInTheDocument();
  });

  it("should set disabled prop correctly", () => {
    render(<ContactSection {...defaultProps} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).not.toBeDisabled();
  });
});