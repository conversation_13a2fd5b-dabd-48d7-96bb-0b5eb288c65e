import { render, screen, fireEvent } from "@testing-library/react";
import { ReportType } from "@types";
import ContactSection from "./ContactSection";

// Mock dependencies
jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock("@constants", () => ({
  REPORT_CARD_METADATA: {
    INSPECTION: {
      requiredContactMessage: "CONTACT_REQUIRED_MESSAGE",
    },
    AGRO_CREDIT: {
      requiredContactMessage: "CONTACT_REQUIRED_AGRO_MESSAGE",
    },
  },
}));

jest.mock("../../../atoms/custom-select", () => ({
  CustomSelect: ({
    label,
    options,
    onChange,
    onClear,
    onSearch,
    value,
    status,
    disabled,
    notFoundContent
  }: any) => (
    <div data-testid="mock-custom-select">
      <label>{label}</label>
      <select
        data-testid="select-input"
        value={value || ""}
        onChange={(e) => onChange && onChange(e.target.value)}
        disabled={disabled || false}
        data-status={status || ""}
      >
        <option value="">Select contact</option>
        {options?.map((option: any, index: number) => (
          <option key={index} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <button
        data-testid="clear-button"
        onClick={() => onClear && onClear()}
      >
        Clear
      </button>
      <input
        data-testid="search-input"
        onChange={(e) => onSearch && onSearch(e.target.value)}
        placeholder="Search"
      />
      {notFoundContent && <div data-testid="not-found-content">{notFoundContent}</div>}
    </div>
  ),
}));

jest.mock("brainui", () => ({
  Button: ({ children, onClick }: any) => (
    <button data-testid="mock-button" onClick={onClick}>
      {children}
    </button>
  ),
}));

jest.mock("antd", () => ({
  Spin: () => <div data-testid="mock-spin">Loading...</div>,
}));

describe("ContactSection", () => {
  const mockContact = { id: "1", name: "John Doe", value: "12345678901", document_type: "cpf" };
  const mockContacts = [
    {
      label: "John Doe",
      value: JSON.stringify(mockContact),
      document: "12345678901",
      document_type: "cpf",
    },
  ];

  const defaultProps = {
    data: {},
    isSubmitted: false,
    reportType: ReportType.AGRO_CREDIT,
    contacts: mockContacts,
    isLoadingContacts: false,
    debouncedText: jest.fn(),
    onContactChange: jest.fn(),
    onOpenContactForm: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render correctly with all elements", () => {
    render(<ContactSection {...defaultProps} />);

    expect(screen.getByText("DOCUMENT_OR_NAME")).toBeInTheDocument();
    expect(screen.getByText("NEW_CONTACT")).toBeInTheDocument();
    expect(screen.getByTestId("mock-custom-select")).toBeInTheDocument();
    expect(screen.getByTestId("mock-button")).toBeInTheDocument();
  });

  it("should call onContactChange when a contact is selected", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const selectInput = screen.getByTestId("select-input");
    const contactValue = mockContacts[0].value;

    fireEvent.change(selectInput, { target: { value: contactValue } });

    expect(onContactChange).toHaveBeenCalledWith(JSON.parse(mockContacts[0].value));
  });

  it("should call onContactChange with undefined when clearing selection", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const clearButton = screen.getByTestId("clear-button");
    fireEvent.click(clearButton);

    expect(onContactChange).toHaveBeenCalledWith(undefined);
  });

  it("should call debouncedText when searching", () => {
    const debouncedText = jest.fn();
    render(<ContactSection {...defaultProps} debouncedText={debouncedText} />);

    const searchInput = screen.getByTestId("search-input");
    fireEvent.change(searchInput, { target: { value: "John" } });

    expect(debouncedText).toHaveBeenCalledWith("John");
  });

  it("should call onOpenContactForm when new contact button is clicked", () => {
    const onOpenContactForm = jest.fn();
    render(<ContactSection {...defaultProps} onOpenContactForm={onOpenContactForm} />);

    const newContactButton = screen.getByTestId("mock-button");
    fireEvent.click(newContactButton);

    expect(onOpenContactForm).toHaveBeenCalled();
  });

  it("should handle onChange with empty value", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "" } });

    expect(onContactChange).toHaveBeenCalledWith(undefined);
  });

  it("should handle onChange with invalid JSON", () => {
    const onContactChange = jest.fn();
    render(<ContactSection {...defaultProps} onContactChange={onContactChange} />);

    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "invalid-json" } });

    expect(onContactChange).toHaveBeenCalledWith(undefined);
  });

  it("should show error status when submitted without contact", () => {
    render(<ContactSection {...defaultProps} isSubmitted={true} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveAttribute("data-status", "error");
  });

  it("should not show error status when submitted with contact", () => {
    const propsWithContact = {
      ...defaultProps,
      data: { contact: mockContact },
      isSubmitted: true,
    };

    render(<ContactSection {...propsWithContact} />);

    const selectInput = screen.getByTestId("select-input");
    expect(selectInput).toHaveAttribute("data-status", "");
  });

  it("should show error message when submitted without contact", () => {
    render(<ContactSection {...defaultProps} isSubmitted={true} />);

    expect(screen.getByText("CONTACT_REQUIRED_AGRO_MESSAGE")).toBeInTheDocument();
    expect(screen.getByRole("alert")).toBeInTheDocument();
  });

  it("should not show error message when submitted with contact", () => {
    const propsWithContact = {
      ...defaultProps,
      data: { contact: mockContact },
      isSubmitted: true,
    };

    render(<ContactSection {...propsWithContact} />);

    expect(screen.queryByText("CONTACT_REQUIRED_AGRO_MESSAGE")).not.toBeInTheDocument();
    expect(screen.queryByRole("alert")).not.toBeInTheDocument();
  });

  it("should show loading spinner when contacts are loading", () => {
    render(<ContactSection {...defaultProps} isLoadingContacts={true} />);

    expect(screen.getByTestId("not-found-content")).toBeInTheDocument();
    expect(screen.getByTestId("mock-spin")).toBeInTheDocument();
  });

  it("should handle different report types for error messages", () => {
    const propsWithInspection = {
      ...defaultProps,
      reportType: ReportType.INSPECTION,
      isSubmitted: true,
    };

    render(<ContactSection {...propsWithInspection} />);

    expect(screen.getByText("CONTACT_REQUIRED_MESSAGE")).toBeInTheDocument();
  });
});