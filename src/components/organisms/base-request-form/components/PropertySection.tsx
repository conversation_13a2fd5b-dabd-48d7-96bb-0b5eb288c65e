import { But<PERSON> } from "brainui";
import { useTranslation } from "react-i18next";
import { InterestAreaRow, PropertySelect } from "../BaseRequestForm.styles";
import { REPORT_CARD_METADATA } from "@constants";
import { ReportType } from "@types";

interface PropertySectionProps {
  data: any;
  reportType: ReportType;
  properties: any[];
  isSubmitted?: boolean;
  hasSubareas?: boolean;
  onPropertySelect: (id: string) => void;
  setIsPropertyDrawerOpen: (value: boolean) => void;
  setIsDrawingSubareas: (value: boolean) => void;
}

export const PropertySection = ({
  data,
  reportType,
  properties,
  isSubmitted,
  hasSubareas,
  onPropertySelect,
  setIsPropertyDrawerOpen,
  setIsDrawingSubareas,
}: PropertySectionProps) => {
  const { t } = useTranslation();

  return (
    <InterestAreaRow>
      <PropertySelect
        label={t("AVAILABLE_PROPERTIES")}
        value={data.property ? data.property.id : ""}
        disabled={
          REPORT_CARD_METADATA[reportType].isDocumentRequired && !data.contact
        }
        options={properties.map((d) => {
          let label = `${d.area_name || d.external_code} (${d.id})`;
          if (!d.area_name && !d.external_code) {
            label = d.id;
          }
          if (!d.id) {
            label = d.area_name || d.external_code;
          }
          return {
            value: d.id,
            label,
          };
        })}
        status={isSubmitted && !data.property ? "error" : ""}
        onSelect={onPropertySelect}
      />
      <Button
        onClick={() => {
          setIsDrawingSubareas(false);
          setIsPropertyDrawerOpen(true);
        }}
        disabled={
          REPORT_CARD_METADATA[reportType].isDocumentRequired && !data.contact
        }
      >
        {t("NEW_PROPERTY")}
      </Button>
    </InterestAreaRow>
  );
};