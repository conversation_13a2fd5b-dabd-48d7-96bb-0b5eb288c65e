import { render, screen, fireEvent } from "@testing-library/react";
import { PropertySection } from "./PropertySection";
import { ReportType } from "@types";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe("PropertySection", () => {
  const mockProperties = [
    { id: 1, area_name: "Area 1", external_code: "EXT1" },
    { id: 2, area_name: "Area 2", external_code: "EXT2" },
  ];

  const defaultProps = {
    data: { contact: { id: 1 } },
    reportType: ReportType.AGRO_CREDIT,
    properties: mockProperties,
    isSubmitted: false,
    hasSubareas: false,
    onPropertySelect: jest.fn(),
    setIsPropertyDrawerOpen: jest.fn(),
    setIsDrawingSubareas: jest.fn(),
  };

  it("renders correctly", () => {
    render(<PropertySection {...defaultProps} />);
    expect(screen.getByText("AVAILABLE_PROPERTIES")).toBeInTheDocument();
    expect(screen.getByText("NEW_PROPERTY")).toBeInTheDocument();
  });

  it("handles new property button click", () => {
    render(<PropertySection {...defaultProps} />);
    fireEvent.click(screen.getByText("NEW_PROPERTY"));
    expect(defaultProps.setIsDrawingSubareas).toHaveBeenCalledWith(false);
    expect(defaultProps.setIsPropertyDrawerOpen).toHaveBeenCalledWith(true);
  });
});