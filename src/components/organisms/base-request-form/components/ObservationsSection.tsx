import { TextArea } from "brainui";
import { memo } from "react";
import { useTranslation } from "react-i18next";

interface ObservationsSectionProps {
  observation: string;
  onChange: (observation: string) => void;
}

export const ObservationsSection = ({
  observation,
  onChange,
}: ObservationsSectionProps) => {
  const { t } = useTranslation();

  return (
    <TextArea
      data-testid="base-request-form-text-area-observations"
      placeholder={t("OBSERVATIONS_PLACEHOLDER")}
      autoSize={{ minRows: 3, maxRows: 4 }}
      value={observation}
      onChange={({ target }) => onChange(target.value)}
    />
  );
};

export default memo(ObservationsSection);
