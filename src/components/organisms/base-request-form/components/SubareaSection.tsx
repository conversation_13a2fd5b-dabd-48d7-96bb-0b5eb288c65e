import { Button } from "brainui";
import { useTranslation } from "react-i18next";
import VectorSource from "ol/source/Vector";
import { InterestAreaRow, SubareaSelect } from "../BaseRequestForm.styles";

interface SubareaSectionProps {
  data: {
    property?: any;
    subareas?: any[];
  };
  subareas: any[];
  selectedSubareas: any[];
  subareaSource: VectorSource;
  onChange: (value: any) => void;
  setIsDrawingSubareas: (value: boolean) => void;
  setIsFieldsDrawerOpen: (value: boolean) => void;
}

export const SubareaSection = ({
  data,
  subareas,
  selectedSubareas,
  subareaSource,
  onChange,
  setIsDrawingSubareas,
  setIsFieldsDrawerOpen,
}: SubareaSectionProps) => {
  const { t } = useTranslation();

  const handleSubareaSelect = (values: string[]) => {
    const selectedAreas = subareas.filter((s) => values.includes(s.id));
    onChange({
      ...data,
      subareas: selectedAreas,
    });
  };

  const handleNewSubarea = () => {
    setIsDrawingSubareas(true);
    setIsFieldsDrawerOpen(true);
  };

  const subareaOptions = subareas.map((subarea) => ({
    value: subarea.id,
    label: subarea.area_name || subarea.name,
  }));

  return (
    <InterestAreaRow>
      <SubareaSelect
        data-testid="subarea-select"
        mode="multiple"
        label={t("AVAILABLE_SUBAREAS")}
        value={selectedSubareas?.map((s) => s.id) || []}
        disabled={!data.property}
        options={subareaOptions}
        onChange={handleSubareaSelect}
      />
      <Button
        data-testid="new-subarea-button"
        onClick={handleNewSubarea}
        disabled={!data.property}
      >
        {t("NEW_SUBAREA")}
      </Button>
    </InterestAreaRow>
  );
};