
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { ReportType } from "@types";
import BaseRequestForm from "./BaseRequestForm";
import { useBaseRequestForm } from "@/hooks/use-base-request-form";

jest.mock("@/hooks/use-base-request-form");
jest.mock("react-i18next", () => ({
    useTranslation: () => ({ t: (key: string) => key }),
}));

jest.mock("@components", () => ({
    ContactForm: () => <div data-testid="mock-contact-form" />,
    FieldsDrawer: () => <div data-testid="mock-fields-drawer" />,
    NewPropertyDrawer: () => <div data-testid="mock-new-property-drawer" />,
}));

jest.mock("./components", () => ({
    ContactSection: ({ onContactChange, onOpenContactForm }: any) => (
        <div data-testid="mock-contact-section">
            <button onClick={() => onContactChange({ id: "1", name: "Test Contact" })}>
                Select Contact
            </button>
            <button onClick={onOpenContactForm}>New Contact</button>
        </div>
    ),
    PropertySection: ({ onPropertySelect, setIsPropertyDrawerOpen }: any) => (
        <div data-testid="mock-property-section">
            <button onClick={() => onPropertySelect("1")}>Select Property</button>
            <button onClick={() => setIsPropertyDrawerOpen(true)}>New Property</button>
        </div>
    ),
    SubareaSection: ({ onChange }: any) => (
        <div data-testid="mock-subarea-section">
            <button onClick={() => onChange(["1", "2"])}>Select Subareas</button>
        </div>
    ),
    MapSection: ({ setShowMap }: any) => (
        <div data-testid="mock-map-section">
            <button onClick={() => setShowMap(false)}>Toggle Map</button>
        </div>
    ),
    ObservationsSection: ({ onChange }: any) => (
        <div data-testid="mock-observations-section">
            <input
                data-testid="observation-input"
                onChange={(e) => onChange(e.target.value)}
            />
        </div>
    ),
}));

describe("BaseRequestForm", () => {
    const mockBaseRequestHook = {
        contacts: [],
        debouncedText: jest.fn(),
        isContactFormOpen: false,
        isFieldsDrawerOpen: false,
        isLoadingContacts: false,
        isPropertyDrawerOpen: false,
        onCreateNewArea: jest.fn(),
        propertySource: {
            clear: jest.fn(),
        },
        setIsContactFormOpen: jest.fn(),
        setIsDrawingSubareas: jest.fn(),
        setIsFieldsDrawerOpen: jest.fn(),
        setIsPropertyDrawerOpen: jest.fn(),
        setMap: jest.fn(),
        setShowMap: jest.fn(),
        showMap: true,
        subareaSource: {
            clear: jest.fn(),
        },
        fetchProperties: jest.fn(),
        fetchSubareas: jest.fn(),
    };

    const defaultProps = {
        data: {},
        reportType: ReportType.AGRO_CREDIT,
        properties: [],
        subareas: [],
        selectedProperty: null,
        selectedSubareas: [],
        hasSubareas: true,
        isSubmitted: false,
        onChange: jest.fn(),
        onPropertiesLoad: jest.fn(),
        onSubareasLoad: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (useBaseRequestForm as jest.Mock).mockReturnValue(mockBaseRequestHook);
    });

    it("renders all sections correctly", () => {
        render(<BaseRequestForm {...defaultProps} />);

        expect(screen.getByTestId("pb-t-base-request-form")).toBeInTheDocument();
        expect(screen.getByTestId("mock-contact-section")).toBeInTheDocument();
        expect(screen.getByTestId("mock-property-section")).toBeInTheDocument();
        expect(screen.getByTestId("mock-subarea-section")).toBeInTheDocument();
        expect(screen.getByTestId("mock-map-section")).toBeInTheDocument();
        expect(screen.getByTestId("mock-observations-section")).toBeInTheDocument();
    });

    it("handles contact selection correctly", async () => {
        render(<BaseRequestForm {...defaultProps} />);

        fireEvent.click(screen.getByText("Select Contact"));

        await waitFor(() => {
            expect(defaultProps.onChange).toHaveBeenCalledWith({
                contact: { id: "1", name: "Test Contact" },
                property: undefined,
                subareas: undefined,
            });
        });

        expect(mockBaseRequestHook.propertySource.clear).toHaveBeenCalled();
        expect(mockBaseRequestHook.subareaSource.clear).toHaveBeenCalled();
        expect(mockBaseRequestHook.fetchProperties).toHaveBeenCalled();
    });

    it("handles property selection correctly", async () => {
        const mockProperty = { id: "1", name: "Test Property" };
        render(
            <BaseRequestForm
                {...defaultProps}
                properties={[mockProperty]}
                data={{ contact: { id: "1", name: "Test Contact", value: "test-value", document_type: "cpf" } }} />
        );

        fireEvent.click(screen.getByText("Select Property"));

        await waitFor(() => {
            expect(defaultProps.onChange).toHaveBeenCalledWith({
                contact: { id: "1", name: "Test Contact", value: "test-value", document_type: "cpf" },
                property: mockProperty,
                subareas: [],
            });
        });

        expect(mockBaseRequestHook.subareaSource.clear).toHaveBeenCalled();
        expect(mockBaseRequestHook.fetchSubareas).toHaveBeenCalled();
    });

    it("handles subarea selection correctly", async () => {
        const mockSubareas = [
            { id: "1", name: "Subarea 1" },
            { id: "2", name: "Subarea 2" },
        ];
        render(
            <BaseRequestForm
                {...defaultProps}
                subareas={mockSubareas}
                data={{ contact: { id: "1", name: "Test Contact", value: "test-value", document_type: "cpf" } }} />
        );

        fireEvent.click(screen.getByText("Select Subareas"));

        await waitFor(() => {
            expect(defaultProps.onChange).toHaveBeenCalledWith({
                contact: { id: "1", name: "Test Contact", value: "test-value", document_type: "cpf" },
                subareas: mockSubareas,
            });
        });

        expect(mockBaseRequestHook.subareaSource.clear).toHaveBeenCalled();
    });

    it("handles observation changes correctly", () => {
        render(<BaseRequestForm {...defaultProps} />);

        const input = screen.getByTestId("observation-input");
        fireEvent.change(input, { target: { value: "Test observation" } });

        expect(defaultProps.onChange).toHaveBeenCalledWith({
            observation: "Test observation",
        });
    });

    it("handles contact form opening correctly", () => {
        render(<BaseRequestForm {...defaultProps} />);

        fireEvent.click(screen.getByText("New Contact"));

        expect(mockBaseRequestHook.setIsContactFormOpen).toHaveBeenCalledWith(true);
    });

    it("handles property drawer opening correctly", () => {
        render(<BaseRequestForm {...defaultProps} />);

        fireEvent.click(screen.getByText("New Property"));

        expect(mockBaseRequestHook.setIsPropertyDrawerOpen).toHaveBeenCalledWith(
            true
        );
    });

    it("renders without subareas when hasSubareas is false", () => {
        render(<BaseRequestForm {...defaultProps} hasSubareas={false} />);

        expect(screen.queryByTestId("mock-subarea-section")).not.toBeInTheDocument();
    });

    it("clears form data when contact is cleared", async () => {
        jest.mock("./components", () => ({
            ...jest.requireActual("./components"),
            ContactSection: ({ onContactChange, onOpenContactForm }: any) => (
                <div data-testid="mock-contact-section">
                    <button onClick={() => onContactChange(undefined)}>
                        Select Contact
                    </button>
                    <button onClick={onOpenContactForm}>New Contact</button>
                </div>
            ),
        }));

        render(
            <BaseRequestForm
                {...defaultProps}
                data={{ contact: { id: "1", name: "Test Contact", value: "test-value", document_type: "cpf" } }}
            />
        );

        const handleContactChange = jest.fn();
        const onPropertiesLoad = jest.fn();
        const onSubareasLoad = jest.fn();
        const propertySource = { clear: jest.fn() };
        const subareaSource = { clear: jest.fn() };

        handleContactChange(undefined);
        onPropertiesLoad([]);
        onSubareasLoad([]);
        propertySource.clear();
        subareaSource.clear();

        expect(onPropertiesLoad).toHaveBeenCalledWith([]);
        expect(onSubareasLoad).toHaveBeenCalledWith([]);
        expect(propertySource.clear).toHaveBeenCalled();
        expect(subareaSource.clear).toHaveBeenCalled();
    });
});

