import React, { useState } from "react";
import { Button, Input } from "brainui";
import { useTranslation } from "react-i18next";
import { notification } from "antd";
import { getByCode } from "@services";
import { 
  formatIncra, 
  geojsonToFeatures, 
  getExtent, 
  getIncraType, 
  isIncra, 
  toHectares 
} from "@utils";
import { v4 as uuidv4 } from "uuid";
import geojsonArea from "@mapbox/geojson-area";
import { IncraWrapper } from "../NewPropertyDrawer.styles";
import { Property } from "../../../../hooks/use-property-management";

interface IncraTabProps {
  addProperty: (property: Property) => Property[];
  source: any;
  map: any;
}

const IncraTab: React.FC<IncraTabProps> = ({ addProperty, source, map }) => {
  const { t } = useTranslation();
  const [incraCode, setIncraCode] = useState("");

  const searchIncra = async () => {
    const codeType = getIncraType(incraCode);
    try {
      const response = await getByCode(incraCode, codeType);
      if (!response.length) {
        notification.error({
          message: t("INCRA_CODE_NOT_FOUND"),
        });
        return;
      }
      
      const geometry = JSON.parse(response[0].geom);
      const area = toHectares(geojsonArea.geometry(geometry));
      const features = geojsonToFeatures(geometry);
      
      addProperty({
        uid: uuidv4(),
        area,
        geometry,
        features,
        car: "",
        origin: "MANUAL",
      });
      
      source.clear();
      source.addFeatures(features);
      
      if (map) {
        const extent = getExtent(features);
        map.getView().fit(extent, { padding: [25, 25, 25, 25] });
      }
    } catch (e: any) {
      if (e.code === "ERR_BAD_REQUEST") {
        notification.error({
          message: t("INCRA_CODE_NOT_FOUND"),
          description: t("VALUE_INVALID", { value: codeType }),
        });
      } else {
        notification.warning({
          message: t("QUERY_TEMPORARILY_UNAVAILABLE"),
        });
      }
    }
  };

  return (
    <IncraWrapper>
      <Input
        placeholder={t("INSERT_PROPERTY_INCRA_CODE")}
        autoComplete="off"
        value={incraCode}
        onChange={({ target }) => {
          setIncraCode(formatIncra(target.value));
        }}
      />
      <Button
        onClick={searchIncra}
        disabled={!isIncra(incraCode)}
      >
        {t("SEARCH")}
      </Button>
    </IncraWrapper>
  );
};

export default IncraTab;
