import React, { useState } from "react";
import { Button, Select } from "brainui";
import { useTranslation } from "react-i18next";
import { FaCompressArrowsAlt, FaPencilAlt } from "react-icons/fa";
import { MapInteraction } from "../../../../hooks/use-map-interactions";
import { ClickDrawOptions, ClickDrawWrapper } from "../NewPropertyDrawer.styles";
import cities from "../../../../assets/data/cities.json";

interface ClickDrawTabProps {
  interaction: MapInteraction;
  changeInteraction: (interaction: MapInteraction) => void;
  map: any;
}

const ClickDrawTab: React.FC<ClickDrawTabProps> = ({
  interaction,
  changeInteraction,
  map,
}) => {
  const { t } = useTranslation();
  const [selectedCities, setSelectedCities] = useState<any[]>([]);

  return (
    <ClickDrawWrapper>
      <ClickDrawOptions>
        <Button
          size="small"
          icon={<FaCompressArrowsAlt />}
          onClick={() => {
            changeInteraction(MapInteraction.CLICK);
          }}
          variant={
            interaction === MapInteraction.CLICK ? "standard" : "outlined"
          }
        >
          {t("CLICK")}
        </Button>
        <Button
          size="small"
          icon={<FaPencilAlt />}
          onClick={() => {
            changeInteraction(MapInteraction.DRAW);
          }}
          variant={
            interaction === MapInteraction.DRAW ? "standard" : "outlined"
          }
        >
          {t("Desenhe")}
        </Button>
      </ClickDrawOptions>
      <Select
        label={t("PROPERTY_CITY")}
        mode="multiple"
        allowClear
        placeholder={t("SEARCH_PROPERTY_CITY")}
        showSearch
        filterOption={(input, option) =>
          option.value
            .toLocaleLowerCase()
            .includes(input.toLocaleLowerCase())
        }
        onChange={(nameUf: string[]) => {
          if (!nameUf.length) return;
          const [name, uf] = nameUf[0].split("/");
          const city = cities.find(
            (c) => c.name === name && c.uf === uf
          );
          if (!city || !map) return;
          const view = map.getView();
          view.setCenter([city.longitude, city.latitude]);
          view.setZoom(13);
        }}
        onSelect={(nameUf: string) => {
          if (!nameUf.length) return;
          const [name, uf] = nameUf.split("/");
          const city = cities.find(
            (c) => c.name === name && c.uf === uf
          );
          if (!city) return;
          setSelectedCities([...selectedCities, city]);
        }}
        onDeselect={(nameUf: string) => {
          if (!nameUf.length) return;
          const [name, uf] = nameUf.split("/");
          const city = cities.find(
            (c) => c.name === name && c.uf === uf
          );
          if (!city) return;
          setSelectedCities(
            selectedCities.filter(
              (c) => c.ibge_code !== city.ibge_code
            )
          );
        }}
        onClear={() => {
          setSelectedCities([]);
        }}
      >
        {cities.map((city) => (
          <Select.Option
            key={city.ibge_code}
            value={`${city.name}/${city.uf}`}
            disabled={
              selectedCities.length === 5 &&
              !selectedCities.find(
                (c) => c.ibge_code === city.ibge_code
              )
            }
          >
            {city.name}/{city.uf}
          </Select.Option>
        ))}
      </Select>
    </ClickDrawWrapper>
  );
};

export default ClickDrawTab;
