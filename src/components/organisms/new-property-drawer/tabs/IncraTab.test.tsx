import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import IncraTab from "./IncraTab";
import { getByCode } from "@services";
import {
  formatIncra,
  geojsonToFeatures,
  getExtent,
  getIncraType,
  isIncra,
  toHectares
} from "@utils";
import { notification } from "antd";

jest.mock("@services", () => ({
  getByCode: jest.fn(),
}));

jest.mock("@utils", () => ({
  formatIncra: jest.fn((value) => value),
  geojsonToFeatures: jest.fn(),
  getExtent: jest.fn(() => [0, 0, 1, 1]),
  getIncraType: jest.fn(() => "INCRA"),
  isIncra: jest.fn(() => true),
  toHectares: jest.fn(() => 100),
}));

jest.mock("antd", () => ({
  notification: {
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        "INSERT_PROPERTY_INCRA_CODE": "Insira o código INCRA da propriedade",
        "SEARCH": "Buscar",
        "INCRA_CODE_NOT_FOUND": "Código INCRA não encontrado",
        "VALUE_INVALID": "Valor inválido: {{value}}",
        "QUERY_TEMPORARILY_UNAVAILABLE": "Consulta temporariamente indisponível",
      };
      return translations[key] || key;
    },
  }),
}));

jest.mock("brainui", () => ({
  Button: ({ children, onClick, disabled }) => (
    <button
      data-testid="search-button"
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  ),
  Input: ({ placeholder, autoComplete, value, onChange }) => (
    <input
      data-testid="incra-input"
      placeholder={placeholder}
      autoComplete={autoComplete}
      value={value}
      onChange={onChange}
    />
  ),
}));

describe("IncraTab", () => {
  const mockAddProperty = jest.fn();
  const mockSource = {
    clear: jest.fn(),
    addFeatures: jest.fn(),
  };
  const mockMap = {
    getView: jest.fn().mockReturnValue({
      fit: jest.fn(),
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component with all required elements", () => {
    render(
      <IncraTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    expect(screen.getByTestId("incra-input")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Insira o código INCRA da propriedade")).toBeInTheDocument();

    expect(screen.getByTestId("search-button")).toBeInTheDocument();
    expect(screen.getByText("Buscar")).toBeInTheDocument();
  });

  it("should format INCRA code when input changes", () => {
    render(
      <IncraTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const incraInput = screen.getByTestId("incra-input");
    fireEvent.change(incraInput, { target: { value: "123456789" } });

    expect(formatIncra).toHaveBeenCalledWith("123456789");
  });

  it("should call getByCode and add property when search button is clicked", async () => {
    const mockGeometry = {
      type: "Polygon",
      coordinates: [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]],
    };
    const mockFeatures = [{ id: "feature1" }, { id: "feature2" }];

    (getByCode as jest.Mock).mockResolvedValue([{
      geom: JSON.stringify(mockGeometry),
    }]);
    (geojsonToFeatures as jest.Mock).mockReturnValue(mockFeatures);

    render(
      <IncraTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const incraInput = screen.getByTestId("incra-input");
    fireEvent.change(incraInput, { target: { value: "123456789" } });

    const searchButton = screen.getByTestId("search-button");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(getIncraType).toHaveBeenCalledWith("123456789");
      expect(getByCode).toHaveBeenCalledWith("123456789", "INCRA");
      expect(geojsonToFeatures).toHaveBeenCalledWith(mockGeometry);
      expect(mockAddProperty).toHaveBeenCalledWith(expect.objectContaining({
        area: 100,
        geometry: mockGeometry,
        features: mockFeatures,
        car: "",
        origin: "MANUAL",
      }));
      expect(mockSource.clear).toHaveBeenCalled();
      expect(mockSource.addFeatures).toHaveBeenCalledWith(mockFeatures);
      expect(mockMap.getView().fit).toHaveBeenCalledWith([0, 0, 1, 1], { padding: [25, 25, 25, 25] });
    });
  });

  it("should show error notification when INCRA code is not found", async () => {
    (getByCode as jest.Mock).mockResolvedValue([]);

    render(
      <IncraTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const incraInput = screen.getByTestId("incra-input");
    fireEvent.change(incraInput, { target: { value: "123456789" } });

    const searchButton = screen.getByTestId("search-button");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith({
        message: "Código INCRA não encontrado",
      });
      expect(mockAddProperty).not.toHaveBeenCalled();
    });
  });

  it("should handle bad request error", async () => {
    const error = new Error("Bad request");
    (error as any).code = "ERR_BAD_REQUEST";
    (getByCode as jest.Mock).mockRejectedValue(error);

    render(
      <IncraTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const incraInput = screen.getByTestId("incra-input");
    fireEvent.change(incraInput, { target: { value: "123456789" } });

    const searchButton = screen.getByTestId("search-button");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith(expect.objectContaining({
        message: "Código INCRA não encontrado",
      }));
      expect(mockAddProperty).not.toHaveBeenCalled();
    });
  });

  it("should handle other errors", async () => {
    (getByCode as jest.Mock).mockRejectedValue(new Error("Unknown error"));

    render(
      <IncraTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const incraInput = screen.getByTestId("incra-input");
    fireEvent.change(incraInput, { target: { value: "123456789" } });

    const searchButton = screen.getByTestId("search-button");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(notification.warning).toHaveBeenCalledWith({
        message: "Consulta temporariamente indisponível",
      });
      expect(mockAddProperty).not.toHaveBeenCalled();
    });
  });
});
