import { render, screen, fireEvent } from "@testing-library/react";
import { MapInteraction } from "../../../../hooks/use-map-interactions";

jest.mock("../../../../hooks/use-map-interactions", () => ({
  MapInteraction: {
    CLICK: "CL<PERSON><PERSON>",
    DRAW: "DRAW"
  }
}));

jest.mock("brainui", () => {
  const SelectMock = ({
    children,
    onChange,
    onSelect,
    onDeselect,
    onClear,
    ...props
  }) => (
    <div data-testid="select-city">
      <label>{props.label}</label>
      <select
        data-testid="select-input"
        onChange={(e) => onChange && onChange([e.target.value])}
      >
        {children}
      </select>
      <button onClick={() => onSelect && onSelect("Test City/SP")} data-testid="select-button">
        Select
      </button>
      <button onClick={() => onDeselect && onDeselect("Test City/SP")} data-testid="deselect-button">
        Deselect
      </button>
      <button onClick={() => onClear && onClear()} data-testid="clear-button">
        Clear
      </button>
    </div>
  );

  SelectMock.Option = ({ children, value, disabled }) => (
    <option value={value} disabled={disabled} data-testid={`option-${value}`}>
      {children}
    </option>
  );

  return {
    Button: ({ children, onClick, variant, ...props }) => (
      <button
        onClick={onClick}
        data-testid={props.icon ? "button-with-icon" : "button"}
        data-variant={variant}
      >
        {children}
      </button>
    ),
    Select: SelectMock,
  };
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("../../../../assets/data/cities.json", () => [
  { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 },
  { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 },
]);

jest.mock("../NewPropertyDrawer.styles", () => ({
  ClickDrawWrapper: ({ children }) => <div data-testid="click-draw-wrapper">{children}</div>,
  ClickDrawOptions: ({ children }) => <div data-testid="click-draw-options">{children}</div>,
}));

import ClickDrawTab from "./ClickDrawTab";

describe("ClickDrawTab", () => {
  const mockChangeInteraction = jest.fn();
  const mockMap = {
    getView: jest.fn().mockReturnValue({
      setCenter: jest.fn(),
      setZoom: jest.fn(),
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component with all required elements", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    expect(screen.getAllByTestId("button-with-icon")).toHaveLength(2);

    expect(screen.getByTestId("select-city")).toBeInTheDocument();
    expect(screen.getByText("PROPERTY_CITY")).toBeInTheDocument();

    expect(screen.getByTestId("option-Test City/SP")).toBeInTheDocument();
    expect(screen.getByTestId("option-Another City/RJ")).toBeInTheDocument();
  });

  it("should call changeInteraction when click button is clicked", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.DRAW}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    const buttons = screen.getAllByTestId("button-with-icon");
    const clickButton = buttons[0]; // First button is the CLICK button

    fireEvent.click(clickButton);

    expect(mockChangeInteraction).toHaveBeenCalledWith(MapInteraction.CLICK);
  });

  it("should call changeInteraction when draw button is clicked", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    const buttons = screen.getAllByTestId("button-with-icon");
    const drawButton = buttons[1]; // Second button is the DRAW button

    fireEvent.click(drawButton);

    expect(mockChangeInteraction).toHaveBeenCalledWith(MapInteraction.DRAW);
  });

  it("should set city center and zoom when a city is selected", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "Test City/SP" } });

    expect(mockMap.getView).toHaveBeenCalled();
    expect(mockMap.getView().setCenter).toHaveBeenCalledWith([-46.6333, -23.5505]);
    expect(mockMap.getView().setZoom).toHaveBeenCalledWith(13);
  });

  it("should update selectedCities when a city is selected", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    const selectButton = screen.getByTestId("select-button");
    fireEvent.click(selectButton);

    expect(selectButton).toBeInTheDocument();
  });

  it("should update selectedCities when a city is deselected", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    const deselectButton = screen.getByTestId("deselect-button");
    fireEvent.click(deselectButton);

    expect(deselectButton).toBeInTheDocument();
  });

  it("should clear selectedCities when clear button is clicked", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
      />
    );

    const clearButton = screen.getByTestId("clear-button");
    fireEvent.click(clearButton);

    expect(clearButton).toBeInTheDocument();
  });
});
