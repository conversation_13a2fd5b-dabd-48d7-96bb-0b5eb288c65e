import React, { useState } from "react";
import { InputCar } from "@components";
import { useTranslation } from "react-i18next";
import { getByCar } from "@services";
import { geojsonToFeatures, getExtent, toHectares } from "@utils";
import { v4 as uuidv4 } from "uuid";
import geojsonArea from "@mapbox/geojson-area";
import { Property } from "../../../../hooks/use-property-management";

interface CarTabProps {
  addProperty: (property: Property) => Property[];
  source: any;
  map: any;
}

const CarTab: React.FC<CarTabProps> = ({ addProperty, source, map }) => {
  const { t } = useTranslation();
  const [car, setCar] = useState("");

  const handleCarChange = async (code: string) => {
    setCar(code);
    if (!code) return;
    
    try {
      const carData = await getByCar(code);
      const geometry = JSON.parse(carData.geometry);
      const area = toHectares(geojsonArea.geometry(geometry));
      const features = geojsonToFeatures(geometry);
      
      addProperty({
        uid: uuidv4(),
        area,
        geometry,
        features,
        car: code,
        origin: "CAR",
      });
      
      source.clear();
      source.addFeatures(features);
      
      if (map) {
        const extent = getExtent(features);
        map.getView().fit(extent, { padding: [25, 25, 25, 25] });
      }
    } catch (error) {
    }
  };

  return (
    <InputCar
      placeholder={t("INSERT_PROPERTY_CAR")}
      value={car}
      onChange={handleCarChange}
    />
  );
};

export default CarTab;
