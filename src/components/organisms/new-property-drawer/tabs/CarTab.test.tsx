import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import CarTab from "./CarTab";
import { getByCar } from "@services";
import { geojsonToFeatures, getExtent, toHectares } from "@utils";

jest.mock("@services", () => ({
  getByCar: jest.fn(),
}));

jest.mock("@utils", () => ({
  geojsonToFeatures: jest.fn(),
  getExtent: jest.fn(() => [0, 0, 1, 1]),
  toHectares: jest.fn(() => 100),
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        "INSERT_PROPERTY_CAR": "Insira o CAR da propriedade",
      };
      return translations[key] || key;
    },
  }),
}));

jest.mock("@components", () => ({
  InputCar: ({ placeholder, value, onChange }) => (
    <div data-testid="input-car">
      <input
        data-testid="car-input"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange && onChange(e.target.value)}
      />
    </div>
  ),
}));

describe("CarTab", () => {
  const mockAddProperty = jest.fn();
  const mockSource = {
    clear: jest.fn(),
    addFeatures: jest.fn(),
  };
  const mockMap = {
    getView: jest.fn().mockReturnValue({
      fit: jest.fn(),
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component with all required elements", () => {
    render(
      <CarTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    expect(screen.getByTestId("input-car")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Insira o CAR da propriedade")).toBeInTheDocument();
  });

  it("should call getByCar and add property when CAR code is entered", async () => {
    const mockGeometry = {
      type: "Polygon",
      coordinates: [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]],
    };
    const mockFeatures = [{ id: "feature1" }, { id: "feature2" }];
    
    (getByCar as jest.Mock).mockResolvedValue({
      geometry: JSON.stringify(mockGeometry),
    });
    (geojsonToFeatures as jest.Mock).mockReturnValue(mockFeatures);

    render(
      <CarTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const carInput = screen.getByTestId("car-input");
    fireEvent.change(carInput, { target: { value: "BR-1234567890" } });

    await waitFor(() => {
      expect(getByCar).toHaveBeenCalledWith("BR-1234567890");
      expect(geojsonToFeatures).toHaveBeenCalledWith(mockGeometry);
      expect(mockAddProperty).toHaveBeenCalledWith(expect.objectContaining({
        area: 100,
        geometry: mockGeometry,
        features: mockFeatures,
        car: "BR-1234567890",
        origin: "CAR",
      }));
      expect(mockSource.clear).toHaveBeenCalled();
      expect(mockSource.addFeatures).toHaveBeenCalledWith(mockFeatures);
      expect(mockMap.getView().fit).toHaveBeenCalledWith([0, 0, 1, 1], { padding: [25, 25, 25, 25] });
    });
  });

  it("should handle error when getByCar fails", async () => {
    (getByCar as jest.Mock).mockRejectedValue(new Error("Failed to get CAR"));

    render(
      <CarTab
        addProperty={mockAddProperty}
        source={mockSource}
        map={mockMap}
      />
    );

    const carInput = screen.getByTestId("car-input");
    fireEvent.change(carInput, { target: { value: "invalid-car" } });

    await waitFor(() => {
      expect(getByCar).toHaveBeenCalledWith("invalid-car");
      expect(mockAddProperty).not.toHaveBeenCalled();
    });
  });
});
