import React from "react";
import { <PERSON><PERSON> } from "brainui";
import { InfoCircleOutlined } from "@ant-design/icons";
import { GeometryCard } from "@components";
import { formatNumber, getExtent } from "@utils";
import { GeometryWrapper, InfoText } from "../NewPropertyDrawer.styles";
import { useTranslation } from "react-i18next";
import { Property } from "../../../../hooks/use-property-management";

interface FileUploadTabProps {
  uploadProps: any;
  propertyOptions: Property[];
  properties: Property[];
  setProperties: (properties: Property[]) => void;
  setPropertyOptions: (propertyOptions: Property[]) => void;
  updatePropertiesOnMap: (properties: Property[]) => void;
  map: any;
}

const FileUploadTab: React.FC<FileUploadTabProps> = ({
  uploadProps,
  propertyOptions,
  properties,
  setProperties,
  setPropertyOptions,
  updatePropertiesOnMap,
  map,
}) => {
  const { t } = useTranslation();

  return (
    <div>
      <Dragger
        data-testid="dragger"
        uploadProps={uploadProps}
        fileList={[]}
        onChange={() => {}}
        onDelete={() => {}}
      />
      <GeometryWrapper>
        {propertyOptions.map((property) => (
          <GeometryCard
            key={property.uid}
            uid={property.uid}
            name={`Área de ${formatNumber(property.area, 2)} ha`}
            selected={Boolean(
              properties.find((p) => p.uid === property.uid)
            )}
            geometry={property.geometry}
            onPointerEnter={() => {
              property.features.forEach((feature) =>
                feature.set("highlight", true)
              );
            }}
            onPointerleave={() => {
              property.features.forEach((feature) =>
                feature.set("highlight", false)
              );
            }}
            onClick={() => {
              let updatedProperties = [property];
              propertyOptions.forEach((p) => {
                p.features.forEach((feature) =>
                  feature.set("selected", p.uid === property.uid)
                );
              });
              setProperties(updatedProperties);
            }}
            onRemove={() => {
              const updatedPropertyOptions =
                propertyOptions.filter(
                  (p) => p.uid !== property.uid
                );
              setProperties(
                properties.filter((p) => p.uid !== property.uid)
              );
              setPropertyOptions(updatedPropertyOptions);
              updatePropertiesOnMap(updatedPropertyOptions);
            }}
            onFocus={() => {
              if (!map) return;
              const extent = getExtent(property.features);
              map
                .getView()
                .fit(extent, { padding: [25, 25, 25, 25] });
            }}
          />
        ))}
      </GeometryWrapper>
      {propertyOptions.length > 1 && (
        <InfoText>
          <InfoCircleOutlined size={15} />
          {t("INFO_PROPERTY_ICON_CLICK")}
        </InfoText>
      )}
    </div>
  );
};

export default FileUploadTab;
