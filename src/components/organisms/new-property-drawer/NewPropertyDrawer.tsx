import React, { useState } from "react";
import { <PERSON><PERSON> as <PERSON>t<PERSON><PERSON><PERSON><PERSON>, Ta<PERSON> } from "antd";
import { <PERSON><PERSON>, Drawer } from "brainui";
import { useTranslation } from "react-i18next";
import VectorSource from "ol/source/Vector";
import VectorLayer from "ol/layer/Vector";
import { MapWrapper } from "@components";
import { PROPERTY_STYLE } from "@constants";
import { Content, FooterOptions, LabelInput, Wrapper } from "./NewPropertyDrawer.styles";
import { usePropertyManagement } from "../../../hooks/use-property-management";
import { MapInteraction, useMapInteractions } from "../../../hooks/use-map-interactions";
import { useFileProcessing } from "../../../hooks/use-file-processing";
import ClickDrawTab from "./tabs/ClickDrawTab";
import FileUploadTab from "./tabs/FileUploadTab";
import CarTab from "./tabs/CarTab";
import IncraTab from "./tabs/IncraTab";
import { LatLongTab } from "@components";

enum PropertyTabs {
  CLICK_DRAW = 1,
  FILE_UPLOAD = 2,
  CAR = 3,
  INCRA = 4,
  LAT_LON = 5,
}

interface NewPropertyDrawerProps {
  contact?: {
    id: number;
    group_id: number;
  };
  open: boolean;
  onClose: () => void;
  onSuccess: (newProperty: any) => void;
}

function NewPropertyDrawer({
  contact,
  open,
  onClose,
  onSuccess,
}: NewPropertyDrawerProps) {
  const { t } = useTranslation();
  const [source] = useState(new VectorSource());
  const [currentTab, setCurrentTab] = useState<PropertyTabs>(PropertyTabs.CLICK_DRAW);

  const {
    properties,
    propertyOptions: propOptions,
    propertyName,
    isSubmitted,
    isSaving,
    setProperties,
    setPropertyOptions: setPropOptions,
    setPropertyName,
    addProperty,
    saveProperty,
    handleClose: closePropertyManagement,
  } = usePropertyManagement({
    contact,
    onSuccess,
    onClose,
  });

  const {
    map,
    interaction,
    setMap,
    handleMapClick,
    changeInteraction,
    updatePropertiesOnMap,
    handleGetPropertyByCoordinates,
  } = useMapInteractions({
    addProperty,
    source,
  });

  const {
    propertyOptions: filePropertyOptions,
    setPropertyOptions,
    uploadProps,
  } = useFileProcessing({
    properties,
    setProperties,
    updatePropertiesOnMap,
  });

  const propertyOptions = [...propOptions, ...filePropertyOptions];

  const handleClose = () => {
    closePropertyManagement();
    if (map) {
      source.clear();
      map.getView().setZoom(13);
    }
    onClose();
  };

  return (
    <Drawer
      data-testid="pb-t-new-property"
      title={t("REGISTER_NEW_PROPERTY")}
      open={open}
      closable={false}
      width={650}
      onClose={handleClose}
    >
      <Wrapper>
        <Content>
          <LabelInput
            data-testid="input-property-name"
            label={t("PROPERTY_NAME")}
            value={propertyName}
            status={isSubmitted && !propertyName.length ? "error" : ""}
            disabled={isSaving}
            onChange={({ target }) => {
              setPropertyName(target.value);
            }}
            size="large"
          />
          <Tabs
            defaultActiveKey={"1"}
            activeKey={String(currentTab)}
            onChange={(activeKey: string) => {
              setCurrentTab(Number(activeKey));

              if (activeKey === "2") {
                setProperties([]);

                propertyOptions.forEach((p) => {
                  p.features.forEach((feature) =>
                    feature.set("selected", false)
                  );
                });
              }
            }}
            items={[
              {
                key: "1",
                label: t("CLICK_OR_DRAW"),
                children: (
                  <ClickDrawTab
                    interaction={interaction}
                    changeInteraction={changeInteraction}
                    map={map}
                  />
                ),
              },
              {
                key: "2",
                label: t("KML_OR_SHP"),
                children: (
                  <FileUploadTab
                    uploadProps={uploadProps}
                    propertyOptions={propertyOptions}
                    properties={properties}
                    setProperties={setProperties}
                    setPropertyOptions={setPropertyOptions}
                    updatePropertiesOnMap={updatePropertiesOnMap}
                    map={map}
                  />
                ),
              },
              {
                label: "CAR",
                key: "3",
                children: (
                  <CarTab
                    addProperty={addProperty}
                    source={source}
                    map={map}
                  />
                ),
              },
              {
                label: "INCRA",
                key: "4",
                children: (
                  <IncraTab
                    addProperty={addProperty}
                    source={source}
                    map={map}
                  />
                ),
              },
              {
                label: "Lat & Long",
                key: "5",
                children: (
                  <LatLongTab
                    onGetPropertyByCoordinates={handleGetPropertyByCoordinates}
                  />
                ),
              },
            ]}
          />
          <MapWrapper
            size="large"
            onClick={handleMapClick.bind(this)}
            onMapReady={(mapInstance) => {
              setMap(mapInstance);
              const layer = new VectorLayer({
                source,
                style: (feature) => PROPERTY_STYLE(feature),
              });
              mapInstance.addLayer(layer);
            }}
          />
        </Content>
        <FooterOptions>
          <AntdButton
            type="ghost"
            onClick={handleClose}
            disabled={isSaving}
          >
            {t("CANCEL")}
          </AntdButton>
          <Button
            onClick={saveProperty}
            disabled={isSaving || !properties.length}
          >
            {t("REGISTER")}
          </Button>
        </FooterOptions>
      </Wrapper>
    </Drawer>
  );
}

export default NewPropertyDrawer;
