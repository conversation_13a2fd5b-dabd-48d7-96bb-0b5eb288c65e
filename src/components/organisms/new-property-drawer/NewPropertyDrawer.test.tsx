import { cleanup, render, screen, fireEvent, waitFor } from "@testing-library/react";
import NewPropertyDrawer from "./NewPropertyDrawer";

jest.mock("../../../hooks/use-property-management", () => ({
  usePropertyManagement: jest.fn(() => ({
    properties: [],
    propertyOptions: [],
    propertyName: "",
    isSubmitted: false,
    isSaving: false,
    setProperties: jest.fn(),
    setPropertyOptions: jest.fn(),
    setPropertyName: jest.fn(),
    addProperty: jest.fn(),
    saveProperty: jest.fn(),
    handleClose: jest.fn(),
  })),
}));

jest.mock("../../../hooks/use-map-interactions", () => ({
  useMapInteractions: jest.fn(() => ({
    map: null,
    interaction: "CLICK",
    setMap: jest.fn(),
    handleMapClick: jest.fn(),
    changeInteraction: jest.fn(),
    updatePropertiesOnMap: jest.fn(),
    handleGetPropertyByCoordinates: jest.fn(),
  })),
  MapInteraction: {
    CLICK: "CLICK",
    DRAW: "DRAW",
  },
}));

jest.mock("../../../hooks/use-file-processing", () => ({
  useFileProcessing: jest.fn(() => ({
    propertyOptions: [],
    setPropertyOptions: jest.fn(),
    uploadProps: {
      name: "file",
      multiple: true,
      showUploadList: false,
      accept: ".kml,.shp",
      beforeUpload: jest.fn(),
    },
  })),
}));

jest.mock("./tabs/ClickDrawTab", () => ({
  __esModule: true,
  default: () => <div data-testid="click-draw-tab">Click Draw Tab</div>,
}));

jest.mock("./tabs/FileUploadTab", () => ({
  __esModule: true,
  default: () => <div data-testid="file-upload-tab">File Upload Tab</div>,
}));

jest.mock("./tabs/CarTab", () => ({
  __esModule: true,
  default: () => <div data-testid="car-tab">CAR Tab</div>,
}));

jest.mock("./tabs/IncraTab", () => ({
  __esModule: true,
  default: () => <div data-testid="incra-tab">INCRA Tab</div>,
}));

jest.mock("@components", () => ({
  MapWrapper: ({ onMapReady, onClick, children }) => (
    <div data-testid="pb-t-map-wrapper" onClick={onClick}>
      {children}
    </div>
  ),
  LatLongTab: () => <div data-testid="lat-long-tab">Lat & Long Tab</div>,
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations = {
        "REGISTER_NEW_PROPERTY": "Cadastrar nova propriedade",
        "PROPERTY_NAME": "Nome da propriedade",
        "CLICK_OR_DRAW": "Clique ou desenhe",
        "KML_OR_SHP": "KML ou SHP",
        "CANCEL": "Cancelar",
        "REGISTER": "Cadastrar",
      };
      return translations[key] || key;
    },
  }),
}));

beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  cleanup();
});

const defaultProps = {
  contact: {
    id: 1,
    group_id: 1,
  },
  open: true,
  onClose: jest.fn(),
  onSuccess: jest.fn(),
};

describe("NewPropertyDrawer component (refactored)", () => {
  it("should render the component with all required elements", () => {
    render(<NewPropertyDrawer {...defaultProps} />);

    const component = screen.queryByTestId("pb-t-new-property");
    expect(component).toBeInTheDocument();

    const nameInput = screen.getByTestId("input-property-name");
    expect(nameInput).toBeInTheDocument();

    expect(screen.getByText("Clique ou desenhe")).toBeInTheDocument();
    expect(screen.getByText("KML ou SHP")).toBeInTheDocument();
    expect(screen.getByText("CAR")).toBeInTheDocument();
    expect(screen.getByText("INCRA")).toBeInTheDocument();
    expect(screen.getByText("Lat & Long")).toBeInTheDocument();

    const mapWrapper = screen.getByTestId("pb-t-map-wrapper");
    expect(mapWrapper).toBeInTheDocument();
  });

  it("should call onClose when cancel button is clicked", () => {
    const { usePropertyManagement } = require("../../../hooks/use-property-management");
    const mockHandleClose = jest.fn();
    usePropertyManagement.mockReturnValue({
      properties: [],
      propertyOptions: [],
      propertyName: "",
      isSubmitted: false,
      isSaving: false,
      setProperties: jest.fn(),
      setPropertyOptions: jest.fn(),
      setPropertyName: jest.fn(),
      addProperty: jest.fn(),
      saveProperty: jest.fn(),
      handleClose: mockHandleClose,
    });

    render(<NewPropertyDrawer {...defaultProps} />);

    const cancelButton = screen.getByText("Cancelar");
    fireEvent.click(cancelButton);

    expect(mockHandleClose).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it("should handle tab navigation correctly", async () => {
    render(<NewPropertyDrawer {...defaultProps} />);

    const fileUploadTab = screen.getByText("KML ou SHP");
    fireEvent.click(fileUploadTab);
    
    await waitFor(() => {
      expect(screen.getByTestId("file-upload-tab")).toBeInTheDocument();
    });

    const carTab = screen.getByText("CAR");
    fireEvent.click(carTab);
    
    await waitFor(() => {
      expect(screen.getByTestId("car-tab")).toBeInTheDocument();
    });

    const incraTab = screen.getByText("INCRA");
    fireEvent.click(incraTab);
    
    await waitFor(() => {
      expect(screen.getByTestId("incra-tab")).toBeInTheDocument();
    });

    const latLongTab = screen.getByText("Lat & Long");
    fireEvent.click(latLongTab);
    
    await waitFor(() => {
      expect(screen.getByTestId("lat-long-tab")).toBeInTheDocument();
    });

    const clickDrawTab = screen.getByText("Clique ou desenhe");
    fireEvent.click(clickDrawTab);
    
    await waitFor(() => {
      expect(screen.getByTestId("click-draw-tab")).toBeInTheDocument();
    });
  });

  it("should call saveProperty when register button is clicked", () => {
    const { usePropertyManagement } = require("../../../hooks/use-property-management");
    const mockSaveProperty = jest.fn();
    usePropertyManagement.mockReturnValue({
      properties: [{ uid: "test" }], // Adicionar uma propriedade para habilitar o botão
      propertyOptions: [],
      propertyName: "Test Property",
      isSubmitted: false,
      isSaving: false,
      setProperties: jest.fn(),
      setPropertyOptions: jest.fn(),
      setPropertyName: jest.fn(),
      addProperty: jest.fn(),
      saveProperty: mockSaveProperty,
      handleClose: jest.fn(),
    });

    render(<NewPropertyDrawer {...defaultProps} />);

    const registerButton = screen.getByText("Cadastrar");
    fireEvent.click(registerButton);

    expect(mockSaveProperty).toHaveBeenCalled();
  });
});
