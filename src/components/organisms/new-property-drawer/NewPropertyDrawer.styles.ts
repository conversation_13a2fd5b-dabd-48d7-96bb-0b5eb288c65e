import { Input } from "brainui";
import styled from "styled-components";

export const LabelInput = styled(Input)`
  input {
    padding: 20px 12px 4px 11px !important;
  }
`;

export const FooterOptions = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 10px;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 10px;
  overflow: auto;
`;

export const ClickDrawWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

export const ClickDrawOptions = styled.div`
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;

  button {
    min-width: 174px;
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
  }
`;

export const InfoText = styled.small`
  display: flex;
  align-items: center;

  svg {
    margin-right: 3px;
  }
`;

export const IncraWrapper = styled.div`
  width: 100%;
  display: flex;
  gap: 10px;

  div {
    width: 100%;
  }
`;

export const PopConfirmTitle = styled.div`
  display: flex;
  gap: 15px;
  max-width: 455px;
  padding-bottom: 5px;

  svg {
    height: 30px;
    width: 30px;
  }
`;

export const GeometryWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
`;
