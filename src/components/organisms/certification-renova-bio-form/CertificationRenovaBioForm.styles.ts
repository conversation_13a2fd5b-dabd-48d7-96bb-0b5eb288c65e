import styled from "styled-components";

export const HighlightedText = styled.span`
  color: #10a488;
`;

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

export const DraggerWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
`;

export const DraggerText = styled.p`
  max-width: 26ch;
  text-align: center;
  margin: 0 auto !important;
  line-height: 1.25;
`;

export const DraggerHint = styled.p`
  max-width: 50ch;
  text-align: center;
  margin: 0 auto !important;
  line-height: 1.25;
`;

export const Options = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const InlineTableButton = styled.button`
  cursor: pointer;
  background: none;
  border: none;
`;
