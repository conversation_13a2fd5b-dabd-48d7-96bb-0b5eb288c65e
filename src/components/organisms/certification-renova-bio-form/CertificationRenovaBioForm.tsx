import { Wrapper } from "./CertificationRenovaBioForm.styles";
import { useTranslation } from "react-i18next";
import { Button } from "antd";
import { Options } from "./CertificationRenovaBioForm.styles";
import { useCertificationRenovaBioForm } from "@/hooks/use-certification-renova-bio-form";
import { CertificationRenovaBioFormStepTwo } from "../certification-renova-bio-step-two/CertificationRenovaBioStepTwo";
import { CertificationRenovaBioFormStepOne } from "../certification-renova-bio-step-one/CertificationRenovaBioStepOne";
import { SelectTag } from "@/components/molecules";

export function CertificationRenovaBioForm() {
  const { t } = useTranslation();
  const {
    formStep,
    handleClickButtonNext,
    handleClickButtonCancel,
    handleSubmit,
    documentsData,
    isLoading,
    tagsList,
    selectedTags,
    setSelectedTags
  } = useCertificationRenovaBioForm();

  return (
    <Wrapper>
      {formStep == 0 && <CertificationRenovaBioFormStepOne />}
      {formStep == 1 && <CertificationRenovaBioFormStepTwo />}
      <SelectTag
        tags={tagsList}
        placeholder={t("TAGS_PLACEHOLDER")}
        value={selectedTags}
        onChange={(tags: string[]) => {
          setSelectedTags(tags);
        }}
        optionLimit={selectedTags.length == 3}
      />
      <br />

      <Options>
        <Button data-testid="button-cancel" onClick={handleClickButtonCancel}>
          {t("CANCEL")}
        </Button>
        {formStep === 0 ? (
          <Button
            type="primary"
            data-testid="button-next"
            disabled={Boolean(!documentsData.fileMetadata.name)}
            onClick={handleClickButtonNext}
          >
            {t("NEXT")}
          </Button>
        ) : (
          <Button
            type="primary"
            id="button-submit"
            disabled={isLoading}
            onClick={handleSubmit}
          >
            {t("FINISH")}
          </Button>
        )}
      </Options>
    </Wrapper>
  );
}
