import React from "react";
import { render, fireEvent } from "@testing-library/react";
import { CertificationRenovaBioForm } from "./CertificationRenovaBioForm";
import { useCertificationRenovaBioForm } from "../../../hooks/use-certification-renova-bio-form";

jest.mock("react-i18next", () => {
  return {
    useTranslation: () => {
      return {
        t: (data: string) => data,
      };
    },
  };
});

jest.mock("@/hooks/use-certification-renova-bio-form", () => {
  return {
    useCertificationRenovaBioForm: jest.fn(),
  };
});

describe("CertificationRenovaBioForm", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("calls handleClickButtonCancel when cancel button is clicked", async () => {
    const handleCancel = jest.fn();

    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      formStep: 0,
      reportType: "RENOVABIO_CERTIFICATION",
      handleSubmit: jest.fn(),
      documentsData: {
        fileMetadata: {
          name: "filename",
        },
      },
      handleRemoveFile: jest.fn(),
      handleSetFormStep: jest.fn(),
      handleUploadFile: jest.fn(),
      handleSetReportType: jest.fn(),
      handleClickButtonNext: jest.fn(),
      handleClickButtonCancel: handleCancel,
      isInvalidDocumentsExpanded: false,
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      handleDownloadInvalidDocumentsXLS: jest.fn(),
      tagsList: [],
      selectedTags: [],
      setSelectedTags: jest.fn(),
      
    });

    const { getByTestId } = render(<CertificationRenovaBioForm />);

    fireEvent.click(getByTestId("button-cancel"));

    expect(handleCancel).toHaveBeenCalled();
  });

  it("calls handleClickButtonNext when next button is clicked", () => {
    const handleNext = jest.fn();

    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      formStep: 0,
      reportType: "RENOVABIO_CERTIFICATION",
      handleSubmit: jest.fn(),
      documentsData: {
        fileMetadata: {
          name: "filename",
        },
      },
      handleRemoveFile: jest.fn(),
      handleSetFormStep: jest.fn(),
      handleUploadFile: jest.fn(),
      handleSetReportType: jest.fn(),
      handleClickButtonNext: handleNext,
      handleClickButtonCancel: jest.fn(),
      isInvalidDocumentsExpanded: false,
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      handleDownloadInvalidDocumentsXLS: jest.fn(),
      tagsList: [],
      selectedTags: [],
      setSelectedTags: jest.fn(),
    });

    const { getByTestId } = render(<CertificationRenovaBioForm />);
    fireEvent.click(getByTestId("button-next"));
    expect(handleNext).toHaveBeenCalled();
  });

  it("renders step two when formStep is 1", () => {
    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      formStep: 1,
      reportType: "RENOVABIO_CERTIFICATION",
      handleSubmit: jest.fn(),
      documentsData: {
        carCodes: [],
        invalidRows: [],
        fileMetadata: {
          name: "filename",
        },
      },
      handleRemoveFile: jest.fn(),
      handleSetFormStep: jest.fn(),
      handleUploadFile: jest.fn(),
      handleSetReportType: jest.fn(),
      handleClickButtonNext: jest.fn(),
      handleClickButtonCancel: jest.fn(),
      isInvalidDocumentsExpanded: false,
      handleToggleInvalidDocumentsExpanded: jest.fn(),
      handleDownloadInvalidDocumentsXLS: jest.fn(),
      tagsList: [],
      selectedTags: [],
      setSelectedTags: jest.fn(),
    });

    const { getByTestId } = render(<CertificationRenovaBioForm />);
    expect(
      getByTestId("certification-renova-bio-step-two")
    ).toBeInTheDocument();
  });
});
