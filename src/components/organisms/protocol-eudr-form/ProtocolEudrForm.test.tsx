import { fireEvent, render, screen } from "@testing-library/react";
import ProtocolEudrForm from "./ProtocolEudrForm";
import { useTranslation } from "react-i18next";
import { Commodities } from "@utils";

jest.mock("react-i18next", () => {
  return {
    useTranslation: jest.fn(() => ({
      t: (text) => text,
    })),
  };
});

describe("ProtocolEudrForm component", () => {
  it("should render the component", () => {
    render(
      <ProtocolEudrForm isSubmitted={false} data={{}} onChange={() => {}} />
    );
    const component = screen.queryByTestId("pb-t-protocol-eudr");
    expect(component).toBeInTheDocument();
  });

  it("should call onChange when select is updated", () => {
    const onChangeMock = jest.fn();
    const { getByTestId } = render(
      <ProtocolEudrForm data={{}} isSubmitted={false} onChange={onChangeMock} />
    );

    const select = getByTestId("select-commodity").querySelector("input");

    const commoditie = Object.values(Commodities)[0];

    fireEvent.change(select, { target: { value: commoditie } });

    const option = getByTestId(`option-commodity-${commoditie}`);

    fireEvent.click(option);
    expect(option).toBeInTheDocument();
    expect(onChangeMock).toHaveBeenCalled();
  });
  it("should call onChange when select language is updated", () => {
    const onChangeMock = jest.fn();
    const { getByTestId } = render(
      <ProtocolEudrForm data={{}} isSubmitted={false} onChange={onChangeMock} />
    );

    const select = getByTestId("select-languange").querySelector("input");

    fireEvent.change(select, { target: { value: "en" } });

    const option = getByTestId(`option-language-en`);

    fireEvent.click(option);
    expect(option).toBeInTheDocument();
    expect(onChangeMock).toHaveBeenCalled();
  });
  it("should render select status according state", () => {
    const onChangeMock = jest.fn();
    const { getByTestId, container } = render(
      <ProtocolEudrForm
        data={{
          data: { commodity: undefined },
        }}
        isSubmitted={true}
        onChange={onChangeMock}
      />
    );

    const select = getByTestId("select-commodity").querySelector("input");

    const commoditie = Object.values(Commodities)[0];

    fireEvent.change(select, { target: { value: commoditie } });

    const statusError = container.querySelector(".ant-select-status-error");

    expect(statusError).toBeInTheDocument();
  });
});
