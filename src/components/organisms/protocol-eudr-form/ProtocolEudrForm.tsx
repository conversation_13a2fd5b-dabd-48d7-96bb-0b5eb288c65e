import { ReportFormProps } from "@types";
import { Commodities } from "@utils";
import { Select } from "brainui";
import { useTranslation } from "react-i18next";
import { Content, TwoCols } from "./ProtocolEudrForm.styles";

function ProtocolEudrForm({ isSubmitted, data, onChange }: ReportFormProps) {
  const { t } = useTranslation();
  return (
    <Content data-testid="pb-t-protocol-eudr">
      <TwoCols>
        <Select
          data-testid="select-commodity"
          defaultValue={null}
          label={t("COMMODITY")}
          value={data.data?.commodity}
          options={Object.values(Commodities).map((commoditie) => ({
            value: commoditie,
            label: t(commoditie),
            ["data-testid"]: `option-commodity-${commoditie}`,
          }))}
          onSelect={(selectedCommodity: string) => {
            const newValue = {
              ...data,
              data: {
                ...(data.data || {}),
                commodity: selectedCommodity,
              },
            };
            onChange && onChange(newValue);
          }}
          status={isSubmitted && !data.data?.commodity ? "error" : ""}
        />
        <Select
          defaultValue={null}
          data-testid="select-languange"
          label={t("LANGUAGE")}
          value={data.data?.language}
          options={[
            {
              value: "en",
              label: t("ENGLISH"),
              ["data-testid"]: `option-language-en`,
            },
            {
              value: "pt_BR",
              label: t("PORTUGUESE"),
              ["data-testid"]: `option-language-pt_BR`,
            },
          ]}
          onSelect={(language: string) => {
            const newValue = {
              ...data,
              data: {
                ...(data.data || {}),
                language,
              },
            };
            onChange && onChange(newValue);
          }}
          status={isSubmitted && !data.data?.language ? "error" : ""}
        />
        <div></div>
      </TwoCols>
    </Content>
  );
}

export default ProtocolEudrForm;
