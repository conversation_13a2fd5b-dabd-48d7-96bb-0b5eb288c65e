import {
  BatchRequestForm,
  FormListItem,
  ReportSelect,
  SelectTag,
} from "@components";

import { createProperty } from "@services";

import { ReportType, RequestMethod } from "@types";
import { <PERSON><PERSON>, Button as AntdButton, Checkbox, Modal, Radio } from "antd";
import { <PERSON><PERSON>, Divider } from "brainui";
import { useTranslation } from "react-i18next";

import {
  Content,
  DrawerContent,
  FooterOptions,
  ReportSignContent,
  ReportSignWrapper,
  ReportSignWrapperDescription,
  ReportSignWrapperTitle,
} from "./ReportRequestForm.styles";
import { useReportRequestForm } from "@/hooks/use-report-request-form";
import { Property } from "@/types/property";

export interface ReportRequestValue {
  key: React.Key;
  reportType: ReportType;
  contact: any;
  observation: string;
  property: Property;
  data?: Record<string, any>;
  subareas?: Array<{ area_name: string; name: string; coordinates: any }>;
}

function ReportRequestForm() {
  const { t } = useTranslation();
  const {
    reportMetadata,
    REQUEST_METHODS,
    requestMethod,
    alertCredits,
    batchRequestFiles,
    handleRefBacenGeometry,
    handleRefBacenSubareas,
    handleRequestReports,
    isBacenGeometryModalOpen,
    isSaving,
    isSubmitted,
    properties,
    selectedTags,
    signature,
    subareas,
    isDisabledButtonRequestReport,
    isSavingBacenGeometry,
    isSignatureModalOpen,
    setAlertCredits,
    setBatchRequestFiles,
    setIsBacenGeometryModalOpen,
    setIsSavingBacenGeometry,
    setIsSignatureModalOpen,
    setRequestMethod,
    onAddItem,
    onFormChange,
    onRemoveItem,
    setSignature,
    value,
    setProperties,
    setSubareas,
    handleClose,
    setSelectedTags,
    tagsList,
    getValidReports,
    requestReports,
    hasSignatureResource,
    noSignatureResource,
    setReportType,
  } = useReportRequestForm();

  return (
    <div data-testid="pb-t-report-request-form" style={{ height: "100%" }}>
      <DrawerContent>
        <Content>
          <span>{t("REQUEST_METHOD_LABEL")}</span>
          {!reportMetadata.batchRequestOnly && (
            <Radio.Group
              data-testid="radio-group-request-methods"
              options={REQUEST_METHODS}
              value={requestMethod}
              onChange={({ target }) => {
                setRequestMethod(target.value);
              }}
            />
          )}
          {requestMethod == RequestMethod.MANUAL &&
            !reportMetadata?.batchRequestOnly &&
            value.map((reportRequestValue, index) => {
              return (
                <FormListItem
                  multiple
                  key={reportRequestValue.key}
                  count={index + 1}
                  onAdd={onAddItem}
                  onRemove={() => onRemoveItem(reportRequestValue)}
                  disabled={isSaving}
                  canRemove={index < value.length - 1}
                >
                  <ReportSelect
                    data={reportRequestValue}
                    subareas={subareas}
                    properties={properties}
                    initialReportType={reportMetadata?.type}
                    isSubmitted={isSubmitted}
                    onChange={(value) => {
                      onFormChange(reportRequestValue.key, value);
                    }}
                    onPropertiesLoad={(newProperties) =>
                      setProperties(newProperties)
                    }
                    onSubareasLoad={(newSubareas) => setSubareas(newSubareas)}
                    onRefBacenGeometryLoad={handleRefBacenGeometry}
                    onRefBacenSubareasLoad={handleRefBacenSubareas}
                  />
                </FormListItem>
              );
            })}

          {requestMethod == RequestMethod.BATCH && (
            <BatchRequestForm
              reportType={reportMetadata?.type}
              files={batchRequestFiles}
              onChange={(files) => setBatchRequestFiles(files)}
              onChangeReportType={(reportType) => setReportType(reportType)}
            />
          )}
        </Content>

        <div>
          <Divider orientation="left" orientationMargin="0">
            {t("REPORT_CHARACTERISTICS")}
          </Divider>
          {reportMetadata?.hasSignature && (
            <ReportSignWrapper>
              <Checkbox
                disabled={!hasSignatureResource || !noSignatureResource}
                className="ar-checkbox-multiline"
                checked={signature}
                onChange={({ target: { checked } }) => {
                  setSignature(checked);
                }}
              >
                <ReportSignContent>
                  <ReportSignWrapperTitle>
                    {t("SIGN_REPORTS")}
                  </ReportSignWrapperTitle>
                  <ReportSignWrapperDescription>
                    {t("SIGN_REPORTS_INFO")}
                  </ReportSignWrapperDescription>
                </ReportSignContent>
              </Checkbox>
            </ReportSignWrapper>
          )}

          <SelectTag
            tags={tagsList}
            placeholder={t("TAGS_PLACEHOLDER")}
            disabled={isSaving}
            value={selectedTags}
            onChange={(tags: string[]) => {
              setSelectedTags(tags);
            }}
            optionLimit={selectedTags.length == 3}
          />
          <FooterOptions>
            <AntdButton type="ghost" onClick={handleClose} disabled={isSaving}>
              {t("CANCEL")}
            </AntdButton>
            {alertCredits && (
              <Alert
                className="ar-alert-message"
                message={t("ALERT_LOW_CREDITS")}
                type="error"
                closable
                onClose={() => setAlertCredits(false)}
              />
            )}
            <Button
              onClick={handleRequestReports}
              disabled={isSaving || isDisabledButtonRequestReport}
              data-testid="button-request-report"
            >
              {getValidReports().length > 1
                ? t("ACTION_REQUEST_N_REPORTS", {
                    value: getValidReports().length,
                  })
                : t("ACTION_REQUEST_REPORT")}
            </Button>
          </FooterOptions>
        </div>
      </DrawerContent>
      <Modal
        title={t("SIGN_POPUP_ALERT")}
        open={isSignatureModalOpen}
        onOk={() => {
          requestReports();
          setIsSignatureModalOpen(false);
        }}
        onCancel={() => {
          setIsSignatureModalOpen(false);
        }}
      >
        <p>
          {t(
            signature
              ? "SIGN_POPUP_ALERT_DESCRIPTION_SIGNED"
              : "SIGN_POPUP_ALERT_DESCRIPTION_UNSIGNED"
          )}
        </p>
      </Modal>

      <Modal
        title={t("ALERT_NEW_GEOMETRY_BACEN")}
        open={isBacenGeometryModalOpen}
        onOk={async () => {
          setIsSavingBacenGeometry(true);
          await Promise.all(
            value
              .filter((x) => x.property?.isInternal)
              .map((x) =>
                createProperty(
                  x.contact.id,
                  x.contact.group_id,
                  x.property.area_name,
                  x.property.area_coordinates,
                  x.property.car,
                  x.property.origin
                )
              )
          );
          requestReports();
          setIsSavingBacenGeometry(false);
          setIsBacenGeometryModalOpen(false);
        }}
        confirmLoading={isSavingBacenGeometry}
        onCancel={() => {
          requestReports();
          setIsBacenGeometryModalOpen(false);
        }}
        cancelText={t("NO")}
        okText={t("YES")}
      >
        {t("RELATED_TO_CODE_VALUE", { value: value[0]?.data?.refBacen })}
      </Modal>
    </div>
  );
}

export default ReportRequestForm;
