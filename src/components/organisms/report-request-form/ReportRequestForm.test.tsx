import ReportRequestForm from "./ReportRequestForm";
import { cleanup, render, screen, fireEvent } from "@testing-library/react";
import { useReportRequestForm } from "@/hooks/use-report-request-form";
import { ReportType } from "@types";
import { GeoJSONGeometry } from "ol/format/GeoJSON";

const geoJson: GeoJSONGeometry = {
  coordinates: [],
  type: "MultiPolygon",
};
jest.mock("@/hooks/use-account-details", () => {
  return {
    useAccountDetails: jest.fn(() => ({
      handleGetDetailsAccount: jest.fn(),
      hasResource: jest.fn(() => false),
    })),
  };
});

jest.mock("@/hooks/use-report-request-form");

jest.mock("@/utils", () => {
  return {
    formatDocument: jest.fn((doc) => doc),
    geojsonToFeatures: jest.fn(),
    getExtent: jest.fn(),
    getInputStatus: jest.fn(),
    isIncra: jest.fn(),
  };
});

describe("ReportRequestForm component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useReportRequestForm as jest.Mock).mockReturnValue({
      reportMetadata: {
        batchRequestOnly: false,
        hasSignature: true,
        type: ReportType.DETAILED_ANALYSIS_DEFORESTATION,
      },
      REQUEST_METHODS: [
        { label: "Manual", value: "MANUAL" },
        { label: "Batch", value: "BATCH" },
      ],
      requestMethod: "MANUAL",
      alertCredits: false,
      batchRequestFiles: [],
      handleRefBacenGeometry: jest.fn(),
      handleRefBacenSubareas: jest.fn(),
      handleRequestReports: jest.fn(),
      isBacenGeometryModalOpen: false,
      isSaving: false,
      isSubmitted: false,
      properties: [
        {
          area_coordinates: geoJson,
        },
      ],
      selectedTags: [],
      signature: false,
      subareas: [geoJson],
      isDisabledButtonRequestReport: false,
      isSavingBacenGeometry: false,
      isSignatureModalOpen: false,
      setAlertCredits: jest.fn(),
      setBatchRequestFiles: jest.fn(),
      setIsBacenGeometryModalOpen: jest.fn(),
      setIsSavingBacenGeometry: jest.fn(),
      setIsSignatureModalOpen: jest.fn(),
      setRequestMethod: jest.fn(),
      onAddItem: jest.fn(),
      onFormChange: jest.fn(),
      onRemoveItem: jest.fn(),
      setSignature: jest.fn(),
      value: [
        {
          key: "1",
          reportType: ReportType.DETAILED_ANALYSIS_DEFORESTATION,
          contact: {
            name: "john-doe",
            document_type: "CPF",
            value: "09999999999",
          },
          observation: "",
          property: {},
        },
      ],
      setProperties: jest.fn(),
      setSubareas: jest.fn(),
      handleClose: jest.fn(),
      setSelectedTags: jest.fn(),
      tagsList: [],
      getValidReports: jest.fn(() => []),
      requestReports: jest.fn(),
      hasSignatureResource: true,
      noSignatureResource: true,
      setReportType: jest.fn(),
    });
  });

  afterEach(() => {
    cleanup();
  });

  it("should call setRequestMethod on radio request method is clicked", async () => {
    const setRequestMethod = jest.fn();

    (useReportRequestForm as jest.Mock).mockReturnValue({
      ...useReportRequestForm(),
      setRequestMethod,
    });

    const { getByTestId } = render(<ReportRequestForm />);

    const radio = getByTestId("radio-group-request-methods").querySelectorAll(
      "input"
    )[1];

    fireEvent.click(radio);

    expect(setRequestMethod).toHaveBeenCalled();
  });

  it("should call on remove item when click on FormListItem button remove", () => {
    const onRemoveItem = jest.fn();

    (useReportRequestForm as jest.Mock).mockReturnValue({
      ...useReportRequestForm(),
      onRemoveItem,
      value: [
        ...useReportRequestForm().value,
        {
          key: "2",
          reportType: ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR,
          contact: {
            name: "jack-doe",
            document_type: "CPF",
            value: "09999999998",
          },
          observation: "",
          property: {},
        },
      ],
    });
    const { getAllByTestId } = render(<ReportRequestForm />);

    const button = getAllByTestId("pb-t-form-list-item-action")[0];

    fireEvent.click(button);

    expect(onRemoveItem).toHaveBeenCalled();
  });
});
