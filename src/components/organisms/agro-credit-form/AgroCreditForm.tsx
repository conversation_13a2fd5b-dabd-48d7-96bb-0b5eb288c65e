import { Steps } from "antd";
import { AgroCreditFormConfirm } from "../agro-credit-form-confirm";
import AgroCreditFormReview from "../agro-credit-form-review/AgroCreditFormReview";
import { AgroCreditFormScores } from "../agro-credit-form-scores";
import { AgroCreditUpload } from "../agro-credit-upload";
import { Wrapper } from "./AgroCreditForm.styles";
import { useAgroCreditForm } from "../../../hooks/use-agro-credit-form";

export interface AgroCreditFormData {
  type: string;
  document: string;
  debt: number;
  scoreName: string;
  score: number;
  nonComplianceChance: string;
  date: string;
}

export interface AgroCreditFormState {
  filename: string;
  file: File | null;
  tags: string[];
  documents: { document: string; debt: string }[];
  invalidRows: { document: string; debt: string }[];
  scores: string[];
}

function AgroCreditForm() {
  const {
    step,
    setStep,
    resetState,
    handleSubmit,
    setState,
    state,
    isSubmitting,
  } = useAgroCreditForm();

  return (
    <Wrapper data-testid="pb-t-agro-credit">
      <Steps
        current={step}
        items={[{}, {}, {}, {}]}
        className="empty-title-steps"
      />
      {step == 0 && (
        <AgroCreditUpload
          data={state}
          onChange={(data) => {
            if (!data) {
              resetState();
              return;
            }
            setState(data);
          }}
          onNext={() => {
            setStep(step + 1);
          }}
        />
      )}
      {step == 1 && (
        <AgroCreditFormReview
          data={state}
          onChange={() => {
            resetState();
          }}
          onBack={() => {
            setStep(step - 1);
          }}
          onNext={() => {
            setStep(step + 1);
          }}
        />
      )}
      {step == 2 && (
        <AgroCreditFormScores
          data={state}
          onChange={(data) => {
            if (!data) {
              resetState();
              return;
            }
            setState(data);
          }}
          onBack={() => {
            setStep(step - 1);
          }}
          onNext={() => {
            setStep(step + 1);
          }}
        />
      )}
      {step == 3 && (
        <AgroCreditFormConfirm
          data={state}
          disabled={isSubmitting}
          onChange={() => {
            resetState();
          }}
          onBack={() => {
            setStep(step - 1);
          }}
          onNext={() => {
            handleSubmit();
          }}
        />
      )}
    </Wrapper>
  );
}

export default AgroCreditForm;
