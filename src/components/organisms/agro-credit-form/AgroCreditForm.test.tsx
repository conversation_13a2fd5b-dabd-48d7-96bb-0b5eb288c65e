import { cleanup, render, screen, fireEvent } from "@testing-library/react";
import React from "react";
import AgroCreditForm from "./AgroCreditForm";
import { Provider } from "react-redux";
import { store } from "../../../store";
import { useAgroCreditForm } from "../../../hooks/use-agro-credit-form";

jest.mock("../../../hooks/use-agro-credit-form");

jest.mock("../agro-credit-upload/AgroCreditUpload", () => ({
  __esModule: true,
  default: ({ onNext, onChange }) => (
    <div data-testid="pb-t-agro-credit-upload">
      <button data-testid="upload-next-button" onClick={onNext}>Next</button>
      <button data-testid="upload-reset-button" onClick={() => onChange()}>Reset</button>
      <button data-testid="upload-change-button" onClick={() => onChange({ tags: ["new-tag"] })}>Change</button>
    </div>
  ),
}));

jest.mock("../agro-credit-form-review/AgroCreditFormReview", () => ({
  __esModule: true,
  default: ({ onNext, onBack, onChange }) => (
    <div data-testid="pb-t-agro-credit-review">
      <button data-testid="review-next-button" onClick={onNext}>Next</button>
      <button data-testid="review-back-button" onClick={onBack}>Back</button>
      <button data-testid="review-reset-button" onClick={onChange}>Reset</button>
    </div>
  ),
}));

jest.mock("../agro-credit-form-scores/AgroCreditFormScores", () => ({
  __esModule: true,
  default: ({ onNext, onBack, onChange }) => (
    <div data-testid="pb-t-agro-credit-scores">
      <button data-testid="scores-next-button" onClick={onNext}>Next</button>
      <button data-testid="scores-back-button" onClick={onBack}>Back</button>
      <button data-testid="scores-reset-button" onClick={() => onChange()}>Reset</button>
      <button data-testid="scores-change-button" onClick={() => onChange({ scores: ["new-score"] })}>Change</button>
    </div>
  ),
}));

jest.mock("../agro-credit-form-confirm/AgroCreditFormConfirm", () => ({
  __esModule: true,
  default: ({ onNext, onBack, onChange, disabled }) => (
    <div data-testid="pb-t-agro-credit-confirm">
      <button data-testid="confirm-submit-button" onClick={onNext} disabled={disabled}>Submit</button>
      <button data-testid="confirm-back-button" onClick={onBack}>Back</button>
      <button data-testid="confirm-reset-button" onClick={onChange}>Reset</button>
    </div>
  ),
}));

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

const mockSetStep = jest.fn();
const mockResetState = jest.fn();
const mockSetState = jest.fn();
const mockHandleSubmit = jest.fn();

const defaultMockState = {
  tags: ["tag1", "tag2"],
  documents: [
    {
      document: "CPF",
      debt: "123",
    },
  ],
  invalidRows: [],
  scores: ["CREDIT_RISK"],
};

describe("AgroCreditForm component", () => {
  beforeEach(() => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 0,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });
  });

  it("should render the component with step 0 (upload)", () => {
    render(<AgroCreditForm />);
    const component = screen.queryByTestId("pb-t-agro-credit");
    expect(component).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-agro-credit-upload")).toBeInTheDocument();
  });

  it("should render the component with step 1 (review)", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 1,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    expect(screen.getByTestId("pb-t-agro-credit")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-agro-credit-review")).toBeInTheDocument();
  });

  it("should render the component with step 2 (scores)", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 2,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    expect(screen.getByTestId("pb-t-agro-credit")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-agro-credit-scores")).toBeInTheDocument();
  });

  it("should render the component with step 3 (confirm)", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 3,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    expect(screen.getByTestId("pb-t-agro-credit")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-agro-credit-confirm")).toBeInTheDocument();
  });

  it("should handle next button click in upload step", () => {
    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("upload-next-button"));
    expect(mockSetStep).toHaveBeenCalledWith(1);
  });

  it("should handle next button click in review step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 1,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("review-next-button"));
    expect(mockSetStep).toHaveBeenCalledWith(2);
  });

  it("should handle back button click in review step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 1,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("review-back-button"));
    expect(mockSetStep).toHaveBeenCalledWith(0);
  });

  it("should handle next button click in scores step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 2,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("scores-next-button"));
    expect(mockSetStep).toHaveBeenCalledWith(3);
  });

  it("should handle back button click in scores step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 2,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("scores-back-button"));
    expect(mockSetStep).toHaveBeenCalledWith(1);
  });

  it("should handle submit button click in confirm step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 3,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("confirm-submit-button"));
    expect(mockHandleSubmit).toHaveBeenCalled();
  });

  it("should handle back button click in confirm step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 3,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("confirm-back-button"));
    expect(mockSetStep).toHaveBeenCalledWith(2);
  });

  it("should handle reset state in upload step", () => {
    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("upload-reset-button"));
    expect(mockResetState).toHaveBeenCalled();
  });

  it("should handle reset state in review step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 1,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("review-reset-button"));
    expect(mockResetState).toHaveBeenCalled();
  });

  it("should handle reset state in scores step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 2,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("scores-reset-button"));
    expect(mockResetState).toHaveBeenCalled();
  });

  it("should handle reset state in confirm step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 3,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("confirm-reset-button"));
    expect(mockResetState).toHaveBeenCalled();
  });

  it("should handle state change in upload step", () => {
    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("upload-change-button"));
    expect(mockSetState).toHaveBeenCalledWith({ tags: ["new-tag"] });
  });

  it("should handle state change in scores step", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 2,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: false,
    });

    render(<AgroCreditForm />);
    fireEvent.click(screen.getByTestId("scores-change-button"));
    expect(mockSetState).toHaveBeenCalledWith({ scores: ["new-score"] });
  });

  it("should disable submit button when isSubmitting is true", () => {
    (useAgroCreditForm as jest.Mock).mockReturnValue({
      step: 3,
      setStep: mockSetStep,
      handleSubmit: mockHandleSubmit,
      resetState: mockResetState,
      setState: mockSetState,
      state: defaultMockState,
      isSubmitting: true,
    });

    render(<AgroCreditForm />);
    expect(screen.getByTestId("confirm-submit-button")).toBeDisabled();
  });
});
