import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import SoyDeforestationFormBatch from "./SoyDeforestationFormBatch";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/hooks/use-app-dispatch";
import { useSelector } from "react-redux";
import { ReportType } from "@types";
import <PERSON> from "papaparse";
import { notification } from "antd";

jest.mock("react-i18next");
jest.mock("@/hooks/use-app-dispatch");
jest.mock("react-redux");
jest.mock("antd", () => {
  const antd = jest.requireActual("antd");
  return {
    ...antd,
    notification: {
      warning: jest.fn(),
      error: jest.fn(),
    },
  };
});

describe("SoyDeforestationFormBatch", () => {
  const notificationWarningMock = jest.fn();
  const notificationErrorMock = jest.fn();
  const translationMock = jest.fn((text) => text);
  const dispatchMock = jest.fn();
  const useSelectorMock = jest.fn(() => ({
    soybeanDeforestationForm: {
      reportType: ReportType.SOY_DEFORESTATION_CAR,
    },
  }));
  const files = [
    {
      tempId: "1",
      name: "file1.csv",
      file: new File(["content"], "file1.csv", { type: "text/csv" }),
      lineCount: 10,
    },
  ];
  beforeEach(() => {
    (notification.warning as jest.Mock).mockImplementation(
      notificationWarningMock
    );
    (notification.error as jest.Mock).mockImplementation(notificationErrorMock);
    (useSelector as jest.Mock).mockImplementation(useSelectorMock);
    (useTranslation as jest.Mock).mockReturnValue({
      t: translationMock,
    });
    (useAppDispatch as jest.Mock).mockReturnValue(dispatchMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(<SoyDeforestationFormBatch files={files} onChange={() => {}} />);

    const component = screen.queryByTestId("pb-t-soy-deforestation-form-batch");
    expect(component).toBeInTheDocument();
    expect(useSelectorMock).toHaveBeenCalled();
  });

  it("should call dispatch when report type radio is clicked", () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <SoyDeforestationFormBatch files={files} onChange={onChangeMock} />
    );

    const radio = getByTestId("radio-group-report-type").querySelector("input");

    fireEvent.click(radio);

    expect(dispatchMock).toHaveBeenCalled();
  });

  it("should call handleBeforeUpload when a file is uploaded", async () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <SoyDeforestationFormBatch files={files} onChange={onChangeMock} />
    );

    const dragger = getByTestId("dragger-input-file");
    const csvData = Papa.unparse(
      [
        {
          CAR: "**********-0006F49C7F6B41E48FDD1CF83BD479BC",
          "DATA DE CORTE": "01/01/2014",
          SAFRA: "2023/24",
        },
      ],
      {
        delimiter: ";",
      }
    );

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [
          new File([csvData.trim()], "filename.csv", { type: "text/csv" }),
        ],
      },
    });

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalled();
    });
  });

  it("should call handleBeforeUpload when a file is uploaded", async () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <SoyDeforestationFormBatch files={files} onChange={onChangeMock} />
    );

    const dragger = getByTestId("dragger-input-file");
    const csvData = Papa.unparse(
      [
        {
          CAR: "**********-0006F49C7F6B41E48FDD1CF83BD479BC",
          "DATA DE CORTE": "01/01/2014",
          SAFRA: "2023/24",
        },
      ],
      {
        delimiter: ";",
      }
    );

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [
          new File([csvData.trim()], "filename.csv", { type: "text/csv" }),
        ],
      },
    });

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalled();
    });
  });

  it("should show a notification if file is too large", async () => {
    const { getByTestId } = render(
      <SoyDeforestationFormBatch files={files} onChange={() => {}} />
    );

    const dragger = getByTestId("dragger-input-file");

    const blob = new Blob([new Uint8Array(101 * 1024 * 1024)], {
      type: "text/csv",
    });

    const file = new File([blob], "filename.csv", {
      type: "text/csv",
    });

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [file],
      },
    });

    await waitFor(() => {
      expect(notificationWarningMock).toHaveBeenCalledWith({
        message: "FILE_NOT_SENT",
        description: "ALERT_FILE_SIZE",
      });
    });
  });

  it("should show notification when file upload has same name of previous file", async () => {
    const { getByTestId } = render(
      <SoyDeforestationFormBatch files={files} onChange={() => {}} />
    );

    const dragger = getByTestId("dragger-input-file");

    const csvData = Papa.unparse(
      [
        {
          CAR: "**********-0006F49C7F6B41E48FDD1CF83BD479BC",
          "DATA DE CORTE": "01/01/2014",
          SAFRA: "2023/24",
        },
      ],
      {
        delimiter: ";",
      }
    );

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [
          new File([csvData.trim()], files[0].name, { type: "text/csv" }),
        ],
      },
    });

    await waitFor(() => {
      expect(notificationWarningMock).toHaveBeenCalledWith({
        message: "ALERT_FILE_SAME_NAME",
      });
    });
  });

  it("should call onChange when onRemove button is clicked", () => {
    const onChangeMock = jest.fn();

    const { getByTestId } = render(
      <SoyDeforestationFormBatch files={files} onChange={onChangeMock} />
    );

    const button = getByTestId("pb-t-form-list-item-action");
    fireEvent.click(button);

    expect(onChangeMock).toHaveBeenCalled();
  });
});
