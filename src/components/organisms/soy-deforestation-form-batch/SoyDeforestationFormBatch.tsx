import { MODEL_BY_REPORT_TYPE } from "@constants";
import { FileRequest, ReportType } from "@types";
import { Button, notification, Radio } from "antd";
import Dragger from "antd/lib/upload/Dragger";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import { icon_cloud_upload } from "@/assets";
import { useAppDispatch } from "@/hooks/use-app-dispatch";
import { setReportTypeAction } from "@/store/modules/soy-deforestation-form/actions";
import {
  BatchRequestInputRow,
  DraggerHint,
  DraggerText,
  DraggerWrapper,
  FormWrapper,
  HighlightedText,
  UploadedFile,
  UploadedFilesList,
} from "./SoyDeforestationFormBatch.style";
import { FormListItem } from "../../molecules";
import { RootState } from "../../../store";

interface SoyDeforestationFormBatchProps {
  files: FileRequest[];
  onChange: (files: FileRequest[]) => void;
}

function SoyDeforestationFormBatch({
  files,
  onChange,
}: SoyDeforestationFormBatchProps) {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const reportType = useSelector(
    (state: RootState) => state.soybeanDeforestationForm.reportType
  );

  function handleBeforeUpload(file) {
    const reader = new FileReader();
    if (file.size / (1024 * 1000) > 100) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 100 }),
      });
      return;
    }
    if (files.find((f: FileRequest) => f.name == file.name)) {
      notification.warning({
        message: t("ALERT_FILE_SAME_NAME"),
      });
      return;
    }
    reader.onload = async ({ target }) => {
      const lineCount = (target.result as string)
        .trim()
        .replace(/""|^\n/g, "") // Remove "" e linhas em branco antes do cabecalho
        .replace(/"[^"]+"/g, "") // Remove \n entre aspas duplas
        .match(/\n/g).length; // Conta a quantidade de \n

      const newFiles = [
        ...files,
        {
          tempId: uuidv4(),
          name: file.name,
          file,
          lineCount,
        },
      ];
      onChange(newFiles);
    };

    reader.readAsText(file);
  }

  return (
    <FormWrapper data-testid="pb-t-soy-deforestation-form-batch">
      <BatchRequestInputRow>
        <Radio.Group
          data-testid="radio-group-report-type"
          size="small"
          value={reportType}
          onChange={({ target }) => {
            dispatch(setReportTypeAction(target.value));
            onChange([]);
          }}
        >
          <Radio.Button value={ReportType.SOY_DEFORESTATION_CAR}>
            {t("PROPERTIES")}
          </Radio.Button>
          <Radio.Button value={ReportType.SOY_DEFORESTATION_MUNICIPALITY}>
            {t("MUNICIPALITIES")}
          </Radio.Button>
        </Radio.Group>
        {t("INFO_UPLOAD_SOY_DEFORESTATION")}
        <a href={MODEL_BY_REPORT_TYPE[reportType]} download target="_blank">
          <Button>{t("DOWNLOAD_SAMPLE_CSV")}</Button>
        </a>
        <div>
          <Dragger
            beforeUpload={handleBeforeUpload}
            customRequest={() => {}}
            fileList={[]}
            accept=".csv"
            data-testid="dragger-input-file"
          >
            <DraggerWrapper>
              <DraggerText className="ant-upload-text">
                {t("CHOOSE_FILES")}
              </DraggerText>
              <DraggerHint className="ant-upload-hint">
                {t("DRAG_DROP_OR")}{" "}
                <HighlightedText>
                  {t("CLICK_TO_UPLOAD_FROM_FILES")}
                </HighlightedText>
                .
              </DraggerHint>
              <img
                src={icon_cloud_upload}
                alt="Upload file"
                width="24"
                height="24"
              />
            </DraggerWrapper>
          </Dragger>
        </div>
      </BatchRequestInputRow>
      <UploadedFilesList>
        {files.map((file, index) => (
          <FormListItem
            multiple
            key={file.tempId}
            count={index + 1}
            onRemove={() => {
              onChange(files.filter((f) => f.tempId !== file.tempId));
            }}
            disabled={false}
            canRemove={true}
          >
            <UploadedFile>
              <div>
                <p>{t("SELECTED_FILE")}</p>
                <p>{file.name}</p>
              </div>

              <div>
                <p>{t("FILE_TOTAL")}</p>
                <p>
                  {file.lineCount}{" "}
                  {file.lineCount != 1 ? t("REPORTS") : t("REPORT")}
                </p>
              </div>
            </UploadedFile>
          </FormListItem>
        ))}
      </UploadedFilesList>
    </FormWrapper>
  );
}

export default SoyDeforestationFormBatch;
