import { PlusOutlined } from "@ant-design/icons";
import { SOY_DEFORESTATION_CROP_YEARS } from "@constants";
import { ReportType } from "@types";
import { isCarValid, maskCAR } from "@utils";
import { AutoComplete, Button, Form, notification, Radio } from "antd";
import { DatePicker, Select } from "brainui";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import municipalities from "@/assets/data/cities.json";
import { useAppDispatch } from "@/hooks/use-app-dispatch";
import {
  setReportTypeAction,
  setSelectedCARsAction,
  setSelectedMunicipalitiesAction,
} from "@/store/modules/soy-deforestation-form/actions";
import { OptionItem } from "@/components/atoms";
import { FormListItem } from "@/components/molecules";
import {
  CarInput,
  CarInputWrapper,
  FieldWrapper,
  FormContent,
  MultiOptionItem,
  OptionList,
  Section,
} from "./SoyDeforestationFormManual.styles";
import { RootState } from "@/store";

interface SoyDeforestationFormManualProps {
  reportType: ReportType;
  selectedCARs: string[];
  selectedMunicipalities: { value: number; label: string }[];
}

function SoyDeforestationFormManual({
  reportType,
  selectedCARs,
  selectedMunicipalities,
}: SoyDeforestationFormManualProps) {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const [carCode, setCarCode] = useState("");
  const [municipalitySearch, setMunicipalitySearch] = useState("");
  const isLoading = useSelector(
    (state: RootState) => state.soybeanDeforestationForm.isLoading
  );

  return (
    <FormContent>
      <FormListItem>
        <Section>
          <FieldWrapper>
            <Form.Item
              name="cutoffDate"
              rules={[{ required: true, message: t("REQUIRED_FIELD") }]}
            >
              <DatePicker
                className="date-picker"
                label={t("CUTOFF_DATE")}
                placeholder={t("SELECT")}
                format="DD/MM/YYYY"
              />
            </Form.Item>
          </FieldWrapper>
          <FieldWrapper>
            <Form.Item
              name="cropYear"
              rules={[{ required: true, message: t("REQUIRED_FIELD") }]}
            >
              <Select
                label={t("CROP_YEAR")}
                options={SOY_DEFORESTATION_CROP_YEARS}
              />
            </Form.Item>
          </FieldWrapper>

          <FieldWrapper>
            <Form.Item
              name="language"
              rules={[{ required: true, message: t("REQUIRED_FIELD") }]}
            >
              <Select
                defaultValue={null}
                label={t("LANGUAGE")}
                options={[
                  {
                    value: "en",
                    label: t("ENGLISH"),
                  },
                  {
                    value: "pt_BR",
                    label: t("PORTUGUESE"),
                  },
                ]}
              />
            </Form.Item>
          </FieldWrapper>
        </Section>
        <Section $gap="16px">
          <h2>{t("INTEREST_AREA")}</h2>
          <Radio.Group
            size="small"
            data-testid="radio-group-report-type"
            value={reportType}
            onChange={(e) => {
              dispatch(setReportTypeAction(e.target.value));
            }}
            disabled={isLoading}
          >
            <Radio.Button value={ReportType.SOY_DEFORESTATION_CAR}>
              {t("PROPERTIES")}
            </Radio.Button>
            <Radio.Button value={ReportType.SOY_DEFORESTATION_MUNICIPALITY}>
              {t("MUNICIPALITIES")}
            </Radio.Button>
          </Radio.Group>

          {reportType == ReportType.SOY_DEFORESTATION_CAR && (
            <CarInputWrapper>
              <CarInput
                value={carCode}
                placeholder={t("SEARCH_CAR")}
                onChange={(event) => {
                  setCarCode(maskCAR(event.target.value));
                }}
                disabled={isLoading || selectedCARs.length >= 10}
              />
              <Button
                type="primary"
                shape="circle"
                size="small"
                icon={<PlusOutlined />}
                disabled={!isCarValid(carCode)}
                onClick={() => {
                  if (selectedCARs.includes(carCode)) {
                    notification.warning({
                      message: t("CAR_ALREADY_EXISTS"),
                    });
                    return;
                  }
                  dispatch(
                    setSelectedCARsAction([...selectedCARs, maskCAR(carCode)])
                  );
                  setCarCode("");
                }}
              />
            </CarInputWrapper>
          )}

          {reportType == ReportType.SOY_DEFORESTATION_MUNICIPALITY && (
            <AutoComplete
              value={municipalitySearch}
              placeholder={t("SEARCH_MUNICIPALITY")}
              disabled={isLoading || selectedMunicipalities.length >= 10}
              onSearch={(search) => {
                setMunicipalitySearch(search);
              }}
              filterOption={(inputValue, option) => {
                return option.children[0]
                  .toString()
                  .toLowerCase()
                  .startsWith(inputValue.toLowerCase());
              }}
              onChange={(ibgeCode: string) => {
                const municipality = municipalities.find(
                  (c) => c.ibge_code == Number(ibgeCode)
                );
                if (!municipality) return;
                if (
                  selectedMunicipalities.find(
                    (m) => m.value == municipality.ibge_code
                  )
                ) {
                  dispatch(
                    setSelectedMunicipalitiesAction(
                      selectedMunicipalities.filter(
                        (c) => c.value != municipality.ibge_code
                      )
                    )
                  );
                  return;
                }
                dispatch(
                  setSelectedMunicipalitiesAction([
                    ...selectedMunicipalities,
                    {
                      label: `${municipality.name} - ${municipality.uf}`,
                      value: municipality.ibge_code,
                    },
                  ])
                );
              }}
            >
              {municipalities.map((municipality) => (
                <MultiOptionItem
                  key={municipality.ibge_code}
                  value={municipality.ibge_code}
                  className={
                    selectedMunicipalities.find(
                      (x) => x.value == municipality.ibge_code
                    )
                      ? "ant-select-item-option-active"
                      : ""
                  }
                >
                  {municipality.name} - {municipality.uf}
                </MultiOptionItem>
              ))}
            </AutoComplete>
          )}

          <OptionList>
            <span>
              {t(
                reportType == ReportType.SOY_DEFORESTATION_CAR
                  ? "SELECTED_PROPERTIES"
                  : "SELECTED_MUNICIPALITIES"
              )}
              :
            </span>
            {reportType == ReportType.SOY_DEFORESTATION_CAR &&
              selectedCARs.map((car) => (
                <OptionItem
                  key={car}
                  label={maskCAR(car)}
                  value={car}
                  disabled={isLoading}
                  onRemove={(carToRemove) => {
                    dispatch(
                      setSelectedCARsAction(
                        selectedCARs.filter((c) => c != carToRemove)
                      )
                    );
                  }}
                />
              ))}

            {reportType == ReportType.SOY_DEFORESTATION_MUNICIPALITY &&
              selectedMunicipalities.map((municipality) => (
                <OptionItem
                  key={municipality.value}
                  label={`${municipality.label}`}
                  value={municipality}
                  disabled={isLoading}
                  onRemove={(municipality) => {
                    dispatch(
                      setSelectedMunicipalitiesAction(
                        selectedMunicipalities.filter(
                          (c) => c.value != municipality.value
                        )
                      )
                    );
                  }}
                />
              ))}
          </OptionList>
        </Section>
      </FormListItem>
    </FormContent>
  );
}

export default SoyDeforestationFormManual;
