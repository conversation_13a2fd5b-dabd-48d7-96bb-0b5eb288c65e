import styled from "styled-components";
import { AutoComplete, Input } from "antd";
const { Option } = AutoComplete;

export const FieldWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

export const Section = styled.section<{ $gap?: string }>`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.$gap || 0};

  h2 {
    color: #717171;
    font-size: 1rem;
    font-weight: normal;
    margin: 16px 0 0;
  }
`;

export const FormContent = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding-right: 10px;
`;

export const OptionList = styled.div`
  display: flex;
  flex-direction: column;
`;

export const CarInputWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
  width: 100%;
`;

export const CarInput = styled(Input)`
  width: 100%;
`;

export const MultiOptionItem = styled(Option)``;
