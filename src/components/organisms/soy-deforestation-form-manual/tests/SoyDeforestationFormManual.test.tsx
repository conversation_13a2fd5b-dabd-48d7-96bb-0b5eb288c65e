import React from "react";
import { render, screen } from "@testing-library/react";
import { ReportType } from "@types";
import { Form } from "antd";

jest.mock("@reduxjs/toolkit", () => {
  const originalModule = jest.requireActual("@reduxjs/toolkit");
  return {
    ...originalModule,
    createReducer: jest.fn(() => ({})),
  };
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("@/hooks/use-app-dispatch", () => ({
  useAppDispatch: () => jest.fn(),
}));

jest.mock("react-redux", () => ({
  useSelector: () => false, // isLoading = false
}));

jest.mock("@utils", () => ({
  isCarValid: jest.fn(() => true),
  maskCAR: jest.fn((value) => value),
}));

jest.mock("@/store/modules/soy-deforestation-form/actions", () => ({
  setReportTypeAction: jest.fn(() => ({ type: "mock-set-report-type" })),
  setSelectedCARsAction: jest.fn(() => ({ type: "mock-set-selected-cars" })),
  setSelectedMunicipalitiesAction: jest.fn(() => ({ type: "mock-set-selected-municipalities" })),
}));

jest.mock("@/assets/data/cities.json", () => [
  { ibge_code: 1, name: "Municipality 1", uf: "UF1" },
  { ibge_code: 2, name: "Municipality 2", uf: "UF2" },
  { ibge_code: 3, name: "Municipality 3", uf: "UF3" },
]);

import SoyDeforestationFormManual from "../SoyDeforestationFormManual";

describe("SoyDeforestationFormManual", () => {
  const defaultProps = {
    reportType: ReportType.SOY_DEFORESTATION_CAR,
    selectedCARs: [],
    selectedMunicipalities: [],
  };

  it("should render the component with CAR report type", () => {
    render(
      <Form>
        <SoyDeforestationFormManual {...defaultProps} />
      </Form>
    );

    expect(screen.getByText("CUTOFF_DATE")).toBeInTheDocument();
    expect(screen.getByText("CROP_YEAR")).toBeInTheDocument();
    expect(screen.getByText("LANGUAGE")).toBeInTheDocument();
    expect(screen.getByText("INTEREST_AREA")).toBeInTheDocument();
    expect(screen.getByText("PROPERTIES")).toBeInTheDocument();
  });

  it("should render the component with MUNICIPALITY report type", () => {
    render(
      <Form>
        <SoyDeforestationFormManual
          {...defaultProps}
          reportType={ReportType.SOY_DEFORESTATION_MUNICIPALITY}
        />
      </Form>
    );

    expect(screen.getByText("CUTOFF_DATE")).toBeInTheDocument();
    expect(screen.getByText("CROP_YEAR")).toBeInTheDocument();
    expect(screen.getByText("LANGUAGE")).toBeInTheDocument();
    expect(screen.getByText("INTEREST_AREA")).toBeInTheDocument();
    expect(screen.getByText("MUNICIPALITIES")).toBeInTheDocument();
  });

  it("should render selected CARs", () => {
    render(
      <Form>
        <SoyDeforestationFormManual
          {...defaultProps}
          selectedCARs={["CAR-1", "CAR-2"]}
        />
      </Form>
    );

    expect(screen.getByText("CAR-1")).toBeInTheDocument();
    expect(screen.getByText("CAR-2")).toBeInTheDocument();
  });

  it("should render selected municipalities", () => {
    render(
      <Form>
        <SoyDeforestationFormManual
          {...defaultProps}
          reportType={ReportType.SOY_DEFORESTATION_MUNICIPALITY}
          selectedMunicipalities={[
            { value: 1, label: "Municipality 1" },
            { value: 2, label: "Municipality 2" },
          ]}
        />
      </Form>
    );

    expect(screen.getByText("Municipality 1")).toBeInTheDocument();
    expect(screen.getByText("Municipality 2")).toBeInTheDocument();
  });
});
