import { fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { Provider as ReduxProvider } from "react-redux";
import configureStore, { MockStoreEnhanced } from "redux-mock-store";
import thunk from "redux-thunk";
import { RequestFormContext } from "../../../contexts/RequestFormContext";
import { ReportType } from "../../../types";
import SoyDeforestationForm from "./SoyDeforestationForm";

const Provider = ReduxProvider as any;

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("antd", () => {
  const antd = jest.requireActual("antd");
  return {
    ...antd,
    notification: {
      error: jest.fn(),
      success: jest.fn(),
    },
  };
});

const mockStore = configureStore([thunk]);

const renderComponent = (store: any) => {
  const closeRequestForm = jest.fn();
  const openRequestForm = jest.fn();
  return render(
    <Provider store={store}>
      <RequestFormContext.Provider
        value={{
          closeRequestForm,
          isRequestFormOpen: true,
          reportMetadata: null,
          openRequestForm,
        }}
      >
        <SoyDeforestationForm />
      </RequestFormContext.Provider>
    </Provider>
  );
};

describe("SoyDeforestationForm", () => {
  let store: MockStoreEnhanced<unknown, {}>;

  beforeEach(() => {
    store = mockStore({
      soybeanDeforestationForm: {
        reportType: ReportType.SOY_DEFORESTATION_CAR,
        selectedCARs: [],
        selectedMunicipalities: [],
        isLoading: false,
      },
    });
  });

  it("renders the form with manual request method by default", () => {
    renderComponent(store);
    expect(
      screen.getByTestId("pb-t-soy-deforestation-form")
    ).toBeInTheDocument();
    expect(screen.getByText("MANUALLY")).toBeInTheDocument();
  });

  it("calls handleClose when cancel button is clicked", () => {
    const closeRequestForm = jest.fn();
    const openRequestForm = jest.fn();
    render(
      <Provider store={store}>
        <RequestFormContext.Provider
          value={{
            closeRequestForm,
            isRequestFormOpen: true,
            reportMetadata: null,
            openRequestForm,
          }}
        >
          <SoyDeforestationForm />
        </RequestFormContext.Provider>
      </Provider>
    );
    fireEvent.click(screen.getByText("CANCEL"));
    expect(closeRequestForm).toHaveBeenCalled();
  });
});
