import { RequestFormContext } from "@contexts";
import { FileRequest, ReportType, RequestMethod } from "@types";
import { Button, Form, notification, Radio } from "antd";
import { Moment } from "moment";
import { useContext, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useAppDispatch } from "../../../hooks/use-app-dispatch";
import {
  resetStateAction,
  thunkCreateSoyDeforestationRequest,
} from "../../../store/modules";
import SoyDeforestationFormBatch from "../soy-deforestation-form-batch/SoyDeforestationFormBatch";
import SoyDeforestationFormManual from "../soy-deforestation-form-manual/SoyDeforestationFormManual";
import { Actions, ArForm, Wrapper } from "./SoyDeforestationForm.styles";
import { v4 as uuidv4 } from "uuid";
import { RootState } from "../../../store";

function SoyDeforestationForm() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { closeRequestForm, isRequestFormOpen } =
    useContext(RequestFormContext);

  const [requestMethod, setRequestMethod] = useState<RequestMethod>(
    RequestMethod.MANUAL
  );
  const [files, setFiles] = useState<FileRequest[]>([]);

  const { selectedCARs, selectedMunicipalities, isLoading, error, reportType } =
    useSelector((state: RootState) => state.soybeanDeforestationForm);

  const [form] = Form.useForm();

  const REQUEST_METHODS = [
    { label: t("MANUALLY"), value: RequestMethod.MANUAL },
    { label: t("BATCH"), value: RequestMethod.BATCH },
  ];

  useEffect(() => {
    if (!isRequestFormOpen) {
      handleClose();
    }
  }, [isRequestFormOpen]);

  async function handleManualFormSubmit(values: {
    cutoffDate: Moment;
    cropYear: string;
    language: string;
  }) {
    if (
      reportType == ReportType.SOY_DEFORESTATION_CAR &&
      !selectedCARs.length
    ) {
      notification.error({
        message: t("SELECT_AT_LEAST_ONE_PROPERTY"),
      });
      return;
    }

    if (
      reportType == ReportType.SOY_DEFORESTATION_MUNICIPALITY &&
      !selectedMunicipalities.length
    ) {
      notification.error({
        message: t("SELECT_AT_LEAST_ON_MUNICIPALITY"),
      });
      return;
    }

    let header: string =
      values.language == "en"
        ? "CAR;DATA DE CORTE;SAFRA"
        : "CAR;CUTOFF DATE;CROP YEAR";
    let data: string[] = selectedCARs;
    let lineCount: number = selectedCARs.length;

    if (reportType == ReportType.SOY_DEFORESTATION_MUNICIPALITY) {
      header =
        values.language == "en"
          ? "MUNICIPALITY;CUTOFF DATE;CROP YEAR"
          : "MUNICÍPIO;DATA DE CORTE;SAFRA";
      data = selectedMunicipalities.map((x) => `${x.value}`);
      lineCount = selectedMunicipalities.length;
    }
    const cutoffDate = values.cutoffDate.format("DD/MM/YYYY");
    const dataStr = data
      .map((x) => `${x};${cutoffDate};${values.cropYear}`)
      .join("\n");
    const file = new File(
      [`${header}\n${dataStr}`],
      "soy_deforestation_request.csv",
      {
        type: "text/csv",
      }
    );
    handleFormSubmit([
      {
        tempId: uuidv4(),
        name: file.name,
        file,
        lineCount,
      },
    ]);
  }

  async function handleFormSubmit(files: FileRequest[]) {
    if (!files.length) {
      notification.error({
        message: t("ATTACH_AT_LEAST_ONE_FILE"),
      });
      return;
    }
    const result = await dispatch(thunkCreateSoyDeforestationRequest(files));

    if (result.type === thunkCreateSoyDeforestationRequest.rejected.type) {
      return;
    }

    notification.success({
      message:
        files.length > 1
          ? t("REPORTS_REQUESTED_SUCCESSFULLY")
          : t("REPORT_REQUESTED_SUCCESSFULLY"),
    });
    handleClose();
  }

  function handleClose() {
    dispatch(resetStateAction());
    closeRequestForm();
  }

  useEffect(() => {
    if (!error) return;
    notification.error({
      message: t(error) || error,
    });
  }, [error]);

  return (
    <Wrapper data-testid="pb-t-soy-deforestation-form">
      <Radio.Group
        options={REQUEST_METHODS}
        value={requestMethod}
        onChange={({ target }) => {
          setRequestMethod(target.value);
        }}
        disabled={isLoading}
      />
      {requestMethod == RequestMethod.MANUAL && (
        <ArForm
          form={form}
          onFinish={handleManualFormSubmit}
          disabled={isLoading}
        >
          <SoyDeforestationFormManual
            reportType={reportType}
            selectedCARs={selectedCARs}
            selectedMunicipalities={selectedMunicipalities}
          />
          <Actions>
            <Button onClick={handleClose} disabled={isLoading}>
              {t("CANCEL")}
            </Button>
            <Button type="primary" htmlType="submit" disabled={isLoading}>
              {t("ACTION_REQUEST_REPORT")}
            </Button>
          </Actions>
        </ArForm>
      )}

      {requestMethod == RequestMethod.BATCH && (
        <ArForm onFinish={() => handleFormSubmit(files)}>
          <SoyDeforestationFormBatch
            files={files}
            onChange={(value) => setFiles(value)}
          />
          <Actions>
            <Button onClick={handleClose} disabled={isLoading}>
              {t("CANCEL")}
            </Button>
            <Button type="primary" htmlType="submit" disabled={isLoading}>
              {t("ACTION_REQUEST_REPORT")}
            </Button>
          </Actions>
        </ArForm>
      )}
    </Wrapper>
  );
}

export default SoyDeforestationForm;
