import { Fragment, useEffect } from "react";
import { Carousel, Skeleton } from "antd";
import { useTranslation } from "react-i18next";
import { Banner, ReportCard } from "@components";
import { ReportType } from "@types";
import {
  AvailableReportsContainer,
  HomeTitle,
  HomeWrapper,
} from "./Home.styles";
import { useAccountDetails } from "../../../hooks/use-account-details";
import { useCardsList } from "../../../hooks/use-cards-list";
import { useBannersList } from "../../../hooks/use-banners-list";
import { useRequestForm } from "../../../hooks/use-request-form";

function Home() {
  const { t } = useTranslation();
  const { bannersList, isLoadingBanners } = useBannersList();
  const { cardsList, isLoadingCards } = useCardsList();
  const { handleGetDetailsAccount } = useAccountDetails();
  const { handleOpenRequestForm } = useRequestForm();

  const onClickCard = (type: ReportType) => {
    handleOpenRequestForm(type);
  };

  const reports: JSX.Element[] = cardsList
    .filter((report) => !report.isHidden)
    .map((report) => (
      <ReportCard
        {...report}
        key={report.title}
        onClick={(type) => onClickCard(type)}
      />
    ));

  useEffect(() => {
    handleGetDetailsAccount();
  }, []);

  return (
    <HomeWrapper data-testid="pb-t-home">
      {isLoadingBanners && (
        <Skeleton.Input className="ar-banner-skeleton" active={true} />
      )}

      {!isLoadingBanners && (
        <Carousel>
          {bannersList.map((banner) => (
            <Banner key={banner.id} {...banner} />
          ))}
        </Carousel>
      )}
      <HomeTitle>{t("DISCOVER_REPORTS")}</HomeTitle>
      {!isLoadingCards && (
        <AvailableReportsContainer>{reports}</AvailableReportsContainer>
      )}
    </HomeWrapper>
  );
}

export default Home;
