import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import { Provider } from "react-redux";
import { DataContext } from "../../../contexts/DataContext";
import { store } from "../../../store";
import Home from "./Home";

afterEach(() => {
  cleanup();
});

const ReduxProvider = Provider as any;

describe("Home component", () => {
  it("should render the component", (done) => {
    render(
      <ReduxProvider store={store}>
        <DataContext.Provider
          value={{
            accountInfo: null,
            banners: [],
            form: null,
            requests: [],
            count: 0,
            reportsCount: 0,
            tags: [],
            isLoadingBanners: false,
            user: null,
            cards: [],
            isLoadingCards: false,
            fetchTags: jest.fn(),
            applyFilters: jest.fn(),
            reloadReports: jest.fn(),
            fetchAccountInfo: jest.fn(),
          }}
        >
          <Home />
        </DataContext.Provider>
      </ReduxProvider>
    );
    const component = screen.queryByTestId("pb-t-home");
    expect(component).toBeInTheDocument();
    done();
  });
});
