import { fireEvent, render, screen } from "@testing-library/react";
import SinisterForm from "./SinisterForm";
import { <PERSON><PERSON>, EventSinister, getCropYears } from "@utils";

jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(() => ({
    t: (key: string) => key,
  })),
}));

describe("SinisterForm component", () => {
  it("should render the component", () => {
    const props = {} as any;
    render(
      <SinisterForm
        isSubmitted={false}
        data={{}}
        onTypeChange={() => {}}
        onChange={() => {}}
        {...props}
      />
    );
    const component = screen.queryByTestId("pb-t-sinister-form");
    expect(component).toBeInTheDocument();
  });

  it.each([
    ["input-institution-officer", "value"],
    ["input-total-area", "123456789123"],
    ["input-financial-institution", "value"],
    ["input-policy-number", "value"],
  ])("should call onChange when %p changes", (inputLabel, value) => {
    const onChangeMock = jest.fn();
    const props = {} as any;
    const { getByTestId } = render(
      <SinisterForm
        isSubmitted={false}
        data={{}}
        onTypeChange={() => {}}
        onChange={onChangeMock}
        {...props}
      />
    );

    const input = getByTestId(inputLabel);

    fireEvent.change(input, { target: { value } });

    expect(onChangeMock).toHaveBeenCalled();
  });

  it.each([
    [
      "select-crop",
      { testId: `option-select-crop-${Crop.CORN}`, value: Crop.CORN },
    ],
    [
      "select-event",
      {
        testId: `option-select-crop-${EventSinister.SECA}`,
        value: EventSinister.SECA,
      },
    ],
    [
      "select-crop-year",
      {
        testId: `option-select-crop-year-${getCropYears()[0].value}`,
        value: getCropYears()[0].value,
      },
    ],
  ])(
    "should call onChange when %p changes",
    (selectLabel, { testId, value }) => {
      const onChangeMock = jest.fn();
      const props = {} as any;
      const { getByTestId } = render(
        <SinisterForm
          isSubmitted={false}
          data={{
            contact: "contact",
            data: {
              cropYear: "2020/2022",
              crop: Crop.COTTON,
            },
          }}
          onTypeChange={() => {}}
          onChange={onChangeMock}
          {...props}
        />
      );

      const select = getByTestId(selectLabel).querySelector("input");

      fireEvent.change(select, { target: { value } });

      const option = getByTestId(testId);

      fireEvent.click(option);

      expect(onChangeMock).toHaveBeenCalled();
    }
  );
});
