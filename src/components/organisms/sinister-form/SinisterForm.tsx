import { FIELD_VALIDATION_ERROR_MESSAGE, FIELD_VALIDATORS } from "@constants";
import { ReportFormProps } from "@types";
import { Crop, EventSinister, getCropYears } from "@utils";
import { DatePicker, Input, Select } from "brainui";
import moment, { Moment } from "moment";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON>rrorInfo,
  FieldWrapper,
  ThreeColumns,
  TwoColumns,
  Wrapper,
} from "./SinisterForm.styles";

function SinisterForm({ isSubmitted, data, onChange }: Readonly<ReportFormProps>) {
  const { t } = useTranslation();
  const [cropYears, setCropYears] = useState<
    {
      label: string;
      value: string;
    }[]
  >([]);

  const EVENT_TYPES = [
    {
      value: EventSinister.SECA,
      label: "Seca",
      ["data-testid"]: `option-select-crop-${EventSinister.SECA}`,
    },
  ];
  const OPTIONS_SELECT_CROP = Object.values(Crop).map((crop) => ({
    value: crop,
    label: t(crop),
    ["data-testid"]: `option-select-crop-${crop}`,
  }));

  useEffect(() => {
    setCropYears(getCropYears());
  }, []);

  const handleFieldChange = (fieldValue, field: string) => {
    const newValue = {
      ...data,
      data: {
        ...(data.data || {}),
        [field]: fieldValue,
      },
    };
    onChange?.(newValue);
  };

  const getErrorMessage = (field: string, value?: number | string): string =>
    FIELD_VALIDATION_ERROR_MESSAGE[field].replace("{value}", value);

  const isFieldValid = (field: string): boolean =>
    FIELD_VALIDATORS[field].every((v) =>
      Object.keys(v).every((key) => {
        const fieldValue = (data.data || {})[field] || "";
        return key === "min"
          ? fieldValue.length >= v[key]
          : key === "eq"
          ? fieldValue.length === v[key]
          : true;
      })
    );

  const renderInputField = (
    testId: string,
    labelKey: string,
    field: string,
    maxLength: number,
    type: string = "text",
    isNumber: boolean = false
  ) => (
    <FieldWrapper>
      <Input
        className="label-input"
        data-testid={testId}
        label={t(labelKey)}
        value={data.data?.[field]}
        onChange={({ target }) =>
          handleFieldChange(
            isNumber ? target.value.slice(0, maxLength) : target.value,
            field
          )
        }
        status={isSubmitted && !isFieldValid(field) ? "error" : ""}
        maxLength={maxLength}
        disabled={!data.contact}
        type={type}
      />
      {isSubmitted && !isFieldValid(field) && (
        <ErrorInfo>{getErrorMessage("min", 3)}</ErrorInfo>
      )}
    </FieldWrapper>
  );

  const renderDatePicker = (
    labelKey: string,
    field: string,
    testId: string
  ) => (
    <DatePicker
      className="date-picker"
      label={t(labelKey)}
      format="DD/MM/YYYY"
      placeholder={t("SELECT")}
      value={
        data.data?.[field]
          ? moment(data.data[field], "YYYY-MM-DD")
          : undefined
      }
      onChange={(date: Moment) =>
        handleFieldChange(date?.format("YYYY-MM-DD"), field)
      }
      status={isSubmitted && !data.data?.[field] ? "error" : ""}
      disabled={!data.contact}
      data-testid={testId}
    />
  );

  return (
    <Wrapper data-testid="pb-t-sinister-form">
      <TwoColumns>
        {renderInputField(
          "input-financial-institution",
          "FINANCIAL_INSTITUTION",
          "financialInstitution",
          256
        )}
        {renderInputField(
          "input-institution-officer",
          "INSTITUTION_OFFICER",
          "institutionResponsible",
          256
        )}
      </TwoColumns>
      <TwoColumns>
        {renderInputField(
          "input-policy-number",
          "POLICY_NUMBER",
          "policyNumber",
          15
        )}
        <Select
          data-testid="select-crop-year"
          onSelect={(cropYear: string) => handleFieldChange(cropYear, "cropYear")}
          label={t("CROP_YEAR")}
          value={data.data?.cropYear}
          status={isSubmitted && !data.data?.cropYear ? "error" : ""}
          options={cropYears.map((item) => ({
            ...item,
            ["data-testid"]: `option-select-crop-year-${item.value}`,
          }))}
          disabled={!data.contact}
        />
      </TwoColumns>
      <ThreeColumns>
        <Select
          defaultValue={data.crop}
          data-testid="select-crop"
          label={t("CROP")}
          value={data.data?.crop}
          options={OPTIONS_SELECT_CROP}
          onSelect={(selectedCrop: string) =>
            handleFieldChange(selectedCrop, "crop")
          }
          status={isSubmitted && !data.data?.crop ? "error" : ""}
          disabled={!data.contact}
        />
        {renderInputField(
          "input-total-area",
          "TOTAL_AREA",
          "totalArea",
          11,
          "number",
          true
        )}
        <Select
          data-testid="select-event"
          onSelect={(event: string) => handleFieldChange(event, "event")}
          label={t("EVENT")}
          value={data.data?.event}
          status={isSubmitted && !data.data?.event ? "error" : ""}
          options={EVENT_TYPES}
          disabled={!data.data?.cropYear || !data.data?.crop || !data.contact}
        />
      </ThreeColumns>
      <ThreeColumns>
        {renderDatePicker("SEEDING_DATE", "seedingDate", "date-picker-seeding")}
        {renderDatePicker(
          "OCCURRENCE_DATE",
          "occurrenceDate",
          "date-picker-occurrence"
        )}
        {renderDatePicker("NOTICE_DATE", "noticeDate", "date-picker-notice")}
      </ThreeColumns>
    </Wrapper>
  );
}

export default SinisterForm;
