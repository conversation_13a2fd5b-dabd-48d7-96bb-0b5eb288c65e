import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import ProtocolEudrCecafeForm from "./ProtocolEudrCecafeForm";

afterEach(() => {
  cleanup();
});

describe("ProtocolEudrCecafeForm component", () => {
  it("should render the component", () => {
    render(
      <ProtocolEudrCecafeForm
        data={{}}
        isSubmitted={true}
        onChange={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-protocol-eudr-cecafe");
    expect(component).toBeInTheDocument();
  });
});
