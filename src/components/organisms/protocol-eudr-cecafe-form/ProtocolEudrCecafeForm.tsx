import { ReportFormProps } from "@types";
import { Select } from "brainui";
import { useTranslation } from "react-i18next";
import { Content, TwoCols } from "./ProtocolEudrCecafeForm.styles";
import { useEffect } from "react";

function ProtocolEudrCecafeForm({
  isSubmitted,
  data,
  onChange,
}: ReportFormProps) {
  const { t } = useTranslation();

  useEffect(() => {
    const newValue = {
      ...(data.data || {}),
      language: "pt_BR",
    };
    onChange && onChange(newValue);
  }, []);

  return (
    <Content data-testid="pb-t-protocol-eudr-cecafe">
      {/* <TwoCols>
        <Select
          disabled={true}
          defaultValue={null}
          label={t("LANGUAGE")}
          value={data.data?.language}
          options={[
            {
              value: "en",
              label: t("ENGLISH"),
            },
            {
              value: "pt_BR",
              label: t("PORTUGUESE"),
            },
          ]}
          onSelect={(language: string) => {
            const newValue = {
              ...data,
              data: {
                ...(data.data || {}),
                language,
              },
            };
            onChange && onChange(newValue);
          }}
          status={isSubmitted && !data.data?.language ? "error" : ""}
        />
        <div></div>
      </TwoCols> */}
    </Content>
  );
}

export default ProtocolEudrCecafeForm;
