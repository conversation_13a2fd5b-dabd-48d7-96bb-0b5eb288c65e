import { Radio } from "antd";
import { Content, Wrapper } from "./CertificationRenovaBioStepOne.styles";
import { <PERSON><PERSON> } from "brainui";
import { MODEL_BY_REPORT_TYPE } from "@constants";
import { ReportType } from "@types";
import { RemovableListFile } from "@/components/molecules";
import { useTranslation } from "react-i18next";
import { useCertificationRenovaBioForm } from "@/hooks/use-certification-renova-bio-form";

export function CertificationRenovaBioFormStepOne() {
  const { t } = useTranslation();
  const {
    handleSetReportType,
    handleUploadFile,
    documentsData,
    reportType,
    handleRemoveFile,
  } = useCertificationRenovaBioForm();

  return (
    <Wrapper>
      <Content>
        <p>{t("REPORT_TYPE_OPTION")}</p>
        <Radio.Group
          size="small"
          options={[
            {
              label: t("RENOVABIO_CERTIFICATION_OPTION_LABEL"),
              value: ReportType.RENOVABIO_CERTIFICATION,
            },
            {
              label: t("RENOVABIO_MONITORING_OPTIONAL_LABEL"),
              value: ReportType.RENOVABIO_MONITORING,
            },
          ]}
          value={reportType}
          onChange={({ target }) => {
            handleSetReportType(target.value);
          }}
        />

        <section>
          <h2>{t("UPLOAD_DOCUMENTS_FILE")}</h2>
          <p className="text-description-upload-csv">
            {t("INFO_UPLOAD_CSV_RENOVA_BIO")}
          </p>
        </section>

        <a
          href={MODEL_BY_REPORT_TYPE[reportType]}
          download
          target="_blank"
          className="link-download-csv"
        >
          {t("DOWNLOAD_SAMPLE_CSV")}
        </a>

        <Dragger
          uploadProps={{
            name: "file",
            showUploadList: false,
            accept: ".csv,.txt",
            beforeUpload: () => {
              return false;
            },
          }}
          onChange={(info) => {
            handleUploadFile(info.file as any as File);
          }}
          onDelete={() => {}}
        />
        {documentsData.fileMetadata.name && (
          <RemovableListFile
            fileDetails={"Detalhes"}
            filename={documentsData.fileMetadata.name}
            onRemove={handleRemoveFile}
            index={1}
          />
        )}
      </Content>
    </Wrapper>
  );
}
