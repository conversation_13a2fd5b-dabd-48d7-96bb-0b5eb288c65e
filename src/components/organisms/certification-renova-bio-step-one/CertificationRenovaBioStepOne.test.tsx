import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { CertificationRenovaBioFormStepOne } from "./CertificationRenovaBioStepOne";
import { useTranslation } from "react-i18next";
import { useCertificationRenovaBioForm } from "../../../hooks/use-certification-renova-bio-form";

jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(),
}));

jest.mock("@/hooks/use-certification-renova-bio-form", () => ({
  useCertificationRenovaBioForm: jest.fn(),
}));

describe("CertificationRenovaBioFormStepOne", () => {
  const t = jest.fn((key) => key);
  const handleSetReportType = jest.fn();
  const handleUploadFile = jest.fn();
  const handleRemoveFile = jest.fn();
  const documentsData = { fileMetadata: { name: "" } };
  const reportType = "RENOVABIO_CERTIFICATION";

  beforeEach(() => {
    (useTranslation as jest.Mock).mockReturnValue({ t });
    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      handleSetReportType,
      handleUploadFile,
      documentsData,
      reportType,
      handleRemoveFile,
    });
  });

  it("renders the component", () => {
    render(<CertificationRenovaBioFormStepOne />);
    expect(screen.getByText("REPORT_TYPE_OPTION")).toBeInTheDocument();
  });

  it("renders radio buttons with correct labels", () => {
    render(<CertificationRenovaBioFormStepOne />);
    expect(
      screen.getByLabelText("RENOVABIO_CERTIFICATION_OPTION_LABEL")
    ).toBeInTheDocument();
    expect(
      screen.getByLabelText("RENOVABIO_MONITORING_OPTIONAL_LABEL")
    ).toBeInTheDocument();
  });

  it("calls handleSetReportType on radio button change", () => {
    render(<CertificationRenovaBioFormStepOne />);
    fireEvent.click(
      screen.getByLabelText("RENOVABIO_MONITORING_OPTIONAL_LABEL")
    );
    expect(handleSetReportType).toHaveBeenCalledWith("RENOVABIO_MONITORING");
  });

  it("renders the download link with correct href", () => {
    render(<CertificationRenovaBioFormStepOne />);
    expect(
      screen.getByText("DOWNLOAD_SAMPLE_CSV").closest("a")
    ).toHaveAttribute("href", expect.any(String));
  });

  it("renders RemovableListFile when documentsData has a file", () => {
    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      handleSetReportType,
      handleUploadFile,
      documentsData: { fileMetadata: { name: "example.csv" } },
      reportType,
      handleRemoveFile,
    });
    render(<CertificationRenovaBioFormStepOne />);
    expect(screen.getByText("example.csv")).toBeInTheDocument();
  });

  it("calls handleRemoveFile when RemovableListFile remove button is clicked", () => {
    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      handleSetReportType,
      handleUploadFile,
      documentsData: { fileMetadata: { name: "example.csv" } },
      reportType,
      handleRemoveFile,
    });
    render(<CertificationRenovaBioFormStepOne />);
    fireEvent.click(screen.getByTestId("pb-t-removable-list-file-button"));
    expect(handleRemoveFile).toHaveBeenCalled();
  });

  it("should handle with upload when input file receives a file", () => {
    const handleUploadFile = jest.fn();
    (useCertificationRenovaBioForm as jest.Mock).mockReturnValue({
      handleSetReportType,
      handleUploadFile,
      documentsData: { fileMetadata: { name: "example.csv" } },
      reportType,
      handleRemoveFile,
    });
    const { baseElement } = render(<CertificationRenovaBioFormStepOne />);
    const file = new File([""], "example.csv", { type: "text/csv" });
    const element = baseElement.querySelector("input[type=file]");
    if (element) {
      fireEvent.change(element, {
        target: { files: [file] },
      });
    }
    expect(handleRemoveFile).toHaveBeenCalled();
  });
});
