import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: auto;
  gap: 32px;
  margin: 16px 0;

  p {
    margin: 0;
  }

  h1 {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  h2 {
    font-size: 16px;
    font-weight: 500;
  }

  .link-download-csv {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40%;
    border: 1px solid;
    border-radius: 4px;
    padding: 2px;
    border-color: #00b277;
    color: #00b277;
    transition: opacity 0.3s;

    &:hover {
      cursor: pointer;
      opacity: 0.7;
    }
  }

  .text-description-upload-csv {
    color: rgb(149, 149, 149);
  }
`;
