import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import HeaderNavTabs from "./HeaderNavTabs";

afterEach(() => {
  cleanup();
});

describe("HeaderNavTabs component", () => {
  const headerTabs = [{ label: "Tab 1", tabKey: 1, selected: true }];
  it("should render the component", () => {
    const handleClickTabButton = jest.fn();
    const { getByTestId } = render(
      <HeaderNavTabs tabs={headerTabs} onChange={handleClickTabButton} />
    );

    const component = screen.queryByTestId("pb-t-nav-tabs");
    const headerNavButton = getByTestId("pb-t-header-nav-btn").querySelector(
      "div"
    );
    fireEvent.click(headerNavButton!);
    expect(handleClickTabButton).toHaveBeenCalled();
    expect(component).toBeInTheDocument();
  });
});
