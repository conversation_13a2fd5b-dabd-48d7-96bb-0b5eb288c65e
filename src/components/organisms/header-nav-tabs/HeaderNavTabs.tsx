import { HeaderNavButton } from "@components";

interface HeaderNavTabsProps {
  tabs: { label: string; tabKey: number; selected: boolean }[];
  onChange?: (tabKey: number) => void;
}

function HeaderNavTabs({ tabs, onChange }: HeaderNavTabsProps) {
  return (
    <div
      data-testid="pb-t-nav-tabs"
      className="ant-tabs-nav-list"
      style={{ textAlign: "center", width: "calc(100% - 240px)" }}
    >
      {tabs.map((tab) => (
        <HeaderNavButton
          key={tab.tabKey}
          tabKey={tab.tabKey}
          label={tab.label}
          selected={tab.selected}
          onClick={() => {
            onChange && onChange(tab.tabKey);
          }}
        />
      ))}
      <div
        className="ant-tabs-ink-bar ant-tabs-ink-bar-animated"
        style={{ left: "0px", width: "121px" }}
      ></div>
    </div>
  );
}

export default HeaderNavTabs;
