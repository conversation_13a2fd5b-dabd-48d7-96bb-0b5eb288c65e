import { useCertification2BSvs } from "@/hooks/use-certification-2bsvs";
import { Certification2BSvsFormStep } from "@/store/modules/certification-2bsvs/types";
import { ButtonsContainer, Wrapper } from "./Certification2BSvsForm.styles";
import { But<PERSON>, Steps } from "antd";
import { useTranslation } from "react-i18next";
import { Certification2BSvsFormStepOne } from "../certification-2bsvs-form-step-one/Certification2bsvsFormStepOne";
import { Certification2BSvsFormStepTwo } from "../certification-2bsvs-form-step-two/Certification2bsvsFormStepTwo";

export const Certification2BSvsForm: React.FC = () => {
  const {
    data,
    handleSetFormStep,
    handleClickCancel,
    handleClickRequestReport,
    loading,
  } = useCertification2BSvs();

  const { t } = useTranslation();

  const currentStep = {
    [Certification2BSvsFormStep.UPLOAD_FILE]: 0,
    [Certification2BSvsFormStep.CAR_VALIDATION]: 1,
  };

  return (
    <Wrapper>
      <Steps
        current={currentStep[data.formStep]}
        items={Object.values(Certification2BSvsFormStep).map(() => ({}))}
        className="empty-title-steps"
      />

      <h3>Envie sua lista de documentos.</h3>

      {data.formStep === Certification2BSvsFormStep.UPLOAD_FILE && (
        <Certification2BSvsFormStepOne />
      )}

      {data.formStep === Certification2BSvsFormStep.CAR_VALIDATION && (
        <Certification2BSvsFormStepTwo />
      )}

      <ButtonsContainer>
        <Button data-testid="button-cancel" onClick={handleClickCancel}>
          {t("CANCEL")}
        </Button>

        {data.formStep === Certification2BSvsFormStep.UPLOAD_FILE && (
          <Button
            type="primary"
            id="button-submit"
            data-testid="button-next-form"
            disabled={data.validCars.length === 0}
            onClick={() => {
              handleSetFormStep(Certification2BSvsFormStep.CAR_VALIDATION);
            }}
          >
            {t("NEXT")}
          </Button>
        )}

        {data.formStep === Certification2BSvsFormStep.CAR_VALIDATION &&
          data.validCars.length && (
            <Button
              type="primary"
              id="button-submit"
              data-testid="button-submit"
              disabled={loading}
              onClick={handleClickRequestReport}
            >
              {t("REQUEST_REPORT")}
            </Button>
          )}
      </ButtonsContainer>
    </Wrapper>
  );
};
