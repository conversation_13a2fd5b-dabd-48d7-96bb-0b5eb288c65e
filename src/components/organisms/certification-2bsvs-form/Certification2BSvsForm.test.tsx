import { fireEvent, render } from "@testing-library/react";
import { Certification2BSvsForm } from "./Certification2BSvsForm";
import { useCertification2BSvs } from "@/hooks/use-certification-2bsvs";
import { Certification2BSvsFormStep } from "@/store/modules/certification-2bsvs/types";

jest.mock("@/hooks/use-certification-2bsvs", () => ({
  useCertification2BSvs: jest.fn(),
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

describe("Certification2BSvsForm", () => {
  beforeEach(() => {
    (useCertification2BSvs as jest.Mock).mockReturnValue({
      data: {
        fileMetadata: { name: "", type: "" },
        formStep: Certification2BSvsFormStep.UPLOAD_FILE,
        validCars: ["valid-car"],
        invalidCars: [],
        handleSetFormStep: jest.fn(),
        handleClickCancel: jest.fn(),
        handleClickRequestReport: jest.fn(),
        handleSetLanguage: jest.fn(),
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the first step correctly", () => {
    const { getByTestId } = render(<Certification2BSvsForm />);
    expect(getByTestId("button-cancel")).toBeInTheDocument();
    expect(getByTestId("button-next-form")).toBeInTheDocument();
  });

  it("renders the second step correctly", () => {
    (useCertification2BSvs as jest.Mock).mockReturnValueOnce({
      ...useCertification2BSvs(),
      data: {
        ...useCertification2BSvs().data,
        formStep: Certification2BSvsFormStep.CAR_VALIDATION,
        validCars: ["valid-car"],
        fileMetadata: { name: "file-name", type: "type" },
      },
    });

    const { getByTestId } = render(<Certification2BSvsForm />);
    expect(getByTestId("button-cancel")).toBeInTheDocument();
    expect(getByTestId("button-submit")).toBeInTheDocument();
  });

  it("calls handleClickCancel when cancel button is clicked", () => {
    const handleClickCancel = jest.fn();
    (useCertification2BSvs as jest.Mock).mockReturnValueOnce({
      ...useCertification2BSvs(),
      handleClickCancel,
    });
    const { getByTestId } = render(<Certification2BSvsForm />);
    fireEvent.click(getByTestId("button-cancel"));
    expect(handleClickCancel).toHaveBeenCalled();
  });

  it("calls handleSetFormStep when next button is clicked in the first step", () => {
    const handleSetFormStep = jest.fn();

    (useCertification2BSvs as jest.Mock).mockReturnValueOnce({
      ...useCertification2BSvs(),
      data: {
        ...useCertification2BSvs().data,
        fileMetadata: { name: "file_name", type: "pdf" },
      },
      handleSetFormStep,
    });

    const { getByTestId } = render(<Certification2BSvsForm />);
    const button = getByTestId("button-next-form");
    fireEvent.click(button);

    expect(handleSetFormStep).toHaveBeenCalledWith(
      Certification2BSvsFormStep.CAR_VALIDATION
    );
  });

  it("calls handleClickRequestReport when request report button is clicked in the second step", () => {
    const handleClickRequestReport = jest.fn();
    (useCertification2BSvs as jest.Mock).mockReturnValueOnce({
      ...useCertification2BSvs(),
      data: {
        ...useCertification2BSvs().data,
        formStep: Certification2BSvsFormStep.CAR_VALIDATION,
      },
      handleClickRequestReport,
    });

    const { getByTestId } = render(<Certification2BSvsForm />);
    fireEvent.click(getByTestId("button-submit"));
    expect(handleClickRequestReport).toHaveBeenCalled();
  });
});
