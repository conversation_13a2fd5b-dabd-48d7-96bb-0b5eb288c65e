import { <PERSON>ert, Radio } from "antd";
import {
  Content,
  Container,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lighted<PERSON><PERSON><PERSON>,
  <PERSON>aggerContainer,
} from "./PortifolioDiagnosisFormBatch.styles";
import { useTranslation } from "react-i18next";
import { usePortifolioDiagnosis } from "@/hooks/use-portifolio-diagnosis";
import { MODEL_BY_REPORT_TYPE } from "@constants";
import <PERSON>agger from "antd/lib/upload";
import { icon_cloud_upload } from "@/assets";
import { ReportPortifolioDiagnosisBatchDocumentType } from "@/store/modules/portifolio-diagnosis/types";
import { SectionBatchFileImported } from "@/components/atoms/section-batch-file-imported/SectionBatchFileImported";
import { TableInvalidDocuments } from "@/components/atoms/table-invalid-documents/TableInvalidDocuments";
import { useState } from "react";
import { FormListItem, RemovableListFile } from "@/components/molecules";
import { MultiSelect } from "@/components/atoms/multi-select/MultiSelect";

export function getSubModulesSelectData(subModulesAvailable: Array<{ description: string, report_type: string }>) {
  const nameMapping: Record<string, string> = {
    "Fundiário": "Ambiental",
    "Crédito": "Crédito",
    "Agrícola": "Agrícola",
    "ESG": "ESG"
  };

  const result = subModulesAvailable.map(
    ({ description, report_type }) => ({
      label: nameMapping[description] || description,
      value: report_type,
    })
  );

  return result;
}

export const PortifolioDiagnosisFormBatch = () => {
  const [isToggleDocumentsInvalidsOpen, setIsToggleDocumentsInvalidsOpen] =
    useState(false);
  const { t } = useTranslation();
  const {
    data,
    handleUploadBatchFile,
    handleSetBatchDocumentType,
    handleClickRemoveBatchFile,
  } = usePortifolioDiagnosis();

  const hasUploadedFile =
    data.batch.documentsList.length > 0 ||
    data.batch.invalidDocuments.length > 0;

  function getRemovableFileDetails() {
    const { documentsList, invalidDocuments } = data.batch;
    const totalLines = documentsList.length + invalidDocuments.length;
    if (totalLines !== 1) {
      return `${totalLines} ${t("ROWS")}`;
    }
    return `${totalLines} ${t("ROW")}`;
  }

  // Usamos a função exportada, passando os dados necessários
  const subModulesSelectData = getSubModulesSelectData(data.batch.subModulesAvailable);

  return (
    <Container>
      <Content>
        <FormListItem>
          <p className="p-title">{t("INTEREST_AREA")}</p>

          <Radio.Group
            size="small"
            className="radio-select-portifolio-diagnosis-data"
            data-testid="radio-group-batch-choose-document-type"
            defaultValue={ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ}
            onChange={(event) => {
              handleSetBatchDocumentType(
                event.target.value as ReportPortifolioDiagnosisBatchDocumentType
              );
            }}
          >
            <Radio.Button
              data-testid="radio-button-cpf-cnpj"
              value={ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ}
              checked={
                data.batch.documentType ===
                ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ
              }
            >
              CPF/CNPJ
            </Radio.Button>
            <Radio.Button
              data-testid="radio-button-car"
              value={ReportPortifolioDiagnosisBatchDocumentType.CAR}
              checked={
                data.batch.documentType ===
                ReportPortifolioDiagnosisBatchDocumentType.CAR
              }
            >
              {t("CAR_CODE")}
            </Radio.Button>
          </Radio.Group>
        </FormListItem>

        <div className="divider" />

        <FormListItem>
          <Content className="div-content-select-modules">
            <p className="p-title">{t("Módulos")}</p>
            <MultiSelect
              options={subModulesSelectData}
              defaultValue={subModulesSelectData.map(({ value }) => value)}
              valuesSelected={subModulesSelectData.map(
                ({ value }) => value
              )}
              onChange={() => {}}
              placeholder={t("PORTIFOLIO_DIAGNOSIS_SELECT_MODULE_PLACEHOLDER")}
            />
          </Content>
        </FormListItem>

        <div className="divider" />

        {!data.batch.fileDetails.name && (
          <>
            <section>{t("INFO_UPLOAD_CSV_PORTIFOLIO_DIAGNOSIS")}</section>

            <a
              href={
                MODEL_BY_REPORT_TYPE[
                  `${data.reportType}_${data.batch.documentType}`
                ]
              }
              download
              target="_blank"
              className="link-download-csv"
            >
              {t("DOWNLOAD_SAMPLE_CSV")}
            </a>
          </>
        )}

        {!hasUploadedFile && (
          <DraggerContainer>
            <Dragger
              beforeUpload={(file) => {
                handleUploadBatchFile(file);
                return false;
              }}
              customRequest={() => {}}
              fileList={[]}
              accept=".csv"
              className="dragger-report-portifolio-diagnosis"
              data-testid="dragger-portifolio-diagnosis-batch"
            >
              <DraggerWrapper>
                <DraggerText className="ant-upload-text">
                  {t("CHOOSE_FILES")}
                </DraggerText>
                <DraggerHint className="ant-upload-hint">
                  {`${t("DRAG_DROP_OR")}\n`}
                  <HighlightedText>
                    {t("CLICK_TO_UPLOAD_FROM_FILES")}
                  </HighlightedText>
                  .
                </DraggerHint>
                <img
                  src={icon_cloud_upload}
                  alt="Upload file"
                  width="24"
                  height="24"
                />
              </DraggerWrapper>
            </Dragger>
          </DraggerContainer>
        )}
        {hasUploadedFile && (
          <RemovableListFile
            fileDetails={getRemovableFileDetails()}
            filename={data.batch.fileDetails.name.toLowerCase()}
            index={1}
            onRemove={() => {
              handleClickRemoveBatchFile();
            }}
          />
        )}

        {hasUploadedFile && (
          <SectionBatchFileImported
            documentsList={data.batch.documentsList}
            invalidDocumentsList={data.batch.invalidDocuments}
          />
        )}

        {data.batch.invalidDocuments.length > 0 && (
          <Alert message={t("INVALID_ROWS_DESCRIPTION")} type="warning" />
        )}

        {data.batch.invalidDocuments.length > 0 && (
          <TableInvalidDocuments
            containerClassname="table-invalid-documents-container"
            invalidDocumentsList={data.batch.invalidDocuments}
            isExpanded={isToggleDocumentsInvalidsOpen}
            title="Documentos Inválidos"
            onClickToggleExpand={() =>
              setIsToggleDocumentsInvalidsOpen(!isToggleDocumentsInvalidsOpen)
            }
          />
        )}
      </Content>
    </Container>
  );
};
