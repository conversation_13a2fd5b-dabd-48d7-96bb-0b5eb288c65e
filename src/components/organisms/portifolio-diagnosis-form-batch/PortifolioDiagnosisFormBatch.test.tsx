import { render, fireEvent } from "@testing-library/react";
import { PortifolioDiagnosisFormBatch, getSubModulesSelectData } from "./PortifolioDiagnosisFormBatch";
import { usePortifolioDiagnosis } from "@/hooks/use-portifolio-diagnosis";
import { ReportPortifolioDiagnosisBatchDocumentType } from "../../../store/modules/portifolio-diagnosis/types";
import { ReportType, RequestMethod } from "@types";

jest.mock("@/hooks/use-portifolio-diagnosis");

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("PortifolioDiagnosisFormBatch", () => {
  it("should call handleUploadBatchFile when a file is uploaded", () => {
    const handleUploadFile = jest.fn();
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [],
          invalidDocuments: [],
          subModulesAvailable: [],
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
      },
      handleUploadBatchFile: handleUploadFile,
    });
    const { getByTestId } = render(<PortifolioDiagnosisFormBatch />);
    const uploadInput = getByTestId("dragger-portifolio-diagnosis-batch");

    const file = new File(["content"], "test.csv", { type: "text/csv" });
    if (uploadInput) {
      fireEvent.change(uploadInput, { target: { files: [file] } });
    }

    expect(handleUploadFile).toHaveBeenCalledWith(file);
  });

  it("should render the component", () => {
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [],
          invalidDocuments: [],
          subModulesAvailable: [],
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
      },
      handleUploadBatchFile: jest.fn(),
    });
    const { getByText } = render(<PortifolioDiagnosisFormBatch />);
    expect(getByText("INTEREST_AREA")).toBeInTheDocument();
  });

  it("should display the uploaded file details", () => {
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [{ name: "doc1" }],
          invalidDocuments: [],
          subModulesAvailable: [],
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile: jest.fn(),
    });

    const { getByText } = render(<PortifolioDiagnosisFormBatch />);
    expect(getByText("1 ROW")).toBeInTheDocument();
  });

  it("should call handleClickRemoveBatchFile when the remove button is clicked", () => {
    const handleClickRemoveBatchFile = jest.fn();
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [{ name: "doc1" }],
          invalidDocuments: [],
          subModulesAvailable: [],
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile,
    });

    const { getByTestId } = render(<PortifolioDiagnosisFormBatch />);
    const removeButton = getByTestId("pb-t-removable-list-file-button");

    fireEvent.click(removeButton);
    expect(handleClickRemoveBatchFile).toHaveBeenCalled();
  });

  it("should render the correct text if document list has more than one item", () => {
    const handleClickRemoveBatchFile = jest.fn();

    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [{ name: "doc1" }, { name: "doc1" }],
          invalidDocuments: [],
          subModulesAvailable: [],
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile,
    });

    const { getByText } = render(<PortifolioDiagnosisFormBatch />);
    const expected = getByText("2 ROWS");

    expect(expected).toBeInTheDocument();
  });

  it("should call handleChangeDocumentType if radio input changes", () => {
    const handleSetBatchDocumentType = jest.fn();
    const documentType = ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ;
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [],
          invalidDocuments: [],
          subModulesAvailable: [],
          documentType,
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType,
      handleClickRemoveBatchFile: jest.fn(),
    });
    const { getByTestId } = render(<PortifolioDiagnosisFormBatch />);
    const input = getByTestId("radio-button-car");
    fireEvent.click(input);
    expect(input).toBeInTheDocument();
    expect(handleSetBatchDocumentType).toHaveBeenCalled();
  });

  it("should toggle up table invalid documents", () => {
    (usePortifolioDiagnosis as jest.Mock).mockReturnValue({
      data: {
        batch: {
          documentsList: [],
          subModulesAvailable: [],
          invalidDocuments: ["invalid1"],
          documentType: ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ,
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile: jest.fn(),
    });

    const { getByTestId } = render(<PortifolioDiagnosisFormBatch />);

    const button = getByTestId("button-click-table-toggle-invalid-documents");
    expect(button).toBeInTheDocument();

    const iconArrowUp = getByTestId("icon-arrowup-table-invalid-documents");
    expect(iconArrowUp).toBeInTheDocument();

    fireEvent.click(button);
    const iconArrowDown = getByTestId("icon-arrowdown-table-invalid-documents");

    expect(iconArrowDown).toBeInTheDocument();
  });

  it("should render multiselect with sub-modules", () => {
    const subModulesAvailable = [
      { report_type: "module1", description: "module1" },
      { report_type: "module2", description: "module2" },
    ];
    (usePortifolioDiagnosis as jest.Mock).mockReturnValue({
      ...usePortifolioDiagnosis(),
      data: {
        ...usePortifolioDiagnosis().data,
        requestMethod: RequestMethod.BATCH,
        batch: {
          documentsList: [],
          invalidDocuments: [],
          subModulesAvailable: subModulesAvailable,
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
      },
    });
    const { getByTestId, getByText } = render(<PortifolioDiagnosisFormBatch />);

    const select = getByTestId("select-multi-select");

    fireEvent.mouseDown(select);

    const element = getByText(subModulesAvailable[0].description);

    expect(element).toBeInTheDocument();
  });

  it("should map module names correctly in getSubModulesSelectData", () => {
    const subModulesAvailable = [
      { report_type: "Fundiário", description: "Agricola" },
      { report_type: "Crédito", description: "Crédito" },
      { report_type: "Agrícola", description: "Agrícola" },
      { report_type: "ESG", description: "ESG" },
    ];
    
    (usePortifolioDiagnosis as jest.Mock).mockReturnValue({
      data: {
        batch: {
          documentsList: [],
          invalidDocuments: [],
          subModulesAvailable: subModulesAvailable,
          documentType: "CPF/CNPJ",
          fileDetails: { name: "test.csv" },
        },
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile: jest.fn(),
    });
    
    const { getByText, getByTestId } = render(<PortifolioDiagnosisFormBatch />);
    
    const select = getByTestId("select-multi-select");
    fireEvent.mouseDown(select);
    
    expect(getByText("Agricola")).toBeInTheDocument();
    expect(getByText("Crédito")).toBeInTheDocument();
    expect(getByText("Agrícola")).toBeInTheDocument();
    expect(getByText("ESG")).toBeInTheDocument();
  });

  it("should correctly map module names in getSubModulesSelectData function", () => {
    const subModulesAvailable = [
      { report_type: "Fundiário", description: "Fundiário" },
      { report_type: "Crédito", description: "Crédito" },
      { report_type: "Agrícola", description: "Agrícola" },
      { report_type: "ESG", description: "ESG" },
    ];
    
    const result = getSubModulesSelectData(subModulesAvailable);
    
    expect(result).toEqual([
      { label: "Ambiental", value: "Fundiário" },
      { label: "Crédito", value: "Crédito" },
      { label: "Agrícola", value: "Agrícola" },
      { label: "ESG", value: "ESG" },
    ]);
  });
});
