import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  .p-title {
    color: rgb(149, 149, 149);
    margin-bottom: 16px;
  }

  .divider {
    margin: 8px 0;
  }

  .dragger-report-portifolio-diagnosis {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background: rgb(247, 247, 248);
    padding: 16px 0;
    border-radius: 8px;

    &:hover {
      cursor: pointer;
    }
  }

  .table-invalid-documents-container {
    margin: 8px 0 16px 0;
  }
`;

export const DraggerContainer = styled.div`
  display: flex;
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  transition: border-color 0.2s;
  &:hover {
    border-color: #00b277;
  }
`;

export const Content = styled.div`
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  width: 100%;

  section {
    margin: 14px 0;
    color: rgb(149, 149, 149);
  }

  .link-download-csv {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40%;
    border: 1px solid;
    border-radius: 8px;
    padding: 6px 2px;
    border-color: #00b277;
    color: #00b277;
    transition: opacity 0.3s;
    margin-bottom: 12px;
    background: #fff;
    font-weight: bold;

    &:hover {
      cursor: pointer;
      opacity: 0.7;
    }
  }
`;

export const DraggerWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  padding: 8px;
  width: 100%;
`;

export const DraggerText = styled.p`
  max-width: 26ch;
  text-align: center;
  margin: 0 auto !important;
  line-height: 1.25;
`;

export const DraggerHint = styled.p`
  max-width: 50ch;
  text-align: center;
  margin: 0 auto !important;
  line-height: 1.25;
  white-space: pre-line;
`;

export const HighlightedText = styled.span`
  color: #10a488;
`;
