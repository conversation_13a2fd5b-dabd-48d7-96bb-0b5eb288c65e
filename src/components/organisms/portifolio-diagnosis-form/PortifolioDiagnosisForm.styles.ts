import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  height: 100%;

  .radio-group-select-request-method {
    margin-bottom: 16px;
  }

  .radio-select-portifolio-diagnosis-data {
    .ant-radio-button-wrapper-checked {
      background: rgba(44, 183, 122, 0.1);
    }
  }
`;

export const ButtonsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  width: 100%;
`;
