import { <PERSON>ton, Radio, CheckboxOptionType } from "antd";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontaine<PERSON> } from "./PortifolioDiagnosisForm.styles";
import { ReportType, RequestMethod } from "@types";
import { useTranslation } from "react-i18next";
import { PortifolioDiagnosisFormBatch } from "../portifolio-diagnosis-form-batch/PortifolioDiagnosisFormBatch";
import { usePortifolioDiagnosis } from "@/hooks/use-portifolio-diagnosis";
import { PortifolioDiagnosisFormManually } from "../portifolio-diagnosis-form-mannually/PortiflioDiagnosisFormManually";
import { useMemo } from "react";

export function PortifolioDiagnosisForm() {
  const { t } = useTranslation();
  const {
    handleSetRequestMethod,
    data,
    isLoading,
    handleClickCancel,
    handleClickRequestReport,
    isManualFormEnabled,
  } = usePortifolioDiagnosis();

  const REQUEST_METHODS: CheckboxOptionType[] = useMemo(() => {
    const result = [
      {
        key: RequestMethod.MANUAL,
        label: t("MANUALLY"),
        value: RequestMethod.MANUAL,
        disabled: !isManualFormEnabled,
      },
      {
        key: RequestMethod.BATCH,
        label: t("BATCH"),
        value: RequestMethod.BATCH,
        disabled: false,
      },
    ];

    return result;
  }, [isManualFormEnabled]);

  const shouldDisableSubmitButton = (): boolean => {
    if (isLoading) return true;
    if (data.requestMethod === RequestMethod.MANUAL) {
      const hasMunicipality = data.manual.municipaliciesSelected.length > 0;
      const hasState = data.manual.statesSelected.length > 0;
      return !hasMunicipality && !hasState;
    }
    if (data.requestMethod === RequestMethod.BATCH) {
      const hasDocuments = data.batch.documentsList.length > 0;
      return !hasDocuments;
    }
  };

  return (
    <Wrapper>
      <h3>{t("REQUEST_NEW_REPORT_TITLE")}</h3>
      <Radio.Group
        className="radio-group-select-request-method"
        data-testid="radio-group-select-request-method"
        size="small"
        disabled={false}
        options={REQUEST_METHODS}
        value={data.requestMethod}
        onChange={({ target }) => {
          handleSetRequestMethod(target.value);
        }}
      />
      {data.requestMethod === RequestMethod.MANUAL && (
        <PortifolioDiagnosisFormManually />
      )}

      {data.requestMethod === RequestMethod.BATCH && (
        <PortifolioDiagnosisFormBatch />
      )}

      <ButtonsContainer>
        <Button data-testid="button-cancel" onClick={handleClickCancel}>
          {t("CANCEL")}
        </Button>

        <Button
          type="primary"
          id="button-submit"
          data-testid="button-submit"
          disabled={shouldDisableSubmitButton()}
          onClick={() =>
            handleClickRequestReport(ReportType.PORTFOLIO_DIAGNOSIS)
          }
        >
          {t("REQUEST_REPORT")}
        </Button>
      </ButtonsContainer>
    </Wrapper>
  );
}
