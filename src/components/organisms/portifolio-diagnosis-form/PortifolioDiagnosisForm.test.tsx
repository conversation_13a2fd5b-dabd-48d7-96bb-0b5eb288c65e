import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { PortifolioDiagnosisForm } from "./PortifolioDiagnosisForm";
import { useTranslation } from "react-i18next";
import { usePortifolioDiagnosis } from "@/hooks/use-portifolio-diagnosis";
import { RequestMethod, ReportType } from "@/types";
import { ReportPortifolioDiagnosisBatchDocumentType } from "../../../store/modules/portifolio-diagnosis/types";
import { Resource } from "@constants";
import { useAccountDetails } from "@/hooks/use-account-details";

jest.mock("react-i18next", () => ({
  useTranslation: jest.fn(),
}));

jest.mock("@/hooks/use-portifolio-diagnosis", () => ({
  usePortifolioDiagnosis: jest.fn(),
}));

describe("PortifolioDiagnosisForm", () => {
  const t = jest.fn((key) => key);
  const handleSetRequestMethod = jest.fn();
  const handleClickCancel = jest.fn();
  const handleClickRequestReport = jest.fn();

  beforeEach(() => {
    (useTranslation as jest.Mock).mockReturnValue({ t });
    (usePortifolioDiagnosis as jest.Mock).mockReturnValue({
      handleSetRequestMethod,
      data: {
        requestMethod: RequestMethod.MANUAL,
        manual: {
          municipaliciesSelected: [],
          statesSelected: [],
          subModules: [],
          subModulesSelected: [],
        },
        batch: { documentsList: [] },
      },
      isLoading: false,
      handleClickCancel,
      handleClickRequestReport,
    });
  });

  it("renders the component", () => {
    render(<PortifolioDiagnosisForm />);
    expect(screen.getByText("REQUEST_NEW_REPORT_TITLE")).toBeInTheDocument();
  });

  it("disables the submit button when loading", () => {
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      handleSetRequestMethod,
      data: {
        requestMethod: RequestMethod.MANUAL,
        manual: { municipaliciesSelected: [], statesSelected: [] },
        batch: { documentsList: [] },
      },
      isLoading: true,
      handleClickCancel,
      handleClickRequestReport,
    });

    const { getByTestId } = render(<PortifolioDiagnosisForm />);
    expect(getByTestId("button-submit")).toBeDisabled();
  });

  it("disables the submit button when no data is selected", () => {
    const { getByTestId } = render(<PortifolioDiagnosisForm />);
    expect(getByTestId("button-submit")).toBeDisabled();
  });

  it("enables the submit button when data is selected", () => {
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      handleSetRequestMethod,
      data: {
        requestMethod: RequestMethod.BATCH,
        manual: {
          municipaliciesSelected: [],
          statesSelected: [],
        },
        batch: { documentsList: [], invalidDocumentsList: [] },
      },
      isLoading: false,
      handleClickCancel,
      handleClickRequestReport,
    });

    render(<PortifolioDiagnosisForm />);
    expect(screen.getByText("REQUEST_REPORT")).toBeEnabled();
  });

  it("calls handleClickCancel when cancel button is clicked", () => {
    render(<PortifolioDiagnosisForm />);
    fireEvent.click(screen.getByTestId("button-cancel"));
    expect(handleClickCancel).toHaveBeenCalled();
  });

  it("calls handleClickRequestReport when submit button is clicked", () => {
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      handleSetRequestMethod,
      data: {
        requestMethod: RequestMethod.MANUAL,
        manual: {
          subModules: [],
          subModulesSelected: [],
          municipaliciesSelected: ["some-municipality"],
          statesSelected: [],
        },
        batch: { documentsList: [] },
      },
      isLoading: false,
      handleClickCancel,
      handleClickRequestReport,
    });

    render(<PortifolioDiagnosisForm />);
    fireEvent.click(screen.getByText("REQUEST_REPORT"));
    expect(handleClickRequestReport).toHaveBeenCalledWith(
      ReportType.PORTFOLIO_DIAGNOSIS
    );
  });

  it("should change the report method when the radio button is clicked", () => {
    const handleSetRequestMethod = jest.fn();
    const documentType = ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ;
    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [],
          invalidDocuments: [],
          documentType,
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile: jest.fn(),
      handleSetRequestMethod,
    });
    const { getByTestId, baseElement } = render(<PortifolioDiagnosisForm />);
    const group = getByTestId("radio-group-select-request-method");
    const elements = baseElement.querySelectorAll('input[type="radio"]');
    const [_, batch] = elements as unknown as Element[];
    fireEvent.click(batch);
    expect(group).toBeInTheDocument();
    expect(handleSetRequestMethod).toHaveBeenCalled();
  });

  it(`should disable form manual if resources does not contain ${Resource.PortfolioDiagnosisCreditEnableFormManual} key`, () => {
    const handleSetRequestMethod = jest.fn();
    const documentType = ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ;

    (useAccountDetails as jest.Mock).mockReturnValueOnce({
      ...useAccountDetails(),
      resources: [],
    });

    (usePortifolioDiagnosis as jest.Mock).mockReturnValueOnce({
      data: {
        batch: {
          documentsList: [],
          invalidDocuments: [],
          documentType,
          fileDetails: { name: "test.csv" },
        },
        reportType: "reportType",
      },
      handleUploadBatchFile: jest.fn(),
      handleSetBatchDocumentType: jest.fn(),
      handleClickRemoveBatchFile: jest.fn(),
      handleSetRequestMethod,
    });
    const { getByTestId, baseElement } = render(<PortifolioDiagnosisForm />);

    const group = getByTestId("radio-group-select-request-method");

    const elements = baseElement.querySelectorAll('input[type="radio"]');
    const [manual] = elements as unknown as Element[];

    expect(group).toBeInTheDocument();
    expect(manual).toBeDisabled();
  });
});
