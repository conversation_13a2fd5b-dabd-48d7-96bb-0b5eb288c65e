import ReportFileUpload from "./ReportFileUpload";
import { IReportCSVParserResult, ReportRequest, ReportType, Tag } from "@types";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { act } from "react-dom/test-utils";
import * as services from "@services";

jest.mock("@services", () => ({
  createBatchReportRequest: jest.fn(),
  addTagToRequest: jest.fn(),
  createTags: jest.fn(),
}));

jest.mock("@/hooks/use-tags-list", () => ({
  useTagsList: () => ({
    tagsList: [
      { id: 1, name: "tag1" },
      { id: 2, name: "tag2" },
    ],
    handleListTags: jest.fn(),
  }),
}));

jest.mock("@/hooks/use-report-requests-list", () => ({
  useReportRequestsList: () => ({
    handleListReportsRequests: jest.fn(),
    pagination: { limit: 10, offset: 0 },
  }),
}));

jest.mock("@/hooks/use-account-details", () => ({
  useAccountDetails: () => ({
    handleGetDetailsAccount: jest.fn(),
  }),
}));

jest.mock("@/contexts/AnalyticsDataLayerContext", () => ({
  useAnalytics: () => ({
    sendPageViewEvent: jest.fn(),
  }),
}));

jest.mock("@contexts", () => ({
  RequestFormContext: {
    Consumer: jest.fn(),
    Provider: jest.fn(),
  },
}));

jest.mock("../file-upload-step", () => ({
  FileUploadStep: ({ onChange }) => (
    <div data-testid="mock-file-upload-step">
      <button
        data-testid="mock-upload-button"
        onClick={() => {
          const mockFile = new File(["test"], "test.csv", { type: "text/csv" });
          const mockData = {
            validRows: [{ id: 1 }, { id: 2 }],
            invalidRows: [],
          };
          onChange(mockFile, mockData);
        }}
      >
        Upload File
      </button>
    </div>
  ),
}));

jest.mock("../file-upload-verify-step/FileUploadVerifyStep", () => ({
  __esModule: true,
  default: () => <div data-testid="mock-file-upload-verify-step">Verify Step</div>,
}));

jest.mock("@/components/molecules", () => ({
  SelectTag: ({ onChange }) => (
    <div data-testid="mock-select-tag">
      <button
        data-testid="mock-select-tag-button"
        onClick={() => onChange(["tag1", "tag3"])}
      >
        Select Tags
      </button>
    </div>
  ),
}));

jest.mock("brainui", () => ({
  Button: ({ children, onClick, disabled }) => (
    <button data-testid="mock-brainui-button" onClick={onClick} disabled={disabled}>
      {children}
    </button>
  ),
}));

jest.mock("antd", () => ({
  ...jest.requireActual("antd"),
  Button: ({ children, onClick, disabled }) => (
    <button data-testid="mock-antd-button" onClick={onClick} disabled={disabled}>
      {children}
    </button>
  ),
  notification: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

jest.mock("@/components/atoms/notification-description-generic-error", () => ({
  getDescriptionGenericError: jest.fn(() => "Generic error"),
}));

jest.mock("./ReportFileUpload.styles", () => ({
  Wrapper: ({ children, ...props }) => (
    <div data-testid="pb-t-agro-report-file-upload" {...props}>
      {children}
    </div>
  ),
  Content: ({ children, ...props }) => <div {...props}>{children}</div>,
  Footer: ({ children, ...props }) => <div {...props}>{children}</div>,
  Options: ({ children, ...props }) => <div {...props}>{children}</div>,
  OptionColumn: ({ children, ...props }) => <div {...props}>{children}</div>,
}));

const mockCloseRequestForm = jest.fn();
jest.mock("react", () => ({
  ...jest.requireActual("react"),
  useContext: () => ({
    closeRequestForm: mockCloseRequestForm,
  }),
}));

describe("ReportFileUpload component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with initial state", () => {
    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    expect(screen.getByTestId("pb-t-agro-report-file-upload")).toBeInTheDocument();

    expect(screen.getByTestId("mock-file-upload-step")).toBeInTheDocument();

    const nextButton = screen.getByTestId("mock-brainui-button");
    expect(nextButton).toBeDisabled();
  });

  it("handles file upload and enables the next button", async () => {
    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    expect(nextButton).not.toBeDisabled();
  });

  it("moves to review step when next button is clicked", async () => {
    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    fireEvent.click(nextButton);

    expect(screen.getByTestId("mock-file-upload-verify-step")).toBeInTheDocument();

    const backButton = screen.getAllByTestId("mock-antd-button")[1];
    expect(backButton).toBeInTheDocument();
  });

  it("goes back to upload step when back button is clicked", async () => {
    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    fireEvent.click(nextButton);

    const backButton = screen.getAllByTestId("mock-antd-button")[1];
    fireEvent.click(backButton);

    expect(screen.getByTestId("mock-file-upload-step")).toBeInTheDocument();
  });

  it("closes the form when cancel button is clicked", async () => {
    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const cancelButton = screen.getAllByTestId("mock-antd-button")[0];
    fireEvent.click(cancelButton);

    expect(mockCloseRequestForm).toHaveBeenCalled();
  });

  it("handles tag selection", async () => {
    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const selectTagButton = screen.getByTestId("mock-select-tag-button");
    fireEvent.click(selectTagButton);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    fireEvent.click(nextButton);
  });

  it("submits the report request successfully", async () => {
    const mockReportRequest = { id: 123, name: "Test Report" };
    (services.createBatchReportRequest as jest.Mock).mockResolvedValue(mockReportRequest);
    (services.createTags as jest.Mock).mockResolvedValue([{ id: 3, name: "tag3" }]);
    (services.addTagToRequest as jest.Mock).mockResolvedValue({});

    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const selectTagButton = screen.getByTestId("mock-select-tag-button");
    fireEvent.click(selectTagButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    fireEvent.click(nextButton);

    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(services.createBatchReportRequest).toHaveBeenCalled();
      expect(services.createTags).toHaveBeenCalled();
      expect(services.addTagToRequest).toHaveBeenCalled();
      expect(mockCloseRequestForm).toHaveBeenCalled();
    });
  });

  it("handles API error when submitting report request", async () => {
    const mockError = { code: "ERR_BAD_REQUEST", response: { data: { detail: "API Error" } } };
    (services.createBatchReportRequest as jest.Mock).mockRejectedValue(mockError);

    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    fireEvent.click(nextButton);

    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(services.createBatchReportRequest).toHaveBeenCalled();
      expect(require("antd").notification.error).toHaveBeenCalledWith({
        message: "REPORTS_NOT_REQUESTED",
        description: "API Error",
      });
    });
  });

  it("handles generic API error when submitting report request", async () => {
    const mockError = { code: "UNKNOWN_ERROR" };
    (services.createBatchReportRequest as jest.Mock).mockRejectedValue(mockError);

    render(<ReportFileUpload reportType={ReportType.AGRO_CREDIT} />);

    const uploadButton = screen.getByTestId("mock-upload-button");
    fireEvent.click(uploadButton);

    const nextButton = screen.getByTestId("mock-brainui-button");
    fireEvent.click(nextButton);

    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(services.createBatchReportRequest).toHaveBeenCalled();
      expect(require("antd").notification.error).toHaveBeenCalledWith({
        message: "REPORTS_NOT_REQUESTED",
        description: "Generic error",
      });
    });
  });
});
