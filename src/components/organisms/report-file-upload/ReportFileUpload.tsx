import { SelectTag } from "@/components/molecules";
import { useAnalytics } from "@/contexts/AnalyticsDataLayerContext";
import { useAccountDetails } from "@/hooks/use-account-details";
import { useReportRequestsList } from "@/hooks/use-report-requests-list";
import { useTagsList } from "@/hooks/use-tags-list";
import { RequestFormContext } from "@contexts";
import {
  addTagToRequest,
  createBatchReportRequest,
  createTags,
} from "@services";
import { IReportCSVParserResult, ReportRequest, Tag } from "@types";
import { Button as AntdButton, notification } from "antd";
import { Button } from "brainui";
import { useContext, useState } from "react";
import { useTranslation } from "react-i18next";
import { FileUploadStep } from "../file-upload-step";
import FileUploadVerifyStep from "../file-upload-verify-step/FileUploadVerifyStep";
import { IReportFileUploadStepProps } from "./ReportFileUpload.interfaces";
import {
  Content,
  Footer,
  OptionColumn,
  Options,
  Wrapper,
} from "./ReportFileUpload.styles";
import { getDescriptionGenericError } from "@/components/atoms/notification-description-generic-error";

enum ReportFileUploadStep {
  UPLOAD = 0,
  REVIEW = 1,
}

function ReportFileUpload({ reportType }: IReportFileUploadStepProps) {
  const [data, setData] = useState<IReportCSVParserResult>();
  const [file, setFile] = useState<File>();
  const [step, setStep] = useState<ReportFileUploadStep>(
    ReportFileUploadStep.UPLOAD
  );
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const { t } = useTranslation();
  const { tagsList, handleListTags } = useTagsList();
  const { handleListReportsRequests, pagination } = useReportRequestsList();
  const { sendPageViewEvent } = useAnalytics();
  const { closeRequestForm } = useContext(RequestFormContext);
  const { handleGetDetailsAccount } = useAccountDetails();

  async function requestReports() {
    setIsSaving(true);
    const reportRequest = await requestBatch();

    if (!reportRequest) {
      setIsSaving(false);
      return;
    }

    addTagsToRequest(reportRequest);
    setIsSaving(false);
    handleClose();
    handleListReportsRequests({
      limit: pagination.limit,
      offset: 0,
    });

    sendPageViewEvent({
      pageName: `AG:LG:AgroReport:Relatorio:${reportType}`,
      ambiente: "LG",
      siteSection: "Agro Report",
      url: location.href,
    });
  }

  async function requestBatch() {
    try {
      const response = await createBatchReportRequest(file, reportType, false);
      handleGetDetailsAccount();
      notification.success({
        message:
          data.validRows.length > 1
            ? t("REPORTS_REQUESTED_SUCCESSFULLY")
            : t("REPORT_REQUESTED_SUCCESSFULLY"),
      });
      return response;
    } catch ({ code, response }) {
      let description = getDescriptionGenericError(t);
      if (code == "ERR_BAD_REQUEST") {
        description = response.data["detail"];
      }
      notification.error({
        message: t("REPORTS_NOT_REQUESTED"),
        description,
      });
    }
  }

  async function addTagsToRequest(reportRequest: ReportRequest) {
    if (reportRequest && selectedTags.length) {
      const existingSelectedTags = tagsList.filter((t) =>
        selectedTags.includes(t.name)
      );
      const newTags = selectedTags.filter(
        (t) => !tagsList.find((tag) => tag.name == t)
      );
      let currentTags: Tag[] = existingSelectedTags;
      if (newTags.length) {
        const createdTags = await createTags(newTags);
        currentTags = [...existingSelectedTags, ...createdTags];
        handleListTags();
      }
      const addTagRequests = currentTags.map((tag) =>
        addTagToRequest(reportRequest.id, tag.id)
      );
      await Promise.all(addTagRequests);
    }
  }

  function onNext() {
    if (step == ReportFileUploadStep.UPLOAD) {
      setStep(ReportFileUploadStep.REVIEW);
      return;
    }
    requestReports();
  }

  function canSubmit(): boolean {
    const hasValidRows: boolean = !!data?.validRows.length;

    const isUploadAndHasValidRows =
      step === ReportFileUploadStep.UPLOAD && hasValidRows;
    const isReviewAndHasValidRows =
      step === ReportFileUploadStep.REVIEW && hasValidRows;

    if (isSaving) return false;
    return isUploadAndHasValidRows || isReviewAndHasValidRows;
  }

  function handleClose() {
    closeRequestForm();
  }

  return (
    <Wrapper data-testid="pb-t-agro-report-file-upload">
      <Content>
        {step == ReportFileUploadStep.UPLOAD && (
          <FileUploadStep
            reportType={reportType}
            data={data}
            filename={file ? file.name : ""}
            onChange={(newFile, newData) => {
              setFile(newFile);
              setData(newData);
            }}
          />
        )}
        {step == ReportFileUploadStep.REVIEW && (
          <FileUploadVerifyStep data={data} filename={file ? file.name : ""} />
        )}
      </Content>

      <Footer>
        <SelectTag
          tags={tagsList}
          placeholder={t("TAGS_PLACEHOLDER")}
          disabled={isSaving}
          value={selectedTags}
          onChange={(tags: string[]) => {
            setSelectedTags(tags);
          }}
          optionLimit={selectedTags.length == 3}
        />

        <Options>
          <AntdButton type="ghost" disabled={isSaving} onClick={handleClose}>
            {t("CANCEL")}
          </AntdButton>
          <OptionColumn>
            {step > 0 && (
              <AntdButton
                type="ghost"
                disabled={isSaving}
                onClick={() => setStep(step - 1)}
              >
                {t("BACK")}
              </AntdButton>
            )}
            <Button type="primary" disabled={!canSubmit()} onClick={onNext}>
              {t(
                step != ReportFileUploadStep.REVIEW ? "NEXT" : "REQUEST_REPORT"
              )}
            </Button>
          </OptionColumn>
        </Options>
      </Footer>
    </Wrapper>
  );
}

export default ReportFileUpload;
