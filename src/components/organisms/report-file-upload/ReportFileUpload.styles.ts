import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: auto;
  gap: 32px;
  margin: 16px 0;

  h1 {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  h2 {
    font-size: 16px;
    font-weight: 500;
  }
`;

export const Footer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const Options = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const OptionColumn = styled.div`
  display: flex;
  gap: 5px;
`;
