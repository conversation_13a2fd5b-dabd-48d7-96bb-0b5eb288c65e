import { MODEL_BY_REPORT_TYPE } from "@constants";
import { CSV_PARSER_BY_REPORT_TYPE } from "@constants/files";
import { But<PERSON>, notification } from "antd";
import { <PERSON>agger } from "brainui";
import { useTranslation } from "react-i18next";
import { RemovableListFile } from "../../molecules";
import { IFileUploadStepProps } from "./FileUploadStep.interfaces";
import { Content, Wrapper } from "./FileUploadStep.styles";

function FileUploadStep({
  data,
  filename,
  reportType,
  fileDetailLabel,
  fileDetailLabelPl,
  onChange,
}: IFileUploadStepProps) {
  const { t } = useTranslation();

  function handleFileUpload(file: File) {
    const reader = new FileReader();

    if (file.size / (1024 * 1000) > 100) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 100 }),
      });
      return;
    }

    reader.onload = ({ target }) => {
      const parser = CSV_PARSER_BY_REPORT_TYPE[reportType];
      if (!parser) return;

      const rows = (target.result as string)
        .trim()
        .split(/\s/g)
        .slice(1)
        .map((x) => x.split(";"))
        .filter((item) => {
          if (item.length > 1) {
            return item.reduce((acc, curr) => acc || curr.trim() !== "", false);
          }
          if (!item.length) return false;
          return item[0].trim() !== "";
        });

      const result = parser(rows);
      if (!result.validRows.length) {
        notification.error({
          message: t("NO_VALID_DOCUMENTS_IN_FILE"),
        });
        return;
      }
      onChange?.(file, result);
    };

    reader.readAsText(file);
  }

  return (
    <Wrapper data-testid="pb-t-file-upload-step">
      <Content>
        <section>
          <h2>{t("UPLOAD_DOCUMENTS_FILE")}</h2>
          <p>{t("UPLOAD_DOCUMENTS_FILE_DESCRIPTION")}</p>
          <Button
            size="small"
            style={{ marginBottom: "20px" }}
            download
            href={MODEL_BY_REPORT_TYPE[reportType]}
          >
            {t("DOWNLOAD_TEMPLATE")}
          </Button>
          {!data?.validRows.length && (
            <Dragger
              uploadProps={{
                name: "file",
                multiple: true,
                showUploadList: false,
                accept: ".csv,.txt",
                beforeUpload: (file) => {
                  handleFileUpload(file);
                  return false;
                },
              }}
              fileList={[]}
              onChange={() => {}}
              onDelete={() => {}}
            />
          )}
          {data?.validRows.length > 0 && (
            <RemovableListFile
              index={1}
              onRemove={() => {
                onChange();
              }}
              filename={filename}
              fileDetails={`${data.validRows.length} ${
                data.validRows.length != 1
                  ? t(fileDetailLabelPl ?? "ROWS")
                  : t(fileDetailLabel ?? "ROW")
              }`}
            ></RemovableListFile>
          )}
        </section>
      </Content>
    </Wrapper>
  );
}

export default FileUploadStep;
