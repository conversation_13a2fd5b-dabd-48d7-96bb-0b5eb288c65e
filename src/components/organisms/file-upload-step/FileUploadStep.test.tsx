import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import FileUploadStep from "./FileUploadStep";
import { IFileUploadStepProps } from "./FileUploadStep.interfaces";
import { ReportType } from "../../../types/report-type";
import { CSV_PARSER_BY_REPORT_TYPE } from "../../../constants/files";
import { notification } from "antd";
import { IReportCSVParserResult } from "@types";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("antd", () => ({
  ...jest.requireActual("antd"),
  notification: {
    warning: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("../../../constants", () => ({
  MODEL_BY_REPORT_TYPE: {
    SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE: "template1.csv",
  },
}));

jest.mock("../../../constants/files", () => ({
  CSV_PARSER_BY_REPORT_TYPE: {
    SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE: jest.fn(() => ({
      validRows: [{}, {}],
    })),
  },
}));

const mockProps: IFileUploadStepProps = {
  data: { validRows: [], invalidRows: [] },
  filename: "test.csv",
  reportType: ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE,
  fileDetailLabel: "ROW",
  fileDetailLabelPl: "ROWS",
  onChange: jest.fn(),
};

describe("FileUploadStep", () => {
  it("renders correctly", () => {
    render(<FileUploadStep {...mockProps} />);
    expect(screen.getByText("UPLOAD_DOCUMENTS_FILE")).toBeInTheDocument();
    expect(
      screen.getByText("UPLOAD_DOCUMENTS_FILE_DESCRIPTION")
    ).toBeInTheDocument();
  });

  it("handles file upload correctly", () => {
    render(<FileUploadStep {...mockProps} />);
    const file = new File(["file content"], "test.csv", { type: "text/csv" });

    const input = screen
      .getByTestId("pb-t-file-upload-step")
      .querySelector("input[type='file']");
    if (input) {
      fireEvent.change(input, { target: { files: [file] } });
    }

    expect(notification.warning).not.toHaveBeenCalled();
  });

  it("shows warning for large file", () => {
    render(<FileUploadStep {...mockProps} />);
    const file = new File(["file content"], "test.csv", { type: "text/csv" });
    Object.defineProperty(file, "size", { value: 1024 * 1024 * 101 });

    const input = screen
      .getByTestId("pb-t-file-upload-step")
      .querySelector("input[type='file']");
    if (input) {
      fireEvent.change(input, { target: { files: [file] } });
    }

    expect(notification.warning).toHaveBeenCalledWith({
      message: "FILE_NOT_SENT",
      description: "ALERT_FILE_SIZE",
    });
    expect(mockProps.onChange).not.toHaveBeenCalled();
  });

  it("shows error for invalid file content", () => {
    (
      CSV_PARSER_BY_REPORT_TYPE.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE as jest.Mock
    ).mockReturnValueOnce({
      validRows: [],
    });
    render(<FileUploadStep {...mockProps} />);
    const file = new File(["invalid content"], "test.csv", {
      type: "text/csv",
    });

    const input = screen
      .getByTestId("pb-t-file-upload-step")
      .querySelector("input[type='file']");
    if (input) {
      fireEvent.change(input, { target: { files: [file] } });
    }

    expect(mockProps.onChange).not.toHaveBeenCalled();
  });

  it("renders RemovableListFile when there are valid rows", () => {
    const propsWithValidRows: IFileUploadStepProps = {
      ...mockProps,
      data: { validRows: [{}, {}], invalidRows: [] },
    };
    render(<FileUploadStep {...propsWithValidRows} />);
    expect(screen.getByText("2 ROWS")).toBeInTheDocument();
  });
});
