import { render } from "@testing-library/react";
import { REPORT_REQUEST_COLUMNS } from "../report-requests-table/columns";
import { ReportRequest, ReportType } from "@types";
import { hourMin, shortDate } from "@utils";
import { REPORT_TITLE } from "@constants";

jest.mock("react-i18next", () => ({
  Trans: ({ i18nKey, values }) => (
    <span>
      {i18nKey}
      {values ? `: ${JSON.stringify(values)}` : ""}
    </span>
  ),
}));

jest.mock("@/components/molecules", () => ({
  RequestStatusIndicator: jest.fn(() => <div>RequestStatusIndicator</div>),
}));

jest.useFakeTimers().setSystemTime(new Date("2020-05-05 00:00:00"));

describe("REPORT_REQUEST_COLUMNS", () => {
  const mockRequest: ReportRequest = {
    id: 1,
    tags_names: ["tag1", "tag2"],
    user: { name: "<PERSON>", email: "<EMAIL>" },
    created: new Date(),
    reports_types: [ReportType.AGRO_CREDIT] as unknown as ReportType[],
    reports_count: 2,
  } as ReportRequest;

  it("should render ID column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[0] as any).render!(
      mockRequest.id
    ) as any;
    const { container } = render(component);
    expect(container.textContent).toBe("1");
  });

  it("should render Tags column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[1] as any).render!(
      mockRequest.tags_names
    ) as any;

    const { container } = render(component);
    expect(container.textContent).toBe("tag1tag2");
  });

  it("should render Requested By column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[2] as any).render!(
      mockRequest.user
    ) as any;

    const { container } = render(component);
    expect(container.textContent).toBe("<EMAIL> Doe");
  });

  it("should render Requested At column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[3] as any).render!(
      mockRequest.created
    ) as any;

    const { container } = render(component);

    expect(container.textContent).toEqual(
      `HOUR_OF: ${JSON.stringify({ hour: hourMin(new Date()) })}${shortDate(
        new Date()
      )}`
    );
  });

  it("should render Report Type column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[4] as any).render!(
      mockRequest.reports_types
    ) as any;

    const { container } = render(component);
    expect(container.textContent).toBe(REPORT_TITLE[ReportType.AGRO_CREDIT]);
  });

  it("should render Report Count column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[5] as any).render!(
      mockRequest.reports_count
    ) as any;

    const { container } = render(component);
    expect(container.textContent).toBe("2 REPORTS");
  });

  it("should render Report Status column correctly", () => {
    const component = (REPORT_REQUEST_COLUMNS[6] as any).render!(
      mockRequest.id,
      mockRequest
    ) as any;

    const { container } = render(component);
    expect(container.textContent).toBe("RequestStatusIndicator");
  });
});
