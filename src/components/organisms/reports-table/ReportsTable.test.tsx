import { cleanup, render, screen, fireEvent, waitFor } from "@testing-library/react";
import ReportsTable from "./ReportsTable";
import { Report, ReportStatus, ReportType, ReportFormat } from "@types";
import * as useListReportsModule from "@/hooks/use-list-reports";
import * as useReportRequestsListModule from "@/hooks/use-report-requests-list";
import * as useCardsListModule from "@/hooks/use-cards-list";
import * as useAutoRefetchReportsModule from "@/hooks/use-auto-refetch-reports";
import { cloneReport, downloadReport, downloadReportProperties } from "@services";
import { notification } from "antd";
import { Pagination } from "@/store/modules/report-requests/list/types";
import { AvailableReport } from "@/constants/reports";

jest.mock("@/hooks/use-list-reports", () => ({
  useListReports: jest.fn(),
}));
jest.mock("@/hooks/use-report-requests-list", () => ({
  useReportRequestsList: jest.fn(),
}));
jest.mock("@/hooks/use-cards-list", () => ({
  useCardsList: jest.fn(),
}));
jest.mock("@/hooks/use-auto-refetch-reports", () => ({
  useAutoRefetchReports: jest.fn(),
}));

jest.mock("@services", () => ({
  cloneReport: jest.fn(),
  downloadReport: jest.fn(),
  downloadReportProperties: jest.fn(),
  downloadFile: jest.fn(),
}));

jest.mock("antd", () => {
  const originalModule = jest.requireActual("antd");
  return {
    ...originalModule,
    notification: {
      success: jest.fn(),
      error: jest.fn(),
    },
  };
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      if (options) {
        return `${key} ${JSON.stringify(options)}`;
      }
      return key;
    },
  }),
  Trans: ({ i18nKey, values }: { i18nKey: string; values?: any }) => (
    <span>
      {i18nKey}
      {values ? ` ${JSON.stringify(values)}` : ""}
    </span>
  ),
}));

jest.mock("moment", () => {
  return () => ({
    format: () => "01-01-2023T12_00_00",
  });
});

URL.createObjectURL = jest.fn(() => "blob:url");

const originalCreateElement = document.createElement;
document.createElement = jest.fn().mockImplementation((tag) => {
  if (tag === "a") {
    return {
      href: "",
      download: "",
      dispatchEvent: jest.fn(),
      remove: jest.fn(),
    };
  }
  return originalCreateElement.call(document, tag);
});

describe("ReportsTable component", () => {
  const mockRequestId = 123;
  const mockHandleListReports = jest.fn();
  const mockHandleListReportsRequests = jest.fn();
  const mockHandleSetPaginationData = jest.fn();
  const mockHandleSetFiltersData = jest.fn();

  const mockReports: Report[] = [
    {
      id: 1,
      key: "1",
      external_id: null,
      report_request_id: 123,
      report_exports: [1],
      report_about: [
        { label: "CPF", value: "12345678900" },
        { label: "CONTACT_NAME", value: "John Doe" },
        { label: "CAR", value: "CAR12345" },
      ],
      report_type: ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR,
      status: ReportStatus.DONE,
      created: new Date("2023-01-01"),
      modified: new Date("2023-01-01"),
      errors: null,
      observation: null,
      signature: false,
    },
    {
      id: 2,
      key: "2",
      external_id: null,
      report_request_id: 123,
      report_exports: [],
      report_about: [
        { label: "CNPJ", value: "12345678000100" },
        { label: "CONTACT_NAME", value: "Company Inc" },
        { label: "PROPERTY_NAME", value: "Farm 1" },
      ],
      report_type: ReportType.INSPECTION,
      status: ReportStatus.PROCESSING,
      created: new Date("2023-01-02"),
      modified: new Date("2023-01-02"),
      errors: null,
      observation: null,
      signature: false,
    },
  ];

  const mockCardsList: AvailableReport[] = [
    {
      id: 1,
      type: ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR,
      title: "Protocol EUDR",
      description: "Protocol EUDR description",
      formats: [ReportFormat.PDF],
      models: [],
      isNew: false,
      commingSoon: false,
      hasMetadata: false,
      hasSignature: false,
      hasSignatureResource: "valuation-report-signed" as any,
    },
    {
      id: 2,
      type: ReportType.INSPECTION,
      title: "Inspection",
      description: "Inspection description",
      formats: [ReportFormat.PDF],
      models: [],
      isNew: false,
      commingSoon: false,
      hasMetadata: false,
      hasSignature: false,
      hasSignatureResource: "valuation-report-signed" as any,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    jest.spyOn(useListReportsModule, 'useListReports').mockReturnValue({
      handleListReports: mockHandleListReports,
      reports: mockReports,
      total: mockReports.length,
    });

    jest.spyOn(useReportRequestsListModule, 'useReportRequestsList').mockReturnValue({
      handleListReportsRequests: mockHandleListReportsRequests,
      pagination: {
        offset: 0,
        limit: 10,
        total: 10,
        pages: 1,
        totalPages: 1
      } as Pagination,
      handleSetPaginationData: mockHandleSetPaginationData,
      handleSetFiltersData: mockHandleSetFiltersData,
      reportsCount: 2,
      count: 2,
      requests: [],
      isLoading: false,
      filters: {},
      handleClearFiltersAndRefreshReports: jest.fn(),
    });

    jest.spyOn(useCardsListModule, 'useCardsList').mockReturnValue({
      cardsList: mockCardsList,
      handleListCards: jest.fn(),
      isLoadingCards: false,
    });

    jest.spyOn(useAutoRefetchReportsModule, 'useAutoRefetchReports').mockImplementation((fn) => {
      return fn;
    });
  });

  afterEach(() => {
    cleanup();
  });

  it("should render the component", () => {
    render(<ReportsTable requestId={mockRequestId} />);

    const component = screen.getByTestId("pb-t-reports-table");
    expect(component).toBeInTheDocument();
  });

  it("should call handleListReports on mount", () => {
    render(<ReportsTable requestId={mockRequestId} />);

    expect(mockHandleListReports).toHaveBeenCalledWith({
      page: 1,
      pageSize: 10,
      requestId: mockRequestId,
    });
  });

  it("should filter reports based on requestId", () => {
    render(<ReportsTable requestId={mockRequestId} />);

    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("Company Inc")).toBeInTheDocument();
  });

  it("should render report columns correctly", () => {
    render(<ReportsTable requestId={mockRequestId} />);

    expect(screen.getByText("1")).toBeInTheDocument();
    expect(screen.getByText("2")).toBeInTheDocument();

    expect(screen.getByText("123.456.789-00")).toBeInTheDocument();
    expect(screen.getByText("12.345.678/0001-00")).toBeInTheDocument();

    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("Company Inc")).toBeInTheDocument();
  });

  it("should handle download report action", async () => {
    (downloadReport as jest.Mock).mockResolvedValue(new Blob(["test data"]));

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const downloadOption = await screen.findByText("DOWNLOAD_REPORT_EXTENSION {\"extension\":\"PDF\"}");
    fireEvent.click(downloadOption);

    await waitFor(() => {
      expect(downloadReport).toHaveBeenCalledWith(1, 1);
      expect(notification.success).toHaveBeenCalledWith({
        message: "DOWNLOAD_STARTED",
      });
    });
  });

  it("should handle property download for EUDR reports", async () => {
    (downloadReportProperties as jest.Mock).mockResolvedValue({
      data: new Blob(["test data"]),
      filename: "test.json",
    });

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const propertyDownloadOption = await screen.findByText("DOWNLOAD_PROPERTY_INDIVIDUAL");
    fireEvent.click(propertyDownloadOption);

    await waitFor(() => {
      expect(downloadReportProperties).toHaveBeenCalledWith(1);
      expect(notification.success).toHaveBeenCalledWith({
        message: "DOWNLOAD_FINISHED",
      });
    });
  });

  it("should handle report update action", async () => {
    (cloneReport as jest.Mock).mockResolvedValue({});

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const updateOption = await screen.findByText("REQUEST_REPORT_UPDATE");
    fireEvent.click(updateOption);

    await waitFor(() => {
      expect(cloneReport).toHaveBeenCalledWith(1);
      expect(mockHandleListReportsRequests).toHaveBeenCalled();
      expect(notification.success).toHaveBeenCalledWith({
        message: "REPORT_UPDATE_SUCCESS",
      });
    });
  });

  it("should handle contestation request action", async () => {
    (cloneReport as jest.Mock).mockResolvedValue({});

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const contestationOption = await screen.findByText("REQUEST_CONTESTATION");
    fireEvent.click(contestationOption);

    await waitFor(() => {
      expect(cloneReport).toHaveBeenCalledWith(1, ReportType.DETAILED_ANALYSIS_DEFORESTATION);
      expect(mockHandleListReportsRequests).toHaveBeenCalled();
      expect(notification.success).toHaveBeenCalledWith({
        message: "REPORT_CONTESTATION_SUCCESS",
      });
    });
  });

  it("should handle pagination change", () => {

    render(<ReportsTable requestId={mockRequestId} />);

    mockHandleListReports.mockClear();

    const component = screen.getByTestId("pb-t-reports-table");
    expect(component).toBeInTheDocument();

    mockHandleListReports({ page: 2, pageSize: 10, requestId: mockRequestId });

    expect(mockHandleListReports).toHaveBeenCalledWith({
      page: 2,
      pageSize: 10,
      requestId: mockRequestId,
    });
  });

  it("should handle download error", async () => {
    (downloadReport as jest.Mock).mockRejectedValue(new Error("Download failed"));

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const downloadOption = await screen.findByText("DOWNLOAD_REPORT_EXTENSION {\"extension\":\"PDF\"}");
    fireEvent.click(downloadOption);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith({
        message: "ERR_REPORT_DOWNLOAD",
      });
    });
  });

  it("should handle property download error", async () => {
    (downloadReportProperties as jest.Mock).mockRejectedValue(new Error("Download failed"));

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const propertyDownloadOption = await screen.findByText("DOWNLOAD_PROPERTY_INDIVIDUAL");
    fireEvent.click(propertyDownloadOption);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith({
        message: "ERR_PROPERTIES_DOWNLOAD",
      });
    });
  });

  it("should handle report update error", async () => {
    (cloneReport as jest.Mock).mockRejectedValue(new Error("Clone failed"));

    render(<ReportsTable requestId={mockRequestId} />);

    const actionButtons = screen.getAllByRole("button");
    const moreButton = actionButtons.find(button =>
      button.querySelector('.anticon-more')
    );
    expect(moreButton).toBeTruthy();
    if (moreButton) {
      fireEvent.click(moreButton);
    }

    const updateOption = await screen.findByText("REQUEST_REPORT_UPDATE");
    fireEvent.click(updateOption);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith({
        message: "ERROR_UPDATE_REPORT",
        description: "TRY_AGAIN_LATER",
      });
    });
  });
});
