import { ColumnsType } from "antd/lib/table";
import { Trans } from "react-i18next";
import { REPORT_TITLE } from "../../../constants/reports";
import { Report, ReportAbout } from "../../../types/report";
import { ReportType } from "../../../types/report-type";
import { hourMin, shortDate } from "../../../utils/date";
import { formatDocument } from "../../../utils/document";
import { ReportAboutColumn, ReportStatusIndicator } from "../../molecules";
import {
  ColumnAlignRight,
  ColumnCaption,
  ColumnValue,
} from "./ReportsTable.styles";

export const REPORT_COLUMNS: ColumnsType<Report> = [
  {
    title: "ID",
    key: "id",
    dataIndex: "id",
    width: "50px",
    render: (id: number) => <span>{id}</span>,
  },
  {
    title: () => <Trans i18nKey="COLUMN_DOCUMENT" />,
    key: "file_name",
    dataIndex: "report_about",
    render: (reportAbout: ReportAbout[]) => {
      const doc = reportAbout.find((d) => ["CPF", "CNPJ"].includes(d.label));
      const name = reportAbout.find((d) => d.label == "CONTACT_NAME");

      return (
        <div>
          <ColumnCaption>
            {doc && formatDocument(doc.value, doc.label)}
          </ColumnCaption>
          <ColumnValue>{name?.value}</ColumnValue>
        </div>
      );
    },
  },
  {
    title: () => <Trans i18nKey="REPORT_TYPE" />,
    key: "report_type",
    dataIndex: "report_type",
    render: (type: ReportType) => (
      <span>
        <Trans i18nKey={REPORT_TITLE[type]} />
      </span>
    ),
  },
  {
    title: () => <Trans i18nKey="INTEREST_AREA" />,
    key: "report_about",
    dataIndex: "report_about",
    width: "240px",
    render: (reportAbout: ReportAbout[]) => (
      <ReportAboutColumn reportAbout={reportAbout} />
    ),
  },
  {
    title: () => <Trans i18nKey="CREATED_AT" />,
    key: "action",
    align: "right",
    width: "100px",
    render: (report) => {
      const dict = {
        DONE: {
          hour: (
            <Trans
              i18nKey="HOUR_OF"
              values={{ hour: hourMin(report.modified) }}
            />
          ),
          date: shortDate(report.modified),
        },

        ERROR: {
          hour: "",
          date: "-",
        },
        PENDING: {
          hour: "",
          date: "",
        },
        PROCESSING: {
          hour: "",
          date: "",
        },
      };

      return (
        <ColumnAlignRight>
          <ColumnCaption>{dict[report.status].hour}</ColumnCaption>
          <ColumnValue>{dict[report.status].date}</ColumnValue>
        </ColumnAlignRight>
      );
    },
  },
  {
    title: () => <Trans i18nKey="REPORT_STATUS" />,
    key: "status",
    dataIndex: "status",
    align: "right",
    width: "200px",
    render: (status, report) => (
      <ReportStatusIndicator status={status} errorMessage={report.errors} />
    ),
  },
];
