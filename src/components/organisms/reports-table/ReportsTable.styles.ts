import { Table } from "brainui";
import styled, { css } from "styled-components";

export const ColumnCaption = styled.p`
  font-size: 12px;
  font-weight: 300;
  line-height: 1.5;
  margin: 0;
`;

export const ColumnValue = styled.p`
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
`;

export const ColumnAlignRight = styled.div`
  text-align: right;
`;

export const ReportTable = styled(Table)`
  padding-top: 14px;
  padding-left: 97px;
  padding-bottom: 50px;

  ${({ dataSource }) =>
    dataSource?.map(
      ({ key }: any) => css`
        tr[data-row-key*="${key}"] td:nth-child(2) {
          border-left: none !important;
        }
      `
    )}
`;
