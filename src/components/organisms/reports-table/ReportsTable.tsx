import { DownloadOutlined, MoreOutlined } from "@ant-design/icons";
import { CONTENT_TYPE, REPORT_FORMAT_DATA } from "@constants";
import {
  cloneReport,
  downloadFile,
  downloadReport,
  downloadReportProperties,
} from "@services";
import { Report, ReportType } from "@types";
import { getReportStatusColor } from "@utils";
import { Button, Dropdown, MenuProps, notification } from "antd";
import moment from "moment";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { icon_pdf, icon_source_notes } from "../../../assets";
import { ReportTable } from "./ReportsTable.styles";
import { REPORT_COLUMNS } from "./columns";
import { useAutoRefetchReports } from "../../../hooks/use-auto-refetch-reports";
import { useCardsList } from "@/hooks/use-cards-list";
import { useReportRequestsList } from "@/hooks/use-report-requests-list";
import { useListReports } from "@/hooks/use-list-reports";

interface ReportsTableProps {
  requestId: number;
}

function ReportsTable({ requestId }: ReportsTableProps) {
  const { t } = useTranslation();
  const { cardsList } = useCardsList();
  const { handleListReports, reports, total } = useListReports();
  const {
    handleListReportsRequests,
    pagination,
    handleSetPaginationData,
    handleSetFiltersData,
  } = useReportRequestsList();
  const [reportsFiltered, setReportsFiltered] = useState<Report[]>([]);

  useAutoRefetchReports(
    () => handleListReports({ page: 1, pageSize: 10, requestId }),
    reports
  );

  useEffect(() => {
    handleListReports({ page: 1, pageSize: 10, requestId });
  }, [requestId]);

  useEffect(() => {
    const filtered = reports.filter(
      (item) => item.report_request_id === requestId
    );
    if (filtered.length) {
      setReportsFiltered(filtered);
    }
  }, [requestId, reports]);

  useEffect(() => {
    return () => {
      handleSetPaginationData({ pagination: { offset: 0 } });
      handleSetFiltersData({ filters: {} });
    };
  }, []);

  const COLUMNS = [
    ...REPORT_COLUMNS,
    {
      title: "Ações",
      key: "action",
      align: "center",
      width: "80px",
      render: (_, report: Report) => {
        let report_type = report.report_type;
        if (report_type == ReportType.INSPECTION_FINANCIAL) {
          report_type = ReportType.INSPECTION;
        } else if (report_type == ReportType.SOY_DEFORESTATION_MUNICIPALITY) {
          report_type = ReportType.SOY_DEFORESTATION_CAR;
        }
        const reportMetadata = cardsList.find(
          (card) => card.type == report_type
        );
        let label = t("DOWNLOAD_REPORT");
        if (reportMetadata?.formats.length) {
          label = t("DOWNLOAD_REPORT_EXTENSION", {
            extension: REPORT_FORMAT_DATA[reportMetadata?.formats[0]]?.text,
          });
        }
        let items: MenuProps["items"] = [
          {
            key: 1,
            label,
            icon: <img src={icon_pdf} width="16" height="16" alt="PDF" />,
            disabled: !report.report_exports?.length,
            onClick: async () => {
              notification.success({
                message: t("DOWNLOAD_STARTED"),
              });
              downloadReport(report.id, report.report_exports[0])
                .then((fileData) => {
                  const date = moment().format("DD-MM-YYYYTHH_mm_SS");
                  const type =
                    REPORT_FORMAT_DATA[reportMetadata?.formats[0]]?.text;
                  const filename = `REPORT_${report.report_type}_${
                    report.id
                  }_${date}.${type.toLowerCase()}`;
                  const blob = new Blob([fileData], {
                    type: CONTENT_TYPE[type],
                  });
                  const link = document.createElement("a");
                  link.href = URL.createObjectURL(blob);
                  link.download = filename;
                  link.dispatchEvent(
                    new MouseEvent(`click`, {
                      bubbles: true,
                      cancelable: true,
                      view: window,
                    })
                  );
                  setTimeout(function () {
                    link.remove();
                  }, 0);
                  notification.success({
                    message: t("DOWNLOAD_FINISHED"),
                  });
                })
                .catch(() => {
                  notification.error({
                    message: t("ERR_REPORT_DOWNLOAD"),
                  });
                });
            },
          },
        ];
        if (
          report.report_type === ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR ||
          report.report_type ===
            ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
        ) {
          items.push({
            key: 2,
            label: t("DOWNLOAD_PROPERTY_INDIVIDUAL"),
            onClick: () => handlePropertyDownload(report),
            icon: <DownloadOutlined />,
          });
        }
        if (
          ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE != report.report_type
        ) {
          items.push({
            key: 3,
            label: t("REQUEST_REPORT_UPDATE"),
            onClick: async () => {
              try {
                await cloneReport(report.id);
                handleListReportsRequests({
                  offset: pagination.offset,
                  limit: pagination.limit,
                });
                notification.success({
                  message: t("REPORT_UPDATE_SUCCESS"),
                });
              } catch {
                notification.error({
                  message: t("ERROR_UPDATE_REPORT"),
                  description: t("TRY_AGAIN_LATER"),
                });
              }
            },
            icon: (
              <img
                src={icon_source_notes}
                width="16"
                height="16"
                alt={t("UPDATE_REPORTS")}
              />
            ),
          });
        }
        if (
          [
            ReportType.SOCIOENVIRONMENT_COMPLIANCE,
            ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR,
            ReportType.SOCIOENVIRONMENT_PROTOCOL,
            ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG,
            ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER,
          ].includes(report.report_type)
        ) {
          items.push({
            key: 4,
            label: t("REQUEST_CONTESTATION"),
            disabled: !report.report_exports?.length,
            onClick: async () => {
              try {
                await cloneReport(
                  report.id,
                  ReportType.DETAILED_ANALYSIS_DEFORESTATION
                );
                handleListReportsRequests({
                  offset: pagination.offset,
                  limit: pagination.limit,
                });
                notification.success({
                  message: t("REPORT_CONTESTATION_SUCCESS"),
                });
              } catch {
                notification.error({
                  message: t("ERROR_CONTESTATION_REPORT"),
                  description: t("TRY_AGAIN_LATER"),
                });
              }
            },
            icon: (
              <img
                src={icon_source_notes}
                width="16"
                height="16"
                alt={t("REQUEST_CONTESTATION")}
              />
            ),
          });
        }
        return (
          <Dropdown
            menu={{
              items,
            }}
            placement="bottomRight"
            trigger={["click"]}
            arrow
            disabled={!items.length}
          >
            <Button
              type="ghost"
              size="small"
              shape="circle"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        );
      },
    },
  ];

  async function handlePropertyDownload(report) {
    try {
      const { data, filename } = await downloadReportProperties(report.id);
      downloadFile(data, filename, "application/json");
      notification.success({
        message: t("DOWNLOAD_FINISHED"),
      });
    } catch {
      notification.error({
        message: t("ERR_PROPERTIES_DOWNLOAD"),
      });
    }
  }

  return (
    <ReportTable
      data-testid="pb-t-reports-table"
      className="ar-reports-table"
      columns={COLUMNS as any}
      dataSource={reportsFiltered.map((report) => ({
        ...report,
        key: report.id,
        borderColor: getReportStatusColor(report.status),
      }))}
      pagination={{
        hideOnSinglePage: true,
        total,
        onChange: (page, pageSize) => {
          handleListReports({ page, pageSize, requestId });
        },
      }}
    />
  );
}

export default ReportsTable;
