import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import AgroCreditUpload from "./AgroCreditUpload";
import <PERSON> from "papaparse";
import { notification } from "antd";

jest.mock("antd", () => {
  const antd = jest.requireActual("antd");
  return {
    ...antd,
    notification: {
      warning: jest.fn(),
      error: jest.fn(),
    },
  };
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("@/hooks/use-tags-list", () => {
  return {
    useTagsList: jest.fn(() => ({
      tagsList: [],
    })),
  };
});

describe("AgroCreditUpload component", () => {
  const notificationWarningMock = jest.fn();
  const notificationErrorMock = jest.fn();

  beforeEach(() => {
    (notification.warning as jest.Mock).mockImplementation(
      notificationWarningMock
    );
    (notification.error as jest.Mock).mockImplementation(notificationErrorMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    const component = screen.queryByTestId("pb-t-agro-credit-upload");
    expect(component).toBeInTheDocument();
  });

  it("should call onNext when Next button is clicked", () => {
    const onNextMock = jest.fn();
    render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [{ document: "1234567", debt: "1000" }],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={() => {}}
        onNext={onNextMock}
      />
    );

    const nextButton = screen.getByText("NEXT");
    fireEvent.click(nextButton);

    expect(onNextMock).toHaveBeenCalled();
  });
  it("should call handleUploadFile when file is uploaded", async () => {
    const onNextMock = jest.fn();
    const onChangeMock = jest.fn();
    const { getByTestId } = render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={onChangeMock}
        onNext={onNextMock}
      />
    );

    const dragger = getByTestId("dragger-input-file");

    const csvData = Papa.unparse(
      [{ documento: "98976362934", "dívida(R$)": "1000" }],
      {
        delimiter: ";",
      }
    );

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [
          new File([csvData.trim()], "filename.csv", { type: "text/csv" }),
        ],
      },
    });

    const expected = {
      documents: [
        {
          debt: "1000",
          document: "98976362934",
        },
      ],
      filename: "filename.csv",
      invalidRows: [],
      scores: [],
      tags: [],
    };

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(
        expect.objectContaining(expected)
      );
    });
  });
  it("should show a notification if file is too large", async () => {
    const { getByTestId } = render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    const dragger = getByTestId("dragger-input-file");

    const blob = new Blob([new Uint8Array(101 * 1024 * 1024)], {
      type: "text/csv",
    });

    const file = new File([blob], "filename.csv", {
      type: "text/csv",
    });

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [file],
      },
    });

    await waitFor(() => {
      expect(notificationWarningMock).toHaveBeenCalledWith({
        message: "FILE_NOT_SENT",
        description: "ALERT_FILE_SIZE",
      });
    });
  });

  it("should show a notification if does not have any documents valid", async () => {
    const { getByTestId } = render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    const dragger = getByTestId("dragger-input-file");

    const csvData = Papa.unparse(
      [{ documento: "111111", "dívida(R$)": "1000" }],
      {
        delimiter: ";",
      }
    );

    const file = new File([csvData], "filename.csv", {
      type: "text/csv",
    });

    fireEvent.drop(dragger, {
      dataTransfer: {
        files: [file],
      },
    });

    await waitFor(() => {
      expect(notificationErrorMock).toHaveBeenCalledWith({
        message: "NO_VALID_DOCUMENTS_IN_FILE",
      });
    });
  });

  it("should close form if button cancel is clicked", () => {
    const onChangeMock = jest.fn();
    const { getByText } = render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={onChangeMock}
        onNext={() => {}}
      />
    );

    const button = getByText("CANCEL");
    fireEvent.click(button);

    expect(onChangeMock).toHaveBeenCalled();
  });

  it("should call onChange when button to remove document uploaded is clicked", () => {
    const onChangeMock = jest.fn();
    const { getByTestId } = render(
      <AgroCreditUpload
        data={{
          file: null,
          filename: "",
          documents: [
            {
              debt: "1000",
              document: "9999999",
            },
          ],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onChange={onChangeMock}
        onNext={() => {}}
      />
    );

    const button = getByTestId("pb-t-removable-list-file-button");
    fireEvent.click(button);

    expect(onChangeMock).toHaveBeenCalledWith({
      documents: [],
      file: null,
      filename: "",
      invalidRows: [],
      scores: [],
      tags: [],
    });
  });
});
