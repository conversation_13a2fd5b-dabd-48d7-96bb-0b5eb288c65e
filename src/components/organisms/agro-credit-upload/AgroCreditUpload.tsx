import { RequestFormContext } from "@/contexts/RequestFormContext";
import { isDocumentValid } from "@utils";
import { Button, notification } from "antd";
import { <PERSON>agger } from "brainui";
import { useContext } from "react";
import { useTranslation } from "react-i18next";
import { model_agro_credit_input } from "../../../assets/data";
import { RemovableListFile, SelectTag } from "../../molecules";
import { AgroCreditFormState } from "../agro-credit-form/AgroCreditForm";
import { Content, Options, Wrapper } from "./AgroCreditUpload.styles";
import { useTagsList } from "../../../hooks/use-tags-list";
import { readCSV } from "@/utils/csv";

interface AgroCreditUploadProps {
  readonly data: AgroCreditFormState;
  readonly onChange: (data?: AgroCreditFormState) => void;
  readonly onNext: () => void;
}

function AgroCreditUpload({ data, onChange, onNext }: AgroCreditUploadProps) {
  const { t } = useTranslation();
  const { tagsList } = useTagsList();
  const { closeRequestForm } = useContext(RequestFormContext);

  async function handleFileUpload(file: File) {
    if (file.size / (1024 * 1000) > 100) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 100 }),
      });
      return;
    }

    const transformHeader = (_: string, index: number) => {
      const dict = {
        0: "document",
        1: "debt",
      };

      return dict[index];
    };

    const csv: Array<{ document: string; debt: string }> = await readCSV(
      file,
      transformHeader
    );

    const documents = csv.filter(
      (item) => item.document.length >= 7 && isDocumentValid(item.document)
    );
    const invalidRows = csv.filter(
      (item) => item.document.length < 7 || !isDocumentValid(item.document)
    );

    if (!documents.length) {
      notification.error({
        message: t("NO_VALID_DOCUMENTS_IN_FILE"),
      });
      return;
    }

    onChange({
      ...data,
      filename: file.name,
      file,
      documents,
      invalidRows,
    });
  }

  function handleClose() {
    onChange();
    closeRequestForm();
  }

  return (
    <Wrapper data-testid="pb-t-agro-credit-upload">
      <Content>
        <h1>{t("UPLOAD_DOCUMENT_LIST")}</h1>
        <section>
          <h2>{t("IDENTIFICATION_TAG")}</h2>
          <p>{t("IDENTIFICATION_TAG_DESCRIPTION")}</p>
          <SelectTag
            tags={tagsList}
            placeholder={t("TAG_NAME")}
            value={data.tags}
            onChange={(tags: string[]) => {
              onChange({
                ...data,
                tags,
              });
            }}
            optionLimit={data.tags.length == 3}
          />
        </section>
        <section>
          <h2>{t("UPLOAD_DOCUMENTS_FILE")}</h2>
          <p>{t("UPLOAD_DOCUMENTS_FILE_DESCRIPTION")}</p>
          <Button
            size="small"
            style={{ marginBottom: "20px" }}
            download
            href={model_agro_credit_input}
          >
            {t("DOWNLOAD_TEMPLATE")}
          </Button>
          {!data.documents.length && (
            <Dragger
              data-testid="dragger-input-file"
              uploadProps={{
                name: "file",
                multiple: true,
                showUploadList: false,
                accept: ".csv,.txt",
                beforeUpload: (file) => {
                  handleFileUpload(file);
                  return false;
                },
              }}
              fileList={[]}
              onChange={() => {}}
              onDelete={() => {}}
            />
          )}
          {data.documents.length > 0 && (
            <RemovableListFile
              index={1}
              onRemove={() => {
                onChange({
                  ...data,
                  filename: "",
                  documents: [],
                  invalidRows: [],
                });
              }}
              filename={data.filename}
              fileDetails={`${data.documents.length} ${
                data.documents.length != 1 ? t("DEBTORS") : t("DEBTOR")
              }`}
            ></RemovableListFile>
          )}
        </section>
      </Content>
      <Options>
        <Button onClick={handleClose}>{t("CANCEL")}</Button>
        <Button
          type="primary"
          disabled={!data.documents.length}
          onClick={onNext}
        >
          {t("NEXT")}
        </Button>
      </Options>
    </Wrapper>
  );
}

export default AgroCreditUpload;
