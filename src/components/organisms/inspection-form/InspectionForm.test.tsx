import { cleanup, render, screen, fireEvent, waitFor } from "@testing-library/react";
import InspectionForm from "./InspectionForm";
import { ReportType } from "@types";
import { getRefBacenInfo } from "@services";
import { notification } from "antd";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => {
      const translations = {
        "BACEN_REPORT": "Resolução BACEN",
        "STANDART": "Padrão",
        "BACEN_REFERENCE": "Referência BACEN",
        "CONTRACT_NAME": "Número do contrato",
        "FINANCIAL_INSTITUTION": "Instituição financeira",
        "INSTITUTION_NAME": "Nome da instituição",
        "INSTITUTION_OFFICER": "Reponsável pela instituição",
        "MUNICIPALITY": "Município",
        "FINANCED_CROP_YEAR": "Safra financiada",
        "FINANCED_CROP": "Cultura financiada",
        "TOTAL_FINANCED_AREA": "Área total financiada",
        "ESTIMATED_CROP_YIELD": "Produtividade estimada",
        "SOIL_TYPE": "Tipo de solo",
        "CROP_GROUP": "Grupo de cultivo",
        "SEEDING_DATE": "Data de plantio",
        "HARVESTING_DATE": "Data de colheita",
        "CONTRACT_EXPIRE_DATE": "Data de vencimento do contrato",
        "SELECT": "Selecionar",
        "SEARCHING_REFBACEN": `Buscando REFBACEN ${key.value || ""}`,
        "REFBACEN_NOT_FOUND": "REFBACEN_NOT_FOUND",
        "SOIL_TYPE_CROP_GROUP_INFO": "Informações sobre tipo de solo e grupo de cultivo",
        "SOIL_SANDY": "Arenoso",
        "SOIL_AVERAGE": "Médio",
        "SOIL_CLAYISH": "Argiloso",
        "GROUP_N": `Grupo ${key.group || ""}`,
        "PERENNIAL": "Perene",
        "SOYBEAN": "Soja",
        "CORN": "Milho",
        "COTTON": "Algodão",
        "WHEAT": "Trigo",
        "COFFEE": "Café",
        "SUGAR_CANE": "Cana-de-açúcar"
      };
      return translations[key] || key;
    }
  })
}));

jest.mock("@services", () => ({
  getRefBacenInfo: jest.fn(),
}));

jest.mock("@utils", () => ({
  getCropYears: jest.fn(() => [
    { label: "1ª Safra 2023/2024", value: "2023/2024" },
    { label: "2ª Safra 2024/2024", value: "2024/2024" },
  ]),
  getSoilByCropYear: jest.fn(() => ["SANDY", "AVERAGE", "CLAYISH"]),
  getInputStatus: jest.fn(() => ""),
  Crop: {
    SOYBEAN: "SOYBEAN",
    CORN: "CORN",
    COTTON: "COTTON",
    WHEAT: "WHEAT",
    COFFEE: "COFFEE",
    SUGAR_CANE: "SUGAR_CANE",
  },
  SoilType: {
    AD1: "AD1",
    AD2: "AD2",
    AD3: "AD3",
    AD4: "AD4",
    AD5: "AD5",
    AD6: "AD6",
    SANDY: "SANDY",
    AVERAGE: "AVERAGE",
    CLAYISH: "CLAYISH",
  },
}));

jest.mock("antd", () => {
  const original = jest.requireActual("antd");
  return {
    ...original,
    notification: {
      info: jest.fn(),
      warn: jest.fn(),
    },
  };
});

jest.mock("use-debounce", () => ({
  useDebouncedCallback: (fn) => fn, // Make debounced functions execute immediately
}));

jest.mock("uuid", () => ({
  v4: jest.fn(() => "test-uuid"),
}));

jest.mock("../../../assets/data/cities.json", () => [
  { ibge_code: 1, name: "São Paulo", uf: "SP" },
  { ibge_code: 2, name: "Rio de Janeiro", uf: "RJ" },
]);

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe("InspectionForm component", () => {
  const defaultProps = {
    isSubmitted: false,
    data: {
      contact: { id: 1, name: "Test Contact" },
      data: {},
    },
    properties: [],
    subareas: [],
    onTypeChange: jest.fn(),
    onChange: jest.fn(),
    onRefBacenGeometryLoad: jest.fn(),
    onRefBacenSubareasLoad: jest.fn(),
  };

  it("should render the component", () => {
    render(<InspectionForm {...defaultProps} />);
    const component = screen.queryByTestId("pb-t-inspection-form");
    expect(component).toBeInTheDocument();
    expect(screen.getByText("Resolução BACEN")).toBeInTheDocument();
    expect(screen.getByText("Padrão")).toBeInTheDocument();
  });

  it("should call onTypeChange when component mounts", () => {
    render(<InspectionForm {...defaultProps} />);
    expect(defaultProps.onTypeChange).toHaveBeenCalledWith(ReportType.INSPECTION_FINANCIAL);
  });

  it("should switch between report types", () => {
    render(<InspectionForm {...defaultProps} />);

    expect(screen.getByText("Referência BACEN")).toBeInTheDocument();
    expect(screen.getByText("Instituição financeira")).toBeInTheDocument();

    fireEvent.click(screen.getByText("Padrão"));
    expect(defaultProps.onTypeChange).toHaveBeenCalledWith(ReportType.INSPECTION);

    cleanup();

    render(
      <InspectionForm
        {...defaultProps}
        data={{
          ...defaultProps.data,
          data: {
            ...defaultProps.data.data,
            reportType: ReportType.INSPECTION
          }
        }}
      />
    );

    const inspectionForm = screen.getByTestId("pb-t-inspection-form");
    expect(inspectionForm).toBeInTheDocument();

    cleanup();
    const { rerender } = render(
      <InspectionForm
        {...defaultProps}
        data={{
          ...defaultProps.data,
          data: { reportType: ReportType.INSPECTION }
        }}
      />
    );

    rerender(
      <InspectionForm
        {...defaultProps}
        data={{
          ...defaultProps.data,
          data: { reportType: ReportType.INSPECTION }
        }}
      />
    );

    fireEvent.click(screen.getByText("Resolução BACEN"));
    expect(defaultProps.onTypeChange).toHaveBeenCalledWith(ReportType.INSPECTION_FINANCIAL);

    cleanup();
    render(<InspectionForm {...defaultProps} />);

    expect(screen.getByText("Referência BACEN")).toBeInTheDocument();
    expect(screen.getByText("Instituição financeira")).toBeInTheDocument();
  });

  it("should handle field changes", () => {
    render(<InspectionForm {...defaultProps} />);

    const financialInstitutionLabel = screen.getByText("Instituição financeira");
    const financialInstitutionInput = financialInstitutionLabel.parentElement.querySelector('input');

    expect(financialInstitutionInput).toBeInTheDocument();
    fireEvent.change(financialInstitutionInput, { target: { value: "Test Bank" } });

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      contact: { id: 1, name: "Test Contact" },
      data: {
        financialInstitution: "Test Bank",
      },
    });
  });

  it("should show validation errors when submitted", () => {
    render(
      <InspectionForm
        {...defaultProps}
        isSubmitted={true}
      />
    );

    const bacenReferenceLabel = screen.getByText("Referência BACEN");
    const bacenReferenceInput = bacenReferenceLabel.parentElement.querySelector('input');

    expect(bacenReferenceInput.className).toContain("ant-input"); // Basic check that input exists

    const errorInfo = screen.queryAllByText(/min/i);
    expect(errorInfo.length).toBe(0); // No error message yet since we're just checking the input exists
  });

  it("should handle BACEN reference search", async () => {
    const bacenData = {
      crop: "SOYBEAN",
      crop_year: "2023/2024",
      crop_group: "GROUP I",
      municipalities: [1],
      total_area: "100",
      soil_type: "SANDY",
      start_planting_date: "2023-10-01",
      start_harvest_date: "2024-03-01",
      if_name: "Test Bank",
      geometry: { type: "Polygon", coordinates: [[]] },
      sub_areas: [{ type: "Polygon", coordinates: [[]] }],
    };

    (getRefBacenInfo as jest.Mock).mockResolvedValue(bacenData);

    render(<InspectionForm {...defaultProps} />);

    const bacenReferenceLabel = screen.getByText("Referência BACEN");
    const bacenInput = bacenReferenceLabel.parentElement.querySelector('input');
    expect(bacenInput).toBeInTheDocument();

    fireEvent.change(bacenInput, { target: { value: "*********" } });

    expect(getRefBacenInfo).toHaveBeenCalledWith("*********-1");
    expect(notification.info).toHaveBeenCalled();

    expect(defaultProps.onChange).toHaveBeenCalled();

    const lastCallArgs = (defaultProps.onChange as jest.Mock).mock.calls.slice(-1)[0][0];

    expect(lastCallArgs.data).toEqual(expect.objectContaining({
      refBacen: "*********",
    }));

  });

  it("should handle BACEN reference not found", async () => {
    (getRefBacenInfo as jest.Mock).mockResolvedValue(null);

    render(<InspectionForm {...defaultProps} />);

    const bacenReferenceLabel = screen.getByText("Referência BACEN");
    const bacenInput = bacenReferenceLabel.parentElement.querySelector('input');
    expect(bacenInput).toBeInTheDocument();

    fireEvent.change(bacenInput, { target: { value: "*********" } });

    expect(getRefBacenInfo).toHaveBeenCalledWith("*********-1");

    await waitFor(() => {
      expect(notification.warn).toHaveBeenCalledWith({
        message: "REFBACEN_NOT_FOUND",
      });
    });
  });

  it("should handle BACEN reference search error", async () => {
    (getRefBacenInfo as jest.Mock).mockRejectedValue(new Error("API error"));

    render(<InspectionForm {...defaultProps} />);

    const bacenReferenceLabel = screen.getByText("Referência BACEN");
    const bacenInput = bacenReferenceLabel.parentElement.querySelector('input');
    expect(bacenInput).toBeInTheDocument();

    fireEvent.change(bacenInput, { target: { value: "*********" } });

    expect(getRefBacenInfo).toHaveBeenCalledWith("*********-1");

    await waitFor(() => {
      expect(notification.warn).toHaveBeenCalledWith({
        message: "REFBACEN_NOT_FOUND",
      });
    });
  });

  it("should filter municipalities when searching", async () => {
    render(<InspectionForm {...defaultProps} />);

    const municipalityLabel = screen.getByText("Município");
    expect(municipalityLabel).toBeInTheDocument();


    expect(screen.queryByText("São Paulo - SP")).not.toBeInTheDocument(); // Not visible until dropdown is opened

  });

  it("should disable fields when contact is not provided", () => {
    render(
      <InspectionForm
        {...defaultProps}
        data={{ data: {} }} // No contact
      />
    );

    const inputs = screen.getAllByRole("textbox");
    inputs.forEach(input => {
      expect(input).toBeDisabled();
    });

    const radioButtons = screen.getAllByRole("radio");
    radioButtons.forEach(radio => {
      expect(radio).toBeDisabled();
    });
  });

  it("should show soil type and crop group info message when fields are not filled", () => {

    render(<InspectionForm {...defaultProps} />);

    const infoElement = screen.getByText('Informações sobre tipo de solo e grupo de cultivo');
    expect(infoElement).toBeInTheDocument();

    const updatedProps = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        data: {
          soilType: "SANDY",
          cropGroup: "GROUP I",
        },
      },
    };

    cleanup();
    render(<InspectionForm {...updatedProps} />);

    expect(screen.queryByText('Informações sobre tipo de solo e grupo de cultivo')).not.toBeInTheDocument();
  });

  it("should test field validation with submitted form", () => {
    render(
      <InspectionForm
        {...defaultProps}
        isSubmitted={true}
        data={{
          ...defaultProps.data,
          data: {
            refBacen: "12345", // Too short, should fail validation
          },
        }}
      />
    );

    const errorInfo = screen.getByText("O campo deve possuir pelo menos 9 caracteres.");
    expect(errorInfo).toBeInTheDocument();
  });

  it("should handle total area input change", () => {
    render(<InspectionForm {...defaultProps} />);

    const totalAreaLabel = screen.getByText("Área total financiada");
    expect(totalAreaLabel).toBeInTheDocument();

    const totalAreaInput = totalAreaLabel.parentElement.querySelector('input');
    expect(totalAreaInput).toBeInTheDocument();

    fireEvent.change(totalAreaInput, { target: { value: "100" } });

    expect(defaultProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          totalArea: "100"
        })
      })
    );
  });

  it("should handle estimated crop yield input change", () => {
    render(<InspectionForm {...defaultProps} />);

    const estimatedYieldLabel = screen.getByText("Produtividade estimada");
    expect(estimatedYieldLabel).toBeInTheDocument();

    const estimatedYieldInput = estimatedYieldLabel.parentElement.querySelector('input');
    expect(estimatedYieldInput).toBeInTheDocument();

    fireEvent.change(estimatedYieldInput, { target: { value: "50" } });

    expect(defaultProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          estimatedCropYield: "50"
        })
      })
    );
  });

  it("should test form with soil type and crop data", () => {
    render(
      <InspectionForm
        {...defaultProps}
        data={{
          ...defaultProps.data,
          data: {
            crop: "SOYBEAN",
            cropYear: "2023/2024",
            soilType: "SANDY",
            cropGroup: "GROUP I"
          }
        }}
      />
    );

    const soilTypeLabel = screen.getByText("Tipo de solo");
    const cropGroupLabel = screen.getByText("Grupo de cultivo");

    expect(soilTypeLabel).toBeInTheDocument();
    expect(cropGroupLabel).toBeInTheDocument();

    expect(screen.queryByText('Informações sobre tipo de solo e grupo de cultivo')).not.toBeInTheDocument();
  });

  it("should render date pickers", () => {
    render(<InspectionForm {...defaultProps} />);

    const seedingDateLabel = screen.getByText("Data de plantio");
    const harvestingDateLabel = screen.getByText("Data de colheita");
    const contractExpireDateLabel = screen.getByText("Data de vencimento do contrato");

    expect(seedingDateLabel).toBeInTheDocument();
    expect(harvestingDateLabel).toBeInTheDocument();
    expect(contractExpireDateLabel).toBeInTheDocument();
  });

  it("should test BACEN reference with existing property", () => {
    const bacenData = {
      crop: "SOYBEAN",
      crop_year: "2023/2024",
      crop_group: "GROUP I",
      municipalities: [1],
      total_area: "100",
      soil_type: "SANDY",
      start_planting_date: "2023-10-01",
      start_harvest_date: "2024-03-01",
      if_name: "Test Bank",
      geometry: { type: "Polygon", coordinates: [[]] },
      sub_areas: [{ type: "Polygon", coordinates: [[]] }],
    };

    (getRefBacenInfo as jest.Mock).mockResolvedValue(bacenData);

    const existingProperty = {
      id: "existing-id",
      area_name: "Bacen *********",
      area_coordinates: { type: "Polygon", coordinates: [[]] },
      origin: "MANUAL",
      car: "",
      isInternal: true,
    };

    render(
      <InspectionForm
        {...defaultProps}
        properties={[existingProperty]}
      />
    );

    const bacenReferenceLabel = screen.getByText("Referência BACEN");
    const bacenInput = bacenReferenceLabel.parentElement.querySelector('input');

    fireEvent.change(bacenInput, { target: { value: "*********" } });

    expect(getRefBacenInfo).toHaveBeenCalledWith("*********-1");

    const lastCallArgs = (defaultProps.onChange as jest.Mock).mock.calls.slice(-1)[0][0];
    expect(lastCallArgs.data).toEqual(expect.objectContaining({
      refBacen: "*********",
    }));
  });

  it("should handle BACEN reference with geometry data", async () => {
    const bacenData = {
      crop: "SOYBEAN",
      crop_year: "2023/2024",
      crop_group: "GROUP I",
      municipalities: [1],
      total_area: "100",
      soil_type: "SANDY",
      start_planting_date: "2023-10-01",
      start_harvest_date: "2024-03-01",
      if_name: "Test Bank",
      geometry: { type: "Polygon", coordinates: [[]] },
      sub_areas: [{ type: "Polygon", coordinates: [[]] }],
    };

    (getRefBacenInfo as jest.Mock).mockResolvedValue(bacenData);

    render(<InspectionForm {...defaultProps} />);

    const bacenReferenceLabel = screen.getByText("Referência BACEN");
    const bacenInput = bacenReferenceLabel.parentElement.querySelector('input');

    fireEvent.change(bacenInput, { target: { value: "*********" } });

    expect(getRefBacenInfo).toHaveBeenCalledWith("*********-1");

    expect(defaultProps.onChange).toHaveBeenCalled();

    const lastCallArgs = (defaultProps.onChange as jest.Mock).mock.calls.slice(-1)[0][0];
    expect(lastCallArgs.data).toEqual(expect.objectContaining({
      refBacen: "*********",
    }));
  });

  it("should test field validation with eq validator", () => {
    const mockData = {
      ...defaultProps.data,
      data: {
        refBacen: "*********01", // 11 characters, should pass eq validation
      },
    };

    render(
      <InspectionForm
        {...defaultProps}
        isSubmitted={true}
        data={mockData}
      />
    );

    expect(screen.queryByText("O campo deve possuir pelo menos 9 caracteres.")).not.toBeInTheDocument();
  });

  it("should render municipality field", () => {
    render(<InspectionForm {...defaultProps} />);

    const municipalityLabel = screen.getByText("Município");
    expect(municipalityLabel).toBeInTheDocument();

    expect(municipalityLabel).toBeInTheDocument();
  });

  it("should handle institution responsible field change", () => {
    render(
      <InspectionForm
        {...defaultProps}
        data={{
          ...defaultProps.data,
          data: { reportType: ReportType.INSPECTION }
        }}
      />
    );

    const institutionOfficerLabel = screen.queryByText("Reponsável pela instituição");
    expect(institutionOfficerLabel).toBeInTheDocument();

    const institutionOfficerInput = institutionOfficerLabel.parentElement.querySelector('input');
    expect(institutionOfficerInput).toBeInTheDocument();

    fireEvent.change(institutionOfficerInput, { target: { value: "John Doe" } });

    expect(defaultProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          institutionResponsible: "John Doe"
        })
      })
    );
  });

  it("should handle financial institution field change", () => {
    render(<InspectionForm {...defaultProps} />);

    const financialInstitutionLabel = screen.getByText("Instituição financeira");
    expect(financialInstitutionLabel).toBeInTheDocument();

    const financialInstitutionInput = financialInstitutionLabel.parentElement.querySelector('input');
    expect(financialInstitutionInput).toBeInTheDocument();

    fireEvent.change(financialInstitutionInput, { target: { value: "Bank of Testing" } });

    expect(defaultProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          financialInstitution: "Bank of Testing"
        })
      })
    );
  });
});
