import { FIELD_VALIDATION_ERROR_MESSAGE, FIELD_VALIDATORS } from "@constants";
import { getRefBacenInfo } from "@services";
import { ReportFormProps, ReportType } from "@types";
import {
  Crop,
  getCropYears,
  getInputStatus,
  getSoilByCropYear,
  SoilType,
} from "@utils";
import { notification, Radio } from "antd";
import { DatePicker, Input, Select } from "brainui";
import moment, { Moment } from "moment";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDebouncedCallback } from "use-debounce";
import { v4 as uuidv4 } from "uuid";
import municipalities from "../../../assets/data/cities.json";
import {
  ErrorInfo,
  FieldWrapper,
  InfoRow,
  ThreeColumns,
  TwoColumns,
  Wrapper,
} from "./InspectionForm.styles";

type InspectionFormProps = {
  readonly properties: any[];
  readonly subareas: any[];
  readonly onTypeChange: (type: ReportType) => void;
  readonly onRefBacenGeometryLoad: (property) => void;
  readonly onRefBacenSubareasLoad: (subareas) => void;
} & ReportFormProps;

function InspectionForm({
  isSubmitted,
  data,
  properties,
  subareas,
  onTypeChange,
  onChange,
  onRefBacenGeometryLoad,
  onRefBacenSubareasLoad,
}: InspectionFormProps) {
  const { t } = useTranslation();
  const [reportType, setReportType] = useState<
    ReportType.INSPECTION | ReportType.INSPECTION_FINANCIAL
  >(ReportType.INSPECTION_FINANCIAL);
  const [cropYears, setCropYears] = useState([]);
  const [filteredMunicipalities, setFilteredMunicipalities] =
    useState(municipalities);

  const SOIL_TYPES = [
    { value: SoilType.AD1, label: "AD1" },
    { value: SoilType.AD2, label: "AD2" },
    { value: SoilType.AD3, label: "AD3" },
    { value: SoilType.AD4, label: "AD4" },
    { value: SoilType.AD5, label: "AD5" },
    { value: SoilType.AD6, label: "AD6" },
    { value: SoilType.SANDY, label: t("SOIL_SANDY") },
    { value: SoilType.AVERAGE, label: t("SOIL_AVERAGE") },
    { value: SoilType.CLAYISH, label: t("SOIL_CLAYISH") },
  ];

  const CROP_GROUP = [
    { value: "GROUP I", label: t("GROUP_N", { group: "I" }) },
    { value: "GROUP II", label: t("GROUP_N", { group: "II" }) },
    { value: "GROUP III", label: t("GROUP_N", { group: "III" }) },
    { value: "GROUP IV", label: t("GROUP_N", { group: "IV" }) },
    { value: "GROUP V", label: t("GROUP_N", { group: "V" }) },
    { value: "PERENNIAL", label: t("PERENNIAL") },
  ];

  const REFBACEN_DATA_TO_FIELD = {
    crop: "crop",
    crop_year: "cropYear",
    crop_group: "cropGroup",
    municipalities: "municipality",
    total_area: "totalArea",
    soil_type: "soilType",
    start_planting_date: "seedingDate",
    start_harvest_date: "harvestingDate",
    if_name: "financialInstitution",
  };

  useEffect(() => {
    const _cropYears = getCropYears();
    setCropYears(_cropYears);
    onTypeChange(reportType);
  }, []);

  function handleFieldChange(fieldValue, field: string) {
    const newValue = {
      ...data,
      data: {
        ...(data.data || {}),
        [field]: fieldValue,
      },
    };
    onChange && onChange(newValue);
  }

  function getErrorMessage(field: string, value?: number | string): string {
    return FIELD_VALIDATION_ERROR_MESSAGE[field].replace("{value}", value);
  }

  function isFieldValid(field: string): boolean {
    return FIELD_VALIDATORS[field].every((v) => {
      return Object.keys(v).every((key) => {
        if (key == "min")
          return ((data.data || {})[field] || "").length >= v[key];
        if (key == "eq")
          return ((data.data || {})[field] || "").length == v[key];
      });
    });
  }

  async function handleFetchBacen(refBacen: string) {
    try {
      notification.info({
        message: t("SEARCHING_REFBACEN", { value: refBacen }),
      });
      const refBacenParam = refBacen.length == 9 ? `${refBacen}-1` : refBacen;
      const bacenData = await getRefBacenInfo(refBacenParam);
      if (!bacenData) {
        notification.warn({
          message: t("REFBACEN_NOT_FOUND"),
        });
        return;
      }

      let newData = { ...(data.data || {}), refBacen };
      Object.keys(bacenData)
        .filter(
          (key) => bacenData[key] && !["geometry", "sub_areas"].includes(key)
        )
        .forEach((key) => {
          newData[REFBACEN_DATA_TO_FIELD[key]] = bacenData[key];
        });
      let newValue = { ...data, data: newData };
      const geometryName = `Bacen ${refBacen}`;

      let property = properties.find((x) => x.area_name == geometryName);
      // Verifica se existe para não remover a propriedade selecionada com undefined
      if (property) {
        newValue.property = property;
      }

      if (bacenData.geometry && !property) {
        property = {
          id: uuidv4(),
          area_name: geometryName,
          area_coordinates: bacenData.geometry,
          origin: "MANUAL",
          car: "",
          isInternal: true,
        };
        newValue.property = property;
        onRefBacenGeometryLoad(property);
      }

      if (bacenData.sub_areas) {
        const newSubareas = bacenData.sub_areas
          .map((subarea, idx) => {
            const name = `Bacen ${refBacen} (${idx + 1})`;
            const id = uuidv4();
            return {
              id,
              name,
              label: name,
              area_name: name,
              area_coordinates: subarea,
              coordinates: subarea,
              origin: "MANUAL",
              isInternal: true,
            };
          })
          .filter(
            (x) => !subareas.some((subarea) => subarea.area_name == x.area_name)
          );
        if (newSubareas.length) {
          newValue.subareas = newSubareas;
          onRefBacenSubareasLoad(newSubareas);
        }
      }
      onChange && onChange(newValue);
    } catch {
      notification.warn({
        message: t("REFBACEN_NOT_FOUND"),
      });
    }
  }

  const debouncedBacenSearch = useDebouncedCallback((refBacen) => {
    if (refBacen.length == 9 || refBacen.length == 11) {
      handleFetchBacen(refBacen);
    }
  }, 2000);

  async function handleRefBacen(refBacen: string) {
    handleFieldChange(refBacen, "refBacen");
    debouncedBacenSearch(refBacen);
  }

  const onMunicipalitySearch = useDebouncedCallback(async (query: string) => {
    const filtered = municipalities.filter((x) =>
      x.name.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredMunicipalities(filtered);
  }, 50);

  return (
    <Wrapper data-testid="pb-t-inspection-form">
      <Radio.Group
        size="small"
        value={reportType}
        onChange={(e) => {
          setReportType(e.target.value);
          onTypeChange && onTypeChange(e.target.value);
        }}
        disabled={!data.contact}
      >
        <Radio.Button value={ReportType.INSPECTION_FINANCIAL}>
          {t("BACEN_REPORT")}
        </Radio.Button>
        <Radio.Button value={ReportType.INSPECTION}>
          {t("STANDART")}
        </Radio.Button>
      </Radio.Group>
      <TwoColumns>
        {reportType == ReportType.INSPECTION_FINANCIAL && (
          <FieldWrapper>
            <Input
              className="label-input"
              label={t("BACEN_REFERENCE")}
              value={data.data?.refBacen}
              onChange={({ target }) => {
                const refBacen = target.value.replace(/[^\d]/g, "");
                handleRefBacen(refBacen);
              }}
              status={isSubmitted && !data.data?.refBacen ? "error" : ""}
              maxLength={11}
              disabled={!data.contact}
            />
            {isSubmitted && !isFieldValid("refBacen") && (
              <ErrorInfo>{getErrorMessage("min", 9)}</ErrorInfo>
            )}
          </FieldWrapper>
        )}
        {reportType == ReportType.INSPECTION && (
          <FieldWrapper>
            <Input
              className="label-input"
              label={t("CONTRACT_NAME")}
              value={data.data?.contractNumber}
              onChange={({ target }) => {
                handleFieldChange(target.value, "contractNumber");
              }}
              status={isSubmitted && !data.data?.contractNumber ? "error" : ""}
              maxLength={255}
              disabled={!data.contact}
            />
          </FieldWrapper>
        )}
        {reportType == ReportType.INSPECTION_FINANCIAL && (
          <FieldWrapper>
            <Input
              className="label-input"
              label={t("FINANCIAL_INSTITUTION")}
              value={data.data?.financialInstitution}
              onChange={({ target }) => {
                handleFieldChange(target.value, "financialInstitution");
              }}
              status={
                isSubmitted && !isFieldValid("financialInstitution")
                  ? "error"
                  : ""
              }
              maxLength={256}
              disabled={!data.contact}
            />
            {isSubmitted && !isFieldValid("financialInstitution") && (
              <ErrorInfo>{getErrorMessage("min", 3)}</ErrorInfo>
            )}
          </FieldWrapper>
        )}
        {reportType == ReportType.INSPECTION && (
          <FieldWrapper>
            <Input
              className="label-input"
              label={t("INSTITUTION_NAME")}
              value={data.data?.institutionName}
              onChange={({ target }) => {
                handleFieldChange(target.value, "institutionName");
              }}
              status={
                isSubmitted && !isFieldValid("institutionName") ? "error" : ""
              }
              maxLength={256}
              disabled={!data.contact}
            />
            {isSubmitted && !isFieldValid("institutionName") && (
              <ErrorInfo>{getErrorMessage("min", 3)}</ErrorInfo>
            )}
          </FieldWrapper>
        )}
      </TwoColumns>

      <TwoColumns>
        <FieldWrapper>
          <Input
            className="label-input"
            label={t("INSTITUTION_OFFICER")}
            value={data.data?.institutionResponsible}
            onChange={({ target }) => {
              handleFieldChange(target.value, "institutionResponsible");
            }}
            maxLength={256}
            disabled={!data.contact}
          />
        </FieldWrapper>
        <Select
          mode="multiple"
          onChange={(munCode: number) => {
            handleFieldChange(munCode, "municipality");
          }}
          onSearch={onMunicipalitySearch}
          label={t("MUNICIPALITY")}
          value={(data.data?.municipality || []).map((m) => {
            const municipality = municipalities.find((x) => x.ibge_code == m);
            return {
              label: `${municipality.name} - ${municipality.uf}`,
              value: m,
              key: m,
            };
          })}
          status={isSubmitted && !data.data?.municipality ? "error" : ""}
          optionLabelProp="label"
          filterOption={false}
          disabled={
            !data.contact ||
            (data.data?.refBacen?.length == 11 &&
              data.data?.municipality?.length)
          }
        >
          {filteredMunicipalities.map((mun) => (
            <Select.Option
              key={mun.ibge_code}
              label={`${mun.name} - ${mun.uf}`}
              value={mun.ibge_code}
              disabled={
                data.municipality &&
                data.municipality.length >= 5 &&
                !data.municipality.includes(mun.ibge_code)
              }
            >
              {mun.name} - {mun.uf}
            </Select.Option>
          ))}
        </Select>
      </TwoColumns>

      <TwoColumns>
        <Select
          onSelect={(cropYear: string) => {
            handleFieldChange(cropYear, "cropYear");
          }}
          label={t("FINANCED_CROP_YEAR")}
          value={data.data?.cropYear}
          status={isSubmitted && !data.data?.cropYear ? "error" : ""}
          options={cropYears}
          disabled={!data.contact}
        />
        <Select
          defaultValue={data.crop}
          label={t("FINANCED_CROP")}
          value={data.data?.crop}
          options={Object.values(Crop).map((c) => ({ value: c, label: t(c) }))}
          onSelect={(selectedCrop: string) => {
            handleFieldChange(selectedCrop, "crop");
          }}
          status={isSubmitted && !data.data?.crop ? "error" : ""}
          disabled={!data.contact}
        />
      </TwoColumns>
      <TwoColumns>
        <Input
          className="label-input"
          type="number"
          label={t("TOTAL_FINANCED_AREA")}
          value={data.data?.totalArea}
          onChange={({ target }) => {
            handleFieldChange(target.value.slice(0, 11), "totalArea");
          }}
          status={isSubmitted && !data.data?.totalArea ? "error" : ""}
          disabled={!data.contact}
        />
        <Input
          className="label-input"
          type="number"
          label={t("ESTIMATED_CROP_YIELD")}
          value={data.data?.estimatedCropYield}
          onChange={({ target }) => {
            handleFieldChange(target.value.slice(0, 11), "estimatedCropYield");
          }}
          disabled={!data.contact}
        />
      </TwoColumns>

      <TwoColumns>
        <Select
          onSelect={(soilType: string) => {
            handleFieldChange(soilType, "soilType");
          }}
          label={t("SOIL_TYPE")}
          value={data.data?.soilType}
          status={getInputStatus({
            reportType,
            fieldName: "soilType",
            objectWithField: data.data,
            isSubmitted,
          })}
          options={SOIL_TYPES.map((soil) => {
            const allowedSoilTypes = getSoilByCropYear(
              data.data?.crop,
              data.data?.cropYear
            );
            return {
              ...soil,
              disabled: !allowedSoilTypes.includes(soil.value),
            };
          })}
          disabled={!data.data?.cropYear || !data.data?.crop || !data.contact}
        />
        <Select
          onSelect={(cropGroup: string) => {
            handleFieldChange(cropGroup, "cropGroup");
          }}
          label={t("CROP_GROUP")}
          value={data.data?.cropGroup}
          options={CROP_GROUP}
          disabled={!data.contact}
          status={getInputStatus({
            fieldName: "cropGroup",
            objectWithField: data.data,
            reportType,
            isSubmitted,
          })}
        />
      </TwoColumns>
      {(!data.data?.cropGroup || !data.data?.soilType) && (
        <InfoRow>{t("SOIL_TYPE_CROP_GROUP_INFO")}</InfoRow>
      )}
      <ThreeColumns>
        <DatePicker
          className="date-picker"
          label={t("SEEDING_DATE")}
          format="DD/MM/YYYY"
          placeholder={t("SELECT")}
          value={
            data.data?.seedingDate
              ? moment(data.data.seedingDate, "YYYY-MM-DD")
              : undefined
          }
          onChange={(date: Moment) => {
            handleFieldChange(date?.format("YYYY-MM-DD"), "seedingDate");
          }}
          disabled={!data.contact}
        />
        <DatePicker
          className="date-picker"
          label={t("HARVESTING_DATE")}
          placeholder={t("SELECT")}
          format="DD/MM/YYYY"
          value={
            data.data?.harvestingDate
              ? moment(data.data.harvestingDate, "YYYY-MM-DD")
              : undefined
          }
          onChange={(date: Moment) => {
            handleFieldChange(date?.format("YYYY-MM-DD"), "harvestingDate");
          }}
          disabled={!data.contact}
        />
        <DatePicker
          className="date-picker"
          label={t("CONTRACT_EXPIRE_DATE")}
          placeholder={t("SELECT")}
          format="DD/MM/YYYY"
          value={
            data.data?.contractExpirationDate
              ? moment(data.data.contractExpirationDate, "YYYY-MM-DD")
              : undefined
          }
          onChange={(date: Moment) => {
            handleFieldChange(
              date?.format("YYYY-MM-DD"),
              "contractExpirationDate"
            );
          }}
          disabled={!data.contact}
        />
      </ThreeColumns>
    </Wrapper>
  );
}

export default InspectionForm;
