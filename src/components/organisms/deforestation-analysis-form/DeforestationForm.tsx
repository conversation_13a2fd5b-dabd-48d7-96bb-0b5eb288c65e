import { ReportFormProps, ReportType } from "@types";
import { Typography, InputNumber } from "antd";
import { DatePicker } from "brainui";
import moment, { Moment } from "moment";
import { useTranslation } from "react-i18next";
import { DateInputContainer, InputsRow } from "./DeforestationForm.styles";
import { ReportRequestValue } from "../report-request-form/ReportRequestForm";
import { useEffect, useState } from "react";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import { CheckboxDropdown } from "../../molecules/checkbox-dropdown/CheckboxDropdown";
import {
  useSocioEnvironmentCriterias,
  DEFORESTATION_CRITERIAS_WITHOUT_CUTOFFDATE,
  DEFORESTATION_CRITERIAS_WITH_MIN_AREA,
} from "@/hooks/use-socio-environment-criterias";

const { Text } = Typography;

function DeforestationForm({
  data,
  onChange,
}: ReportFormProps<ReportRequestValue>) {
  const { t } = useTranslation();
  const { deforestationCriterias, handleSetSocioEnvironmentalCriterias } =
    useSocioEnvironmentCriterias();
  const [isCheckAllSelected, setIsCheckAllSelected] = useState(false);

  function handleChange() {
    if (!onChange) return;

    const final = deforestationCriterias
      .filter((item) => item.checked === true)
      .map(({ value }) => {
        const exists = data.data?.queries?.find((array) => {
          return array.some((object) => object.value === value);
        });

        if (exists) {
          return exists;
        }

        return [{ label: "QUERY", value, is_parent: true }];
      });

    onChange({ ...data, data: { ...data.data, queries: final } });
  }

  function handleChangeCutoffDate(
    date: Moment,
    item: {
      label: string;
      value: string;
      is_parent?: boolean;
    }[]
  ) {
    let formatted: string = "";
    if (date) {
      formatted = date.format("YYYY-MM-DD");
    }

    const parent = item.find((value) => value.is_parent === true);

    const criteriasUpdated = data.data?.queries?.map((array) => {
      const parentExists = array.find((value) => value.label === parent.label);
      const dateObjectExists = array.find(
        (value) => value.label === "CUTOFF_DATE"
      );

      const isSameParent = parentExists?.value === parent?.value;

      if (!isSameParent) {
        return array;
      }

      if (!dateObjectExists) {
        return [...array, { label: "CUTOFF_DATE", value: formatted }];
      } else {
        return array.map(value => {
          if (value.label === "CUTOFF_DATE") {
            return { ...value, value: formatted };
          }
          return value;
        });
      }
    });

    if (!onChange) return;

    const result: ReportRequestValue = {
      ...data,
      data: { ...data.data, queries: criteriasUpdated },
    };

    onChange(result);
  }

  function handleChangeCheckboxItem(
    event: CheckboxChangeEvent,
    checkboxItem: (typeof deforestationCriterias)[0]
  ) {
    if (isCheckAllSelected) {
      setIsCheckAllSelected(false);
      handleSetSocioEnvironmentalCriterias({
        deforestationCriterias: deforestationCriterias.map((item) => {
          if (checkboxItem.value === item.value) {
            return { ...item, checked: false };
          }
          return item;
        }),
      });
      return;
    }

    const mapped = deforestationCriterias.map((item) => {
      if (checkboxItem.value === item.value) {
        return { ...item, checked: event.target.checked };
      }
      return item;
    });

    handleSetSocioEnvironmentalCriterias({ deforestationCriterias: mapped });
  }

  function handleChangeCheckAll(event: CheckboxChangeEvent) {
    setIsCheckAllSelected(event.target.checked);
    const isCheckedAll = event.target.checked === true;
    handleSetSocioEnvironmentalCriterias({
      deforestationCriterias: deforestationCriterias.map((item) => ({
        ...item,
        checked: isCheckedAll,
      })),
    });
  }



  function handleChangeMinArea(
    minArea: number | null,
    item: {
      label: string;
      value: string;
      is_parent?: boolean;
    }[]
  ) {
    const parent = item.find((value) => value.is_parent === true);

    const criteriasUpdated = data.data?.queries?.map((array) => {
      const parentExists = array.find((value) => value.label === parent.label);
      const minAreaObjectExists = array.find(
        (value) => value.label === "MIN_AREA"
      );

      const isSameParent = parentExists?.value === parent?.value;

      if (!isSameParent) {
        return array;
      }

      if (minArea === null || minArea === undefined) {
        return array.filter((value) => value.label !== "MIN_AREA");
      }

      if (minAreaObjectExists) {
        return array.map((value) => {
          if (value.label === "MIN_AREA") {
            return { ...value, value: minArea.toString() };
          }
          return value;
        });
      }

      return [...array, { label: "MIN_AREA", value: minArea.toString() }];
    });

    if (!onChange) return;

    const result: ReportRequestValue = {
      ...data,
      data: { ...data.data, queries: criteriasUpdated },
    };

    onChange(result);
  }

  useEffect(() => {
    handleChange();
  }, [deforestationCriterias]);

  return (
    <div data-testid="pb-t-deforestation-form">
      <CheckboxDropdown
        checkboxItems={deforestationCriterias}
        isCheckAllSelected={isCheckAllSelected}
        onChangeCheckAll={handleChangeCheckAll}
        onChangeCheckboxItem={handleChangeCheckboxItem}
        placeholder={t("SOCIOENVIRONMENTAL_CRITERIA")}
        onCloseDropdown={handleChange}
      />
      {data.data?.queries?.length > 0 &&
        data.data?.queries
          ?.map((items) => {
            const dateObject = items.find(
              (value) => value.label === "CUTOFF_DATE"
            );
            const minAreaObject = items.find(
              (value) => value.label === "MIN_AREA"
            );
            const parent = items.find((value) => value.is_parent === true);
            const needsMinArea = DEFORESTATION_CRITERIAS_WITH_MIN_AREA.includes(parent?.value);
            const needsCutoffDate = !DEFORESTATION_CRITERIAS_WITHOUT_CUTOFFDATE.includes(parent?.value);

            if (!needsMinArea && !needsCutoffDate) {
              return null;
            }

            const key = JSON.stringify(items);

            return (
              <DateInputContainer key={key}>
                <Text type="secondary" color="#262626">
                  {t(parent?.value || "ERROR")}
                </Text>
                <InputsRow>
                  {needsMinArea && (
                    <InputNumber
                      className="min-area-input"
                      style={{ width: "248px" }}
                      placeholder={t("MIN_AREA_HA")}
                      min={0}
                      step={1}
                      defaultValue={minAreaObject?.value || ""}
                      decimalSeparator=','
                      formatter={value => value.replace('.', ',')}
                      onKeyDown={(e) => {
                        const allowedKeys = [
                          "Backspace",
                          "ArrowLeft",
                          "ArrowRight",
                          "Delete",
                          "Tab",
                          "Enter",
                        ];
                        if (
                          !allowedKeys.includes(e.key) &&
                          !/^\d$/.test(e.key) &&
                          e.key !== ","
                        ) {
                          e.preventDefault();
                          return;
                        }
                        const input = e.currentTarget.value;
                        const selectionStart = e.currentTarget.selectionStart ?? 0;
                        const selectionEnd = e.currentTarget.selectionEnd ?? 0;
                        let newValue = input.slice(0, selectionStart) + e.key + input.slice(selectionEnd);
                        if (e.key === "Backspace" || e.key === "Delete") {
                          newValue = input;
                        }
                        const parts = newValue.split(",");
                        if (parts.length === 2 && parts[1].length > 2) {
                          e.preventDefault();
                        }
                      }}
                      onBlur={(e) => {
                        let value = e.target.value;
                        if (value === "" || /^\d+(,\d+)?$/.test(value)) {
                          if (value.includes(',')) {
                            const [intPart, decPart] = value.split(',');
                            value = intPart + ',' + decPart.slice(0, 2);
                          }
                          handleChangeMinArea(
                            value === "" ? null : parseFloat(value.replace(',', '.')),
                            items
                          );
                        }
                      }}
                    />
                  )}
                  {needsCutoffDate && (
                    <DatePicker
                      className="date-picker"
                      label={t("CUTOFF_DATE")}
                      placeholder={t("SELECT")}
                      format="DD/MM/YYYY"
                      style={{ minWidth: "248px", width: needsMinArea ? "248px" : "50%" }}
                      value={
                        dateObject?.value
                          ? moment(dateObject?.value, "YYYY-MM-DD")
                          : undefined
                      }
                      onChange={(date: Moment) => {
                        handleChangeCutoffDate(date, items);
                      }}
                    />
                  )}
                </InputsRow>
              </DateInputContainer>
            );
          })}
    </div>
  );
}

export default DeforestationForm;
