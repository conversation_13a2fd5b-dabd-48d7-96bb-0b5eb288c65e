import { render, screen, fireEvent } from "@testing-library/react";
import DeforestationForm from "./DeforestationForm";
import { ReportType } from "@types";
import { Property } from "@/types/property";
import { useSocioEnvironmentCriterias } from "@/hooks/use-socio-environment-criterias";
import moment from "moment";

jest.mock("../../molecules/checkbox-dropdown/CheckboxDropdown", () => ({
  CheckboxDropdown: jest.fn(() => <div data-testid="mock-checkbox-dropdown" />),
}));

jest.mock("@/hooks/use-socio-environment-criterias", () => ({
  useSocioEnvironmentCriterias: jest.fn(),
  DEFORESTATION_CRITERIAS_WITHOUT_CUTOFFDATE: ["MORATORIA_DA_SOJA", "PROTOCOLO_VERDE_DOS_GRAOS"],
  DEFORESTATION_CRITERIAS_WITH_MIN_AREA: ["PRODES", "DETER", "EMBARGO_LDI", "EMBARGO_IBAMA", "EMBARGO_ICMBIO", "EMBARGO_SEMA"],
}));

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("brainui", () => ({
  DatePicker: jest.fn(() => <div data-testid="mock-date-picker" />),
  Divider: jest.fn(() => <div data-testid="mock-divider" />),
  Button: jest.fn(() => <div data-testid="mock-button" />),
}));

jest.mock("antd", () => ({
  InputNumber: jest.fn(() => <div data-testid="mock-input-number" className="min-area-input" />),
  Typography: {
    Text: jest.fn(({ children }) => <div>{children}</div>),
  },
}));

describe("DeforestationForm component", () => {
  const mockOnChange = jest.fn();
  const mockHandleSetSocioEnvironmentalCriterias = jest.fn();

  const defaultProps = {
    data: {
      key: "1",
      contact: {},
      observation: "",
      property: {} as Property,
      reportType: ReportType.DETAILED_ANALYSIS_DEFORESTATION,
      data: {
        queries: [],
      },
    },
    isSubmitted: false,
    onChange: mockOnChange,
  };

  const mockDeforestationCriterias = [
    { label: "PRODES", value: "PRODES", checked: false },
    { label: "DETER", value: "DETER", checked: false },
    { label: "EMBARGO_IBAMA", value: "EMBARGO_IBAMA", checked: false },
    { label: "MORATORIA_DA_SOJA", value: "MORATORIA_DA_SOJA", checked: false },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: mockDeforestationCriterias,
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it("should render the component", () => {
    render(<DeforestationForm {...defaultProps} />);
    const component = screen.getByTestId("pb-t-deforestation-form");
    expect(component).toBeInTheDocument();
  });

  it("should call onChange when deforestation criterias change", () => {
    // Mock a criteria as checked
    const criteriaWithChecked = [
      { label: "PRODES", value: "PRODES", checked: true },
      ...mockDeforestationCriterias.slice(1),
    ];

    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: criteriaWithChecked,
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });

    render(<DeforestationForm {...defaultProps} />);

    // Verify that onChange was called with the correct data
    expect(mockOnChange).toHaveBeenCalledWith({
      ...defaultProps.data,
      data: {
        ...defaultProps.data.data,
        queries: [[{ label: "QUERY", value: "PRODES", is_parent: true }]],
      },
    });
  });

  it("should handle criteria with different input requirements", () => {
    // Test 1: Criteria with cutoff date
    const propsWithCutoffDate = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
              { label: "CUTOFF_DATE", value: "2023-01-01" },
            ],
          ],
        },
      },
    };

    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: [
        { label: "PRODES", value: "PRODES", checked: true },
      ],
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });

    const { unmount } = render(<DeforestationForm {...propsWithCutoffDate} />);
    const component1 = screen.getByTestId("pb-t-deforestation-form");
    expect(component1).toBeInTheDocument();
    unmount();

    // Test 2: Criteria without cutoff date
    const propsWithoutCutoffDate = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "MORATORIA_DA_SOJA", is_parent: true },
            ],
          ],
        },
      },
    };

    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: [
        { label: "MORATORIA_DA_SOJA", value: "MORATORIA_DA_SOJA", checked: true },
      ],
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });

    const { unmount: unmount2 } = render(<DeforestationForm {...propsWithoutCutoffDate} />);
    const component2 = screen.getByTestId("pb-t-deforestation-form");
    expect(component2).toBeInTheDocument();
    unmount2();

    // Test 3: Criteria with minimum area
    const propsWithMinArea = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
              { label: "MIN_AREA", value: "10" },
            ],
          ],
        },
      },
    };

    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: [
        { label: "PRODES", value: "PRODES", checked: true },
      ],
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });

    render(<DeforestationForm {...propsWithMinArea} />);
    const component3 = screen.getByTestId("pb-t-deforestation-form");
    expect(component3).toBeInTheDocument();
  });

  it("should handle minimum area operations (update, remove, add)", () => {
    // Type definition for query items
    type QueryItem = { label: string; value: string; is_parent?: boolean };

    // Test 1: Update minimum area when it exists
    const mockOnChange1 = jest.fn();
    const propsWithMultipleCriterias = {
      ...defaultProps,
      onChange: mockOnChange1,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
              { label: "MIN_AREA", value: "5" },
            ],
            [
              { label: "QUERY", value: "DETER", is_parent: true },
              { label: "MIN_AREA", value: "10" },
            ],
          ],
        },
      },
    };

    // Create a function that simulates the handleChangeMinArea logic
    const handleChangeMinArea = (
      minArea: number | null,
      item: { label: string; value: string; is_parent?: boolean }[],
      props: any
    ) => {
      const parent = item.find((value: { label: string; value: string; is_parent?: boolean }) => value.is_parent === true);

      const criteriasUpdated = props.data.data.queries.map((array: { label: string; value: string; is_parent?: boolean }[]) => {
        const parentExists = array.find((value: { label: string; value: string; is_parent?: boolean }) => value.label === parent.label);
        const minAreaObjectExists = array.find(
          (value: { label: string; value: string }) => value.label === "MIN_AREA"
        );

        const isSameParent = parentExists?.value === parent?.value;

        if (!isSameParent) {
          return array;
        }

        if (minArea === null || minArea === undefined) {
          return array.filter((value: { label: string }) => value.label !== "MIN_AREA");
        }

        if (minAreaObjectExists) {
          return array.map((value: { label: string; value: string }) => {
            if (value.label === "MIN_AREA") {
              return { ...value, value: minArea.toString() };
            }
            return value;
          });
        }

        return [...array, { label: "MIN_AREA", value: minArea.toString() }];
      });

      const result = {
        ...props.data,
        data: { ...props.data.data, queries: criteriasUpdated },
      };

      props.onChange(result);
    };

    // Test updating an existing MIN_AREA
    handleChangeMinArea(15, [
      { label: "QUERY", value: "PRODES", is_parent: true },
      { label: "MIN_AREA", value: "5" },
    ], propsWithMultipleCriterias);

    expect(mockOnChange1).toHaveBeenCalled();
    const callArg1 = mockOnChange1.mock.calls[0][0];

    // Find the PRODES and DETER criteria in the updated queries
    const prodesQuery = callArg1.data.queries.find(
      (query: QueryItem[]) => query.find((item: QueryItem) => item.value === "PRODES")
    );
    const deterQuery = callArg1.data.queries.find(
      (query: QueryItem[]) => query.find((item: QueryItem) => item.value === "DETER")
    );

    // Verify that PRODES has the updated min area value
    const prodesMinArea = prodesQuery.find((item: QueryItem) => item.label === "MIN_AREA");
    expect(prodesMinArea).toBeDefined();
    expect(prodesMinArea.value).toBe("15");

    // Verify that DETER still has the original min area value
    const deterMinArea = deterQuery.find((item: QueryItem) => item.label === "MIN_AREA");
    expect(deterMinArea).toBeDefined();
    expect(deterMinArea.value).toBe("10");

    // Test 2: Remove minimum area
    const mockOnChange2 = jest.fn();
    const propsWithMinArea = {
      ...defaultProps,
      onChange: mockOnChange2,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
              { label: "MIN_AREA", value: "5" },
            ],
          ],
        },
      },
    };

    // Call the function with null to remove the MIN_AREA field
    handleChangeMinArea(null, [
      { label: "QUERY", value: "PRODES", is_parent: true },
      { label: "MIN_AREA", value: "5" },
    ], propsWithMinArea);

    expect(mockOnChange2).toHaveBeenCalled();
    const callArg2 = mockOnChange2.mock.calls[0][0];

    // Find the PRODES criteria in the updated queries
    const prodesQueryAfterRemove = callArg2.data.queries.find(
      (query: QueryItem[]) => query.find((item: QueryItem) => item.value === "PRODES")
    );

    // Verify that the MIN_AREA field was removed
    const prodesMinAreaAfterRemove = prodesQueryAfterRemove.find((item: QueryItem) => item.label === "MIN_AREA");
    expect(prodesMinAreaAfterRemove).toBeUndefined();

    // Test 3: Add minimum area when it doesn't exist
    const mockOnChange3 = jest.fn();
    const propsWithoutMinArea = {
      ...defaultProps,
      onChange: mockOnChange3,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
            ],
          ],
        },
      },
    };

    // Call the function with a new value to add the MIN_AREA field
    handleChangeMinArea(20, [
      { label: "QUERY", value: "PRODES", is_parent: true },
    ], propsWithoutMinArea);

    expect(mockOnChange3).toHaveBeenCalled();
    const callArg3 = mockOnChange3.mock.calls[0][0];

    // Find the PRODES criteria in the updated queries
    const prodesQueryAfterAdd = callArg3.data.queries.find(
      (query: QueryItem[]) => query.find((item: QueryItem) => item.value === "PRODES")
    );

    // Verify that a new MIN_AREA field was added
    const prodesMinAreaAfterAdd = prodesQueryAfterAdd.find((item: QueryItem) => item.label === "MIN_AREA");
    expect(prodesMinAreaAfterAdd).toBeDefined();
    expect(prodesMinAreaAfterAdd.value).toBe("20");
  });

  it("should handle null onChange prop", () => {
    // Setup props with null onChange
    const propsWithNullOnChange = {
      ...defaultProps,
      onChange: null,
    };

    render(<DeforestationForm {...propsWithNullOnChange} />);

    // The component should render without errors
    const component = screen.getByTestId("pb-t-deforestation-form");
    expect(component).toBeInTheDocument();
  });

  it("should handle empty queries", () => {
    // Setup props with empty queries
    const propsWithEmptyQueries = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        data: {
          queries: [],
        },
      },
    };

    render(<DeforestationForm {...propsWithEmptyQueries} />);

    // The component should render without errors
    const component = screen.getByTestId("pb-t-deforestation-form");
    expect(component).toBeInTheDocument();
  });

  it("should handle DOM interactions with date pickers", () => {
    // Setup with multiple criteria arrays for date picker test
    const propsWithDateCriterias = {
      ...defaultProps,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
              { label: "CUTOFF_DATE", value: "2023-01-01" },
            ],
            [
              { label: "QUERY", value: "DETER", is_parent: true },
              { label: "CUTOFF_DATE", value: "2023-02-01" },
            ],
          ],
        },
      },
    };

    // Mock the DatePicker component to capture the onChange handler
    (require("brainui").DatePicker as jest.Mock).mockImplementation(
      ({ onChange, value }) => (
        <div
          data-testid="mock-date-picker"
          onClick={() => onChange && onChange(moment("2023-03-15"))}
        >
          {value?.format("YYYY-MM-DD")}
        </div>
      )
    );

    // Mock the deforestation criterias for date picker test
    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: [
        { label: "PRODES", value: "PRODES", checked: true },
        { label: "DETER", value: "DETER", checked: true },
      ],
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });

    render(<DeforestationForm {...propsWithDateCriterias} />);

    // Find all date pickers and click the first one (PRODES)
    const datePickers = screen.getAllByTestId("mock-date-picker");
    fireEvent.click(datePickers[0]);

    // Verify that onChange was called with the correct data
    // Only the PRODES criteria should be updated, DETER should remain unchanged
    expect(mockOnChange).toHaveBeenCalledWith({
      ...propsWithDateCriterias.data,
      data: {
        ...propsWithDateCriterias.data.data,
        queries: [
          [
            { label: "QUERY", value: "PRODES", is_parent: true },
            { label: "CUTOFF_DATE", value: "2023-03-15" },
          ],
          [
            { label: "QUERY", value: "DETER", is_parent: true },
            { label: "CUTOFF_DATE", value: "2023-02-01" },
          ],
        ],
      },
    });
  });

  it("should verify isSameParent condition and behavior", () => {
    const parent = { label: "QUERY", value: "PRODES", is_parent: true };
    const parentExists = { label: "QUERY", value: "PRODES", is_parent: true };
    const differentParent = { label: "QUERY", value: "DETER", is_parent: true };

    const isSameParent1 = parentExists.value === parent.value;
    expect(isSameParent1).toBe(true);

    const isSameParent2 = differentParent.value === parent.value;
    expect(isSameParent2).toBe(false);

    const isSameParent3 = parentExists?.value === parent?.value;
    expect(isSameParent3).toBe(true);

    const isSameParent4 = differentParent?.value === parent?.value;
    expect(isSameParent4).toBe(false);

    // Test behavior with real data
    const queries = [
      [
        { label: "QUERY", value: "PRODES", is_parent: true },
        { label: "CUTOFF_DATE", value: "2023-01-01" },
      ],
      [
        { label: "QUERY", value: "DETER", is_parent: true },
        { label: "CUTOFF_DATE", value: "2023-02-01" },
      ],
    ];

    const onChange = jest.fn();
    const data = {
      key: "1",
      contact: {},
      observation: "",
      property: {} as Property,
      reportType: ReportType.DETAILED_ANALYSIS_DEFORESTATION,
      data: { queries },
    };

    const date = moment("2023-03-15");
    const prodesItem = [
      { label: "QUERY", value: "PRODES", is_parent: true },
      { label: "CUTOFF_DATE", value: "2023-01-01" },
    ];
    const nonExistentItem = [
      { label: "QUERY", value: "NON_EXISTENT", is_parent: true },
      { label: "CUTOFF_DATE", value: "2023-01-01" },
    ];

    // Test cutoff date update
    const simulateHandleChangeCutoffDate = (
      date: moment.Moment,
      item: { label: string; value: string; is_parent?: boolean }[]
    ) => {
      let formatted: string = "";
      if (date) {
        formatted = date.format("YYYY-MM-DD");
      }

      const parent = item.find((value) => value.is_parent === true);

      const criteriasUpdated = data.data?.queries?.map((array) => {
        const parentExists = array.find((value) => value.label === parent.label);
        const dateObjectExists = array.find(
          (value) => value.label === "CUTOFF_DATE"
        );

        const isSameParent = parentExists?.value === parent?.value;

        if (!isSameParent) {
          return array;
        }

        if (!dateObjectExists) {
          return [...array, { label: "CUTOFF_DATE", value: formatted }];
        } else {
          return array.map(value => {
            if (value.label === "CUTOFF_DATE") {
              return { ...value, value: formatted };
            }
            return value;
          });
        }
      });

      const result = {
        ...data,
        data: { ...data.data, queries: criteriasUpdated },
      };

      onChange(result);
    };

    // Test when isSameParent is true
    simulateHandleChangeCutoffDate(date, prodesItem);
    expect(onChange).toHaveBeenCalledWith({
      ...data,
      data: {
        ...data.data,
        queries: [
          [
            { label: "QUERY", value: "PRODES", is_parent: true },
            { label: "CUTOFF_DATE", value: "2023-03-15" },
          ],
          [
            { label: "QUERY", value: "DETER", is_parent: true },
            { label: "CUTOFF_DATE", value: "2023-02-01" },
          ],
        ],
      },
    });

    // Reset the mock
    onChange.mockClear();

    // Test when isSameParent is false
    simulateHandleChangeCutoffDate(date, nonExistentItem);
    expect(onChange).toHaveBeenCalledWith({
      ...data,
      data: {
        ...data.data,
        queries: [
          [
            { label: "QUERY", value: "PRODES", is_parent: true },
            { label: "CUTOFF_DATE", value: "2023-01-01" },
          ],
          [
            { label: "QUERY", value: "DETER", is_parent: true },
            { label: "CUTOFF_DATE", value: "2023-02-01" },
          ],
        ],
      },
    });
  });

  it("should handle DOM interactions with input fields", () => {
    // Mock the InputNumber component to capture and trigger the onBlur handler
    (require("antd").InputNumber as jest.Mock).mockImplementation(
      ({ onBlur, defaultValue, className, "data-testid": dataTestId }) => (
        <input
          data-testid={dataTestId || "mock-input-number"}
          className={className}
          defaultValue={defaultValue}
          onBlur={(_e) => {
            if (onBlur) {
              const mockEvent = {
                target: {
                  value: "15"
                }
              };
              onBlur(mockEvent);
            }
          }}
        />
      )
    );

    // Setup with multiple criteria arrays
    const mockOnChange = jest.fn();
    const propsWithMultipleCriterias = {
      ...defaultProps,
      onChange: mockOnChange,
      data: {
        ...defaultProps.data,
        data: {
          queries: [
            [
              { label: "QUERY", value: "PRODES", is_parent: true },
              { label: "MIN_AREA", value: "5" },
            ],
            [
              { label: "QUERY", value: "DETER", is_parent: true },
              { label: "MIN_AREA", value: "10" },
            ],
          ],
        },
      },
    };

    // Mock the deforestation criterias
    (useSocioEnvironmentCriterias as jest.Mock).mockReturnValue({
      deforestationCriterias: [
        { label: "PRODES", value: "PRODES", checked: true },
        { label: "DETER", value: "DETER", checked: true },
      ],
      handleSetSocioEnvironmentalCriterias: mockHandleSetSocioEnvironmentalCriterias,
    });

    render(<DeforestationForm {...propsWithMultipleCriterias} />);

    // Find all input number elements
    const inputElements = screen.getAllByTestId("mock-input-number");
    expect(inputElements.length).toBeGreaterThan(0);

    // Trigger the onBlur event on the first input (PRODES)
    fireEvent.blur(inputElements[0]);

    // Verify that onChange was called
    expect(mockOnChange).toHaveBeenCalled();
  });
});
