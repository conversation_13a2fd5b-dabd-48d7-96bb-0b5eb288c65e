import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import AgroCreditFormConfirm from "./AgroCreditFormConfirm";

afterEach(() => {
  cleanup();
});

describe("AgroCreditFormConfirm component", () => {
  it("should render the component", () => {
    render(
      <AgroCreditFormConfirm
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        disabled={false}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-agro-credit-confirm");
    expect(component).toBeInTheDocument();
  });
});
