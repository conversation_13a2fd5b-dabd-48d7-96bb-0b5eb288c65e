import { useTranslation } from "react-i18next";
import {
  Content,
  Options,
  Overline,
  SummaryCard,
  Wrapper,
} from "./AgroCreditFormConfirm.styles";
import { Button } from "antd";
import { useContext } from "react";
import { RequestFormContext } from "@contexts";
import { AgroCreditFormState } from "../agro-credit-form/AgroCreditForm";

interface AgroCreditFormConfirmProps {
  readonly data: AgroCreditFormState;
  readonly disabled: boolean;
  readonly onBack: () => void;
  readonly onChange: () => void;
  readonly onNext: () => void;
}

function AgroCreditFormConfirm({
  data,
  disabled,
  onBack,
  onChange,
  onNext,
}: AgroCreditFormConfirmProps) {
  const { t } = useTranslation();
  const { closeRequestForm } = useContext(RequestFormContext);

  function handleClose() {
    onChange();
    closeRequestForm();
  }

  return (
    <Wrapper data-testid="pb-t-agro-credit-confirm">
      <Content>
        <section>
          <h2>{t("CONFIRM_REQUEST")}</h2>
          <SummaryCard>
            <Overline>{t("SUMMARY")}</Overline>
            <p>{t("VALID_DOCUMENTS")}</p>
            <ul>
              <li>
                {data.documents.length} {t("DOCUMENTS")}
              </li>
            </ul>
            <p>{t("SELECTED_SCORES")}</p>
            <ul>
              {data.scores.map((score, idx) => (
                <li key={idx}>{t(score)}</li>
              ))}
            </ul>
          </SummaryCard>
        </section>
      </Content>
      <Options>
        <Button onClick={handleClose} disabled={disabled}>
          {t("CANCEL")}
        </Button>
        <div style={{ display: "flex", gap: "16px" }}>
          <Button onClick={onBack} disabled={disabled}>
            {t("BACK")}
          </Button>
          <Button type="primary" onClick={onNext} disabled={disabled}>
            {t("FINISH")}
          </Button>
        </div>
      </Options>
    </Wrapper>
  );
}

export default AgroCreditFormConfirm;
