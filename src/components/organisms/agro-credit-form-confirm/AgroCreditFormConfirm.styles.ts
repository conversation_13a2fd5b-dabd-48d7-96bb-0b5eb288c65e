import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
  gap: 16px;
  margin: 16px 0;

  h1 {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  h2 {
    font-size: 16px;
    font-weight: 500;
  }
`;

export const Options = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const SummaryCard = styled.div`
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  border: 1px solid #e2e4e7;
  padding: 16px;
  gap: 10px;

  ul {
    margin: 0;
  }

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.2;
  }
`;

export const Overline = styled.p`
  color: #5e6976;
  font-size: 16px;
`;
