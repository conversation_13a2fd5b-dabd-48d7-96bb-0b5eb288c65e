import styled from "styled-components";

export const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
`;

export const IdentificationRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
`;

export const SwitchWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

export const SwitchLabel = styled.div`
  display: flex;
  flex-direction: column;
`;
