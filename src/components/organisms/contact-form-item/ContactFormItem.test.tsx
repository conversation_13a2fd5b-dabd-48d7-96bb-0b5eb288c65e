import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import ContactFormItem from "./ContactFormItem";
import { IdentificationType } from "@types";
import { notification } from "antd";

jest.mock("react-i18next", () => {
  return {
    useTranslation: jest.fn(() => ({
      t: (text: string) => text,
    })),
  };
});

jest.mock("uuid", () => ({
  v4: jest.fn(() => "mocked-uuid"),
}));

jest.mock("antd", () => {
  const original = jest.requireActual("antd");
  return {
    ...original,
    notification: {
      error: jest.fn(),
      success: jest.fn(),
    },
  };
});

jest.mock("@components", () => ({
  InputCar: ({ onAdd, onRemove, value, canRemove, canAdd, placeholder }) => (
    <div data-testid="pb-t-input-car">
      <input
        data-testid="pb-t-input-car-autocomplete"
        placeholder={placeholder}
        defaultValue={value || ""}
        readOnly={canRemove}
      />
      {canRemove && (
        <button
          data-testid="button-remove-car"
          onClick={() => onRemove && onRemove(value)}
        >
          Remove
        </button>
      )}
      {!canRemove && canAdd && (
        <button
          data-testid="button-add-car"
          onClick={() => onAdd && onAdd("BR-1234567-ABCDEFGHIJKLMNOPQRSTUVWXYZ123456")}
        >
          Add
        </button>
      )}
    </div>
  ),
  SelectCity: ({ onChange, placeholder, disabled }) => (
    <div data-testid="pb-t-select-city">
      <select
        data-testid="select-city"
        disabled={disabled}
        title={placeholder}
        onChange={(e) => onChange && onChange([e.target.value])}
      >
        <option value="">Select city</option>
        <option value="1">São Paulo</option>
        <option value="2">Rio de Janeiro</option>
      </select>
    </div>
  ),
}));

describe("ContactFormItem component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(<ContactFormItem />);
    const component = screen.queryByTestId("pb-t-contact-form-item");
    expect(component).toBeInTheDocument();
  });

  it("should render all form elements", () => {
    render(<ContactFormItem />);

    expect(screen.getByTestId("select-identification-type")).toBeInTheDocument();

    expect(screen.getByPlaceholderText("IDENTIFICATION_DOCUMENT")).toBeInTheDocument();

    expect(screen.getByPlaceholderText("NAME_SEARCHED_AUTOMATICALLY")).toBeInTheDocument();

    expect(screen.getByText("SEARCH_PROPERTIES_AUTOMATICALLY")).toBeInTheDocument();
    expect(screen.getByText("INFO_QUERY_TAKE_LONGER")).toBeInTheDocument();

    expect(screen.getByTestId("pb-t-select-city")).toBeInTheDocument();

    expect(screen.getByTestId("pb-t-input-car")).toBeInTheDocument();
  });

  it("should call onChange when identification type changes", () => {
    const onChangeMock = jest.fn();
    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const select = screen.getByTestId("select-identification-type").querySelector("input");
    fireEvent.mouseDown(select);

    const option = screen.getByText("UNIQUE_REGISTRATION");
    fireEvent.click(option);

    expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
      identificationType: IdentificationType.RU,
      document: "",
      searchProperty: false,
    }));
  });

  it("should call onChange when document input changes", () => {
    const onChangeMock = jest.fn();
    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const documentInput = screen.getByPlaceholderText("IDENTIFICATION_DOCUMENT");
    fireEvent.change(documentInput, { target: { value: "12345678901" } });

    expect(onChangeMock).toHaveBeenCalled();
  });

  it("should call onChange when name input changes", () => {
    const onChangeMock = jest.fn();

    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const select = screen.getByTestId("select-identification-type").querySelector("input");
    fireEvent.mouseDown(select);
    const option = screen.getByText("UNIQUE_REGISTRATION");
    fireEvent.click(option);

    onChangeMock.mockClear();

    const nameInput = screen.getByPlaceholderText("CONTACT_NAME");
    fireEvent.change(nameInput, { target: { value: "Test Name" } });

    expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
      name: "Test Name",
    }));
  });

  it("should call onChange when search property switch changes", () => {
    const onChangeMock = jest.fn();
    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const switchElement = screen.getByRole("switch");

    fireEvent.click(switchElement);

    expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
      searchProperty: false,
    }));
  });

  it("should call onChange when cities selection changes", () => {
    const onChangeMock = jest.fn();
    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const citySelect = screen.getByTestId("select-city");

    fireEvent.change(citySelect, { target: { value: "1" } });

    expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
      cities: ["1"],
    }));
  });

  it("should add CAR code when add button is clicked", async () => {
    const onChangeMock = jest.fn();
    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const addButton = screen.getByTestId("button-add-car");

    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId("button-remove-car")).toBeInTheDocument();
    });
  });

  it("should remove CAR code when remove button is clicked", async () => {
    const onChangeMock = jest.fn();
    render(<ContactFormItem onChange={onChangeMock} disabled={false} />);

    const addButton = screen.getByTestId("button-add-car");
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId("button-remove-car")).toBeInTheDocument();
    });

    const removeButton = screen.getByTestId("button-remove-car");
    fireEvent.click(removeButton);

    await waitFor(() => {
      expect(screen.queryByTestId("button-remove-car")).not.toBeInTheDocument();
    });
  });

  it("should show error notification when trying to add an empty CAR", async () => {
    jest.clearAllMocks();

    const InputCarMock = jest.fn(({ onAdd }) => {
      if (onAdd) setTimeout(() => onAdd(""), 0);
      return null;
    });

    jest.spyOn(require("@components"), "InputCar").mockImplementation(InputCarMock);

    render(<ContactFormItem />);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalled();
    });
  });

  it("should show error notification when trying to add a duplicate CAR", async () => {
    jest.clearAllMocks();

    const carCode = "BR-1234567-ABCDEFGHIJKLMNOPQRSTUVWXYZ123456";

    const mockInputCar = jest.fn(({ onAdd, onRemove, value, canRemove, canAdd }) => {
      if (canAdd && !value) {
        setTimeout(() => onAdd && onAdd(carCode), 0);
      }

      if (canAdd && !canRemove) {
        setTimeout(() => onAdd && onAdd(carCode), 100);
      }

      return (
        <div data-testid="pb-t-input-car">
          {value && (
            <div data-testid="car-item">
              <span>{value}</span>
              <button data-testid="button-remove-car" onClick={() => onRemove && onRemove(value)}>
                Remove
              </button>
            </div>
          )}
          {!value && canAdd && (
            <button data-testid="button-add-car">Add</button>
          )}
        </div>
      );
    });

    jest.spyOn(require("@components"), "InputCar").mockImplementation(mockInputCar);

    render(<ContactFormItem />);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith({
        message: "CAR_ALREADY_EXISTS",
        description: "CAR_ALREADY_EXISTS_DESC",
      });
    }, { timeout: 2000 });
  });

  it("should disable name input when identification type is DOCUMENT", () => {
    render(<ContactFormItem />);

    const nameInput = screen.getByPlaceholderText("NAME_SEARCHED_AUTOMATICALLY");
    expect(nameInput).toBeDisabled();

    const select = screen.getByTestId("select-identification-type").querySelector("input");
    fireEvent.mouseDown(select);
    const option = screen.getByText("UNIQUE_REGISTRATION");
    fireEvent.click(option);

    const updatedNameInput = screen.getByPlaceholderText("CONTACT_NAME");
    expect(updatedNameInput).not.toBeDisabled();
  });

  it("should disable search property switch when identification type is not DOCUMENT", () => {
    render(<ContactFormItem />);

    const switchElement = screen.getByRole("switch");
    expect(switchElement).not.toBeDisabled();

    const select = screen.getByTestId("select-identification-type").querySelector("input");
    fireEvent.mouseDown(select);
    const option = screen.getByText("UNIQUE_REGISTRATION");
    fireEvent.click(option);

    const updatedSwitchElement = screen.getByRole("switch");
    expect(updatedSwitchElement).toBeDisabled();
  });

  it("should disable SelectCity when searchProperty is false", () => {
    render(<ContactFormItem />);

    const citySelect = screen.getByTestId("select-city");
    expect(citySelect).not.toBeDisabled();

    const switchElement = screen.getByRole("switch");
    fireEvent.click(switchElement);

    const updatedCitySelect = screen.getByTestId("select-city");
    expect(updatedCitySelect).toBeDisabled();
  });

  it("should respect the disabled prop", () => {
    render(<ContactFormItem disabled={true} />);

    const select = screen.getByTestId("select-identification-type").querySelector("input");
    expect(select).toBeDisabled();

    const documentInput = screen.getByPlaceholderText("IDENTIFICATION_DOCUMENT");
    expect(documentInput).toBeDisabled();

    const nameInput = screen.getByPlaceholderText("NAME_SEARCHED_AUTOMATICALLY");
    expect(nameInput).toBeDisabled();
  });
});
