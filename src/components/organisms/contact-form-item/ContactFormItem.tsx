import { InputCar, SelectCity } from "@components";
import { MAX_PROPERTY_LIMIT } from "@constants";
import { IdentificationType } from "@types";
import { formatDocument } from "@utils";
import { Switch, notification } from "antd";
import { Input, Select } from "brainui";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";
import {
  FormGroup,
  IdentificationRow,
  SwitchLabel,
  SwitchWrapper,
} from "./ContactFormItem.styles";

const PLACEHOLDER_BY_IDENTIFICATION_TYPE = {
  [IdentificationType.DOCUMENT]: "IDENTIFICATION_DOCUMENT",
  [IdentificationType.RU]: "IDENTIFICATION_UNIQUE_REGISTER",
  [IdentificationType.CI]: "IDENTIFICATION_INTERNAL_CODE",
};

interface ContactFormItemProps {
  disabled?: boolean;
  onChange?: (value) => void;
}

function ContactFormItem({ disabled, onChange }: ContactFormItemProps) {
  const { t } = useTranslation();
  const [formValue, setFormValue] = useState({
    identificationType: IdentificationType.DOCUMENT,
    document: "",
    name: "",
    searchProperty: true,
    cities: [],
    CAR: [],
  });

  const addCAR = async (code) => {
    if (!code) {
      notification.error({
        message: t("ALERT_EMPTY_CAR"),
        description: t("ALERT_EMPTY_CAR_DESC"),
      });
      return false;
    }
    const hasCode = formValue.CAR.filter((car) => car.code === code);
    if (hasCode.length) {
      notification.error({
        message: t("CAR_ALREADY_EXISTS"),
        description: t("CAR_ALREADY_EXISTS_DESC"),
      });
      return;
    }
    const newFormValue = {
      ...formValue,
      CAR: [...formValue.CAR, { code, id: uuidv4() }],
    };
    setFormValue(newFormValue);
  };

  const removeCAR = (code) => {
    const newFormValue = {
      ...formValue,
      CAR: formValue.CAR.filter((car) => car.code !== code),
    };
    setFormValue(newFormValue);
  };

  return (
    <FormGroup data-testid="pb-t-contact-form-item">
      <IdentificationRow>
        <Select
          label={t("IDENTIFICATION_TYPE")}
          data-testid="select-identification-type"
          defaultValue={formValue.identificationType}
          options={[
            {
              value: IdentificationType.DOCUMENT,
              ["data-testid"]: `option-select-identification-${IdentificationType.DOCUMENT}`,
              label: "CPF/CNPJ",
            },
            {
              value: IdentificationType.RU,
              ["data-testid"]: `option-select-identification-${IdentificationType.RU}`,
              label: t("UNIQUE_REGISTRATION"),
            },
            {
              value: IdentificationType.CI,
              ["data-testid"]: `option-select-identification-${IdentificationType.CI}`,
              label: t("INTERNAL_CODE"),
            },
          ]}
          onChange={(value) => {
            const newFormValue = {
              ...formValue,
              document: "",
              searchProperty:
                value !== IdentificationType.DOCUMENT
                  ? false
                  : formValue.searchProperty,
              identificationType: value,
            };
            setFormValue(newFormValue);
            onChange && onChange(newFormValue);
          }}
          disabled={disabled}
        />
        <Input
          placeholder={t(
            PLACEHOLDER_BY_IDENTIFICATION_TYPE[formValue.identificationType]
          )}
          value={formValue.document}
          onChange={({ target }) => {
            const newFormValue = {
              ...formValue,
              document: formatDocument(
                target.value,
                formValue.identificationType
              ),
            };
            setFormValue(newFormValue);
            onChange && onChange(newFormValue);
          }}
          disabled={disabled}
        />
      </IdentificationRow>
      <Input
        placeholder={
          formValue.identificationType == IdentificationType.DOCUMENT
            ? t("NAME_SEARCHED_AUTOMATICALLY")
            : t("CONTACT_NAME")
        }
        disabled={
          disabled ||
          formValue.identificationType == IdentificationType.DOCUMENT
        }
        onChange={({ target }) => {
          const newFormValue = {
            ...formValue,
            name: target.value,
          };
          setFormValue(newFormValue);
          onChange && onChange(newFormValue);
        }}
        size="small"
      />
      <SwitchWrapper>
        <Switch
          checked={formValue.searchProperty}
          disabled={formValue.identificationType != IdentificationType.DOCUMENT}
          onChange={(value) => {
            const newFormValue = {
              ...formValue,
              searchProperty: value,
            };
            setFormValue(newFormValue);
            onChange && onChange(newFormValue);
          }}
        />
        <SwitchLabel>
          <p>{t("SEARCH_PROPERTIES_AUTOMATICALLY")}</p>
          <p>{t("INFO_QUERY_TAKE_LONGER")}</p>
        </SwitchLabel>
      </SwitchWrapper>

      <SelectCity
        value={formValue.cities}
        onChange={(newCities) => {
          const newFormValue = {
            ...formValue,
            cities: newCities,
          };
          setFormValue(newFormValue);
          onChange && onChange(newFormValue);
        }}
        placeholder={
          !formValue.searchProperty
            ? t("CITY_SELECTION_UNAVAILABLE")
            : t("CONTACT_PROPERTIES_CITIES")
        }
        disabled={!formValue.searchProperty}
        optionLimit={formValue.cities.length === 5}
      />

      {formValue.CAR.map((car, idx) => (
        <InputCar
          key={car.id}
          placeholder={t("PROPERTY_CAR_PLACEHOLDER")}
          value={car.code}
          canRemove={true}
          onRemove={(code) => removeCAR(code)}
        />
      ))}

      {formValue.CAR.length < MAX_PROPERTY_LIMIT && (
        <InputCar
          placeholder={t("PROPERTY_CAR_PLACEHOLDER")}
          onAdd={(code) => addCAR(code)}
          canAdd={true}
        />
      )}
    </FormGroup>
  );
}

export default ContactFormItem;
