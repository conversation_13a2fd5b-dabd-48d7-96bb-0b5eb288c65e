import { useTranslation } from "react-i18next";
import {
  Content,
  InlineTableButton,
  Summary,
  SummaryContent,
  SummaryCount,
  SummaryHeader,
  SummaryHeaderSection,
  Wrapper,
} from "./FileUploadVerifyStep.styles";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { useState } from "react";
import { icon_person_cancel, icon_person_check } from "../../../assets";
import {
  CloudDownloadOutlined,
  DownOutlined,
  UpOutlined,
} from "@ant-design/icons";
import ExcelJS from "exceljs";
import { IFileUploadVerifyStepProps } from "./FileUploadVerifyStep.interfaces";
import { PreviewTable } from "@/components/atoms";

function FileUploadVerifyStep({ data, filename }: IFileUploadVerifyStepProps) {
  const { t } = useTranslation();
  const [isInvalidDocsExpanded, setIsInvalidDocsExpanded] = useState(true);

  function downloadInvalidRows() {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Linhas Inválidas");
    worksheet.columns = Object.keys(data.invalidRows[0]).map((key) => ({
      header: key,
      key,
    }));
    data.invalidRows.forEach((row) => {
      worksheet.addRow(row);
    });
    workbook.xlsx.writeBuffer().then((excelBuffer) => {
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `${filename}-invalid.xlsx`;
      link.dispatchEvent(
        new MouseEvent(`click`, {
          bubbles: true,
          cancelable: true,
          view: window,
        })
      );
      setTimeout(function () {
        link.remove();
      }, 0);
    });
  }

  return (
    <Wrapper data-testid="pb-t-agro-file-upload-verify">
      <Content>
        <section>
          <h2>{t("REVIEW_UPLOADED_DOCUMENTS")}</h2>
        </section>

        <Summary style={{ flexShrink: 0 }}>
          <SummaryHeader>
            <SummaryHeaderSection>
              <h2>{t("VALID_ROWS")}</h2>
              <SummaryCount>
                <img src={icon_person_check} alt="Valid row icon" />
                <span style={{ color: "#118385" }}>
                  {data.validRows.length}
                </span>
              </SummaryCount>
            </SummaryHeaderSection>
          </SummaryHeader>
        </Summary>

        <Summary>
          <SummaryHeader>
            <SummaryHeaderSection>
              <h2>{t("INVALID_ROWS")}</h2>
              <SummaryCount>
                <img src={icon_person_cancel} alt="Invalid row icon" />
                <span style={{ color: "#A8001F" }}>
                  {data.invalidRows.length}
                </span>
              </SummaryCount>
            </SummaryHeaderSection>
          </SummaryHeader>
          <SummaryContent>
            <Alert message={t("INVALID_ROWS_DESCRIPTION")} type="warning" />
            <div
              style={{
                overflow: "auto",
                display: "flex",
                alignItems: "flex-start",
                width: "100%",
              }}
            >
              <InlineTableButton
                onClick={() => {
                  setIsInvalidDocsExpanded(!isInvalidDocsExpanded);
                }}
              >
                {isInvalidDocsExpanded ? <DownOutlined /> : <UpOutlined />}
              </InlineTableButton>
              <PreviewTable
                data={data.invalidRows}
                expanded={isInvalidDocsExpanded}
              />
            </div>
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                size="small"
                icon={<CloudDownloadOutlined />}
                onClick={downloadInvalidRows}
                disabled={!data.invalidRows.length}
              >
                {t("DOWNLOAD_XLS_RELATION")}
              </Button>
            </div>
          </SummaryContent>
        </Summary>
      </Content>
    </Wrapper>
  );
}

export default FileUploadVerifyStep;
