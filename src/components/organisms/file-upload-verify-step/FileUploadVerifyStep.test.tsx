import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import FileUploadVerifyStep from "./FileUploadVerifyStep";
import { IFileUploadVerifyStepProps } from "./FileUploadVerifyStep.interfaces";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key, // Return the key as the translation
  }),
}));

jest.mock("../../../assets", () => ({
  icon_person_check: "icon-check-mock",
  icon_person_cancel: "icon-cancel-mock",
}));

jest.mock("exceljs", () => {
  return {
    Workbook: jest.fn().mockImplementation(() => ({
      addWorksheet: jest.fn().mockReturnValue({
        columns: [],
        addRow: jest.fn(),
      }),
      xlsx: {
        writeBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(0)),
      },
    })),
  };
});

jest.mock("@/components/atoms", () => ({
  PreviewTable: ({ data, expanded }) => (
    <div data-testid="mock-preview-table" data-expanded={expanded}>
      <span>Row count: {data.length}</span>
    </div>
  ),
}));

describe("FileUploadVerifyStep", () => {
  const mockProps: IFileUploadVerifyStepProps = {
    data: {
      validRows: [{ id: 1, name: "Valid Row 1" }],
      invalidRows: [{ id: 2, name: "Invalid Row 1" }],
    },
    filename: "test-file.csv",
  };

  let mockLink: any;
  let originalCreateElement: any;
  let mockCreateObjectURL: jest.Mock;

  beforeEach(() => {
    mockCreateObjectURL = jest.fn().mockReturnValue("mock-blob-url");
    global.URL.createObjectURL = mockCreateObjectURL;

    mockLink = {
      href: "",
      download: "",
      dispatchEvent: jest.fn(),
      remove: jest.fn(),
    };

    originalCreateElement = document.createElement;
    document.createElement = jest.fn((tag) => {
      if (tag === "a") return mockLink as any;
      return originalCreateElement.call(document, tag);
    });

    jest.useFakeTimers();
  });

  afterEach(() => {
    document.createElement = originalCreateElement;
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  it("renders the component correctly with valid and invalid rows", () => {
    render(<FileUploadVerifyStep {...mockProps} />);

    const component = screen.getByTestId("pb-t-agro-file-upload-verify");
    expect(component).toBeInTheDocument();

    expect(screen.getByText("VALID_ROWS")).toBeInTheDocument();

    const validRowsCount = screen.getAllByText("1").find(
      element => element.parentElement?.querySelector('img[alt="Valid row icon"]')
    );
    expect(validRowsCount).toBeInTheDocument();

    expect(screen.getByText("INVALID_ROWS")).toBeInTheDocument();

    const invalidRowsCount = screen.getAllByText("1").find(
      element => element.parentElement?.querySelector('img[alt="Invalid row icon"]')
    );
    expect(invalidRowsCount).toBeInTheDocument();

    expect(screen.getByText("INVALID_ROWS_DESCRIPTION")).toBeInTheDocument();

    expect(screen.getByTestId("mock-preview-table")).toBeInTheDocument();
    expect(screen.getByText("Row count: 1")).toBeInTheDocument();

    expect(screen.getByText("DOWNLOAD_XLS_RELATION")).toBeInTheDocument();
  });

  it("toggles the invalid rows table expansion when the button is clicked", () => {
    render(<FileUploadVerifyStep {...mockProps} />);

    expect(screen.getByTestId("mock-preview-table").getAttribute("data-expanded")).toBe("true");

    const toggleButton = screen.getByRole("button", { name: "down" });
    fireEvent.click(toggleButton);

    expect(screen.getByTestId("mock-preview-table").getAttribute("data-expanded")).toBe("false");

    fireEvent.click(toggleButton);

    expect(screen.getByTestId("mock-preview-table").getAttribute("data-expanded")).toBe("true");
  });

  it("downloads invalid rows when the download button is clicked", async () => {
    render(<FileUploadVerifyStep {...mockProps} />);

    const downloadButton = screen.getByRole("button", { name: /DOWNLOAD_XLS_RELATION/i });
    fireEvent.click(downloadButton);

    await waitFor(() => {
      expect(mockCreateObjectURL).toHaveBeenCalled();
      expect(mockLink.href).toBe("mock-blob-url");
      expect(mockLink.download).toBe("test-file.csv-invalid.xlsx");
      expect(mockLink.dispatchEvent).toHaveBeenCalled();
    });

    jest.runAllTimers();
    expect(mockLink.remove).toHaveBeenCalled();
  });

  it("disables the download button when there are no invalid rows", () => {
    const propsWithNoInvalidRows: IFileUploadVerifyStepProps = {
      data: {
        validRows: [{ id: 1, name: "Valid Row 1" }],
        invalidRows: [],
      },
      filename: "test-file.csv",
    };

    render(<FileUploadVerifyStep {...propsWithNoInvalidRows} />);

    const downloadButton = screen.getByRole("button", { name: /DOWNLOAD_XLS_RELATION/i });
    expect(downloadButton).toBeDisabled();
  });

  it("renders correctly when there are no valid or invalid rows", () => {
    const emptyProps: IFileUploadVerifyStepProps = {
      data: {
        validRows: [],
        invalidRows: [],
      },
      filename: "test-file.csv",
    };

    render(<FileUploadVerifyStep {...emptyProps} />);

    expect(screen.getByTestId("pb-t-agro-file-upload-verify")).toBeInTheDocument();

    const validCount = screen.getAllByText("0").find(
      element => element.parentElement?.querySelector('img[alt="Valid row icon"]')
    );
    const invalidCount = screen.getAllByText("0").find(
      element => element.parentElement?.querySelector('img[alt="Invalid row icon"]')
    );

    expect(validCount).toBeInTheDocument();
    expect(invalidCount).toBeInTheDocument();
  });
});
