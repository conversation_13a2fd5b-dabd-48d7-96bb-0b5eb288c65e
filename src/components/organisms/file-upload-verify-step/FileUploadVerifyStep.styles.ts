import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
  gap: 16px;
  margin: 16px 0;

  h1 {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  h2 {
    font-size: 16px;
    font-weight: 500;
  }
`;

export const Summary = styled.section`
  border-radius: 8px;
  border: 1px solid #a7a7a7;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

export const SummaryContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  border-top: 1px solid #e2e4e7;
  overflow: hidden;
`;

export const SummaryHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;

  h2 {
    font-size: 14px;
    line-height: 22px;
    color: #8a9292;
    font-weight: 500;
    margin: 0;
  }
`;

export const SummaryHeaderSection = styled.div`
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
  }
`;

export const SummaryCount = styled.div`
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const InlineTableButton = styled.button`
  cursor: pointer;
  background: none;
  border: none;
`;
