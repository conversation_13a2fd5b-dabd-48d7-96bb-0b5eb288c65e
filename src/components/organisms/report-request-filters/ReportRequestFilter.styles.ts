import { Form } from "antd";
import { RangePicker } from "brainui";
import styled from "styled-components";

export const FilterForm = styled(Form)`
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: var(--surface-color-1);
  padding: 32px 24px !important;

  .ant-form-item {
    margin-bottom: 0;
  }
`;

export const LabelInput = styled.div`
  input {
    padding: 20px 12px 4px 11px !important;
  }
`;

export const LabelRangePicker = styled(RangePicker)`
  .ant-picker.ant-picker-range {
    padding: 20px 12px 4px 11px !important;
  }
`;

export const ReportFilterRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 4fr 6fr;
  gap: 24px;
`;

export const TagsDateRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 2fr 2fr 4fr 1fr 1fr;
  gap: 24px;
`;

export const FiltersContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
`;

export const FilterColSearch = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 200px 4fr;
  gap: 24px;
`;

export const FilterCol = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 4fr 150px 150px;
  gap: 24px;
`;

export const FilterColTypes = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 6fr 150px;
  gap: 24px;
`;

export const FilterColAdditionalInfo = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 200px 2fr 2fr;
  gap: 24px;
`;

export const TagFilterSelect = styled.div`
  width: 100%;

  .ant-select {
    width: 100% !important;
  }

  .ant-select.ant-select-in-form-item {
    width: 100% !important;
  }
`;
