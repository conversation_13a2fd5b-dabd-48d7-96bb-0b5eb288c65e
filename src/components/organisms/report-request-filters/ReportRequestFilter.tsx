import { REPORT_TITLE } from "@constants";
import { REPORT_STATUS_LABELS, ReportStatus, ReportType } from "@types";
import { Form } from "antd";
import { Button, Input, RangePicker, Select } from "brainui";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import {
  FilterCol,
  FilterColAdditionalInfo,
  FilterColSearch,
  FilterColTypes,
  FilterForm,
  FiltersContainer,
  LabelInput,
  TagFilterSelect,
} from "./ReportRequestFilter.styles";
import { useCardsList } from "@/hooks/use-cards-list";
import { useTagsList } from "@/hooks/use-tags-list";
import { useRequestForm } from "@/hooks/use-request-form";
import { useReportRequestsList } from "@/hooks/use-report-requests-list";

const { Option } = Select;

function ReportRequestFilter() {
  const { t } = useTranslation();
  const { form } = useRequestForm();
  const {
    handleListReportsRequests,
    pagination,
    handleSetPaginationData,
    handleClearFiltersAndRefreshReports,
    filters,
    handleSetFiltersData,
  } = useReportRequestsList();

  const { cardsList } = useCardsList();
  const { tagsList } = useTagsList();
  const [searchParams] = useSearchParams();
  const [filterTags, setFilterTags] = useState([]);
  const paramId = searchParams.get("id");

  const onFinish = (rawValues) => {
    const values = Object.keys(rawValues)
      .filter(
        (key) =>
          rawValues[key] !== undefined &&
          rawValues[key] !== null &&
          !["ALL"].includes(rawValues[key])
      )
      .reduce((crr, key) => ({ ...crr, [key]: rawValues[key] }), {});

    const digitsSearch = (values["search"] || "").replace(/[^\d]/g, "");
    if (digitsSearch.length == 11 || digitsSearch.length == 14) {
      values["search"] = digitsSearch;
    }

    if (values["created"]) {
      const created = values["created"];
      delete values["created"];
      values["created_start"] = created[0].format("YYYY-MM-DD");
      values["created_end"] = created[1].format("YYYY-MM-DD");
    }
    if (filterTags.length) {
      values["tags_names"] = filterTags;
    }
    handleSetFiltersData({
      filters: values,
    });
    handleListReportsRequests({
      ...values,
      limit: pagination.limit,
      offset: 0,
    });
  };

  useEffect(() => {
    if (!paramId) return;
    handleListReportsRequests({
      search: paramId,
      limit: pagination.limit,
      offset: 0,
    });
  }, [paramId]);

  return (
    <FilterForm
      form={form}
      onFinish={onFinish}
      data-testid="pb-t-report-request-filter"
    >
      <FiltersContainer>
        <FilterColSearch>
          <Form.Item name="id">
            <Input type="number" placeholder={t("ID")} autoComplete="off" />
          </Form.Item>
          <Form.Item name="search">
            <Input placeholder={t("FILTERS_PLACEHOLDER")} autoComplete="off" />
          </Form.Item>
        </FilterColSearch>
        <FilterColTypes>
          <Form.Item name="reports_types" initialValue={ReportType.ALL}>
            <Select label={t("REPORT_TYPE")}>
              <Option value={ReportType.ALL}>{t("EVERY_REPORT_TYPE")}</Option>
              <Option value={ReportType.VALUATION}>
                {t(REPORT_TITLE[ReportType.VALUATION])}
              </Option>
              <Option value={ReportType.INSPECTION}>
                {t(REPORT_TITLE[ReportType.INSPECTION])}
              </Option>
              <Option value={ReportType.SOCIOENVIRONMENT_COMPLIANCE}>
                {t(REPORT_TITLE[ReportType.SOCIOENVIRONMENT_COMPLIANCE])}
              </Option>
              <Option value={ReportType.SOCIOENVIRONMENT_PROTOCOL}>
                {t(REPORT_TITLE[ReportType.SOCIOENVIRONMENT_PROTOCOL])}
              </Option>
              <Option value={ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG}>
                {t(REPORT_TITLE[ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG])}
              </Option>
              <Option
                value={ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER}
              >
                {t(
                  REPORT_TITLE[
                    ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER
                  ]
                )}
              </Option>
              <Option value={ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR}>
                {t(REPORT_TITLE[ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR])}
              </Option>
              <Option value={ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR}>
                {t(
                  REPORT_TITLE[ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR]
                )}
              </Option>
              {cardsList.some(
                (c) =>
                  c.type == ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
              ) ? (
                <Option
                  value={ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE}
                >
                  {t(
                    REPORT_TITLE[
                      ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
                    ]
                  )}
                </Option>
              ) : (
                ""
              )}
              <Option value={ReportType.DETAILED_ANALYSIS_DEFORESTATION}>
                {t(REPORT_TITLE[ReportType.DETAILED_ANALYSIS_DEFORESTATION])}
              </Option>
              <Option value={ReportType.AGRO_CREDIT}>
                {t(REPORT_TITLE[ReportType.AGRO_CREDIT])}
              </Option>
              <Option value={ReportType.SINISTER}>
                {t(REPORT_TITLE[ReportType.SINISTER])}
              </Option>
              <Option value={ReportType.SOY_DEFORESTATION_CAR}>
                {t(REPORT_TITLE[ReportType.SOY_DEFORESTATION_CAR])}
              </Option>
              <Option value={ReportType.SOY_DEFORESTATION_MUNICIPALITY}>
                {t(REPORT_TITLE[ReportType.SOY_DEFORESTATION_MUNICIPALITY])}
              </Option>
              <Option value={ReportType.RENOVABIO_CERTIFICATION}>
                {t(REPORT_TITLE[ReportType.RENOVABIO_CERTIFICATION])}
              </Option>
              <Option value={ReportType.RENOVABIO_MONITORING}>
                {t(REPORT_TITLE[ReportType.RENOVABIO_MONITORING])}
              </Option>
              <Option value={ReportType.PORTFOLIO_DIAGNOSIS}>
                {t(REPORT_TITLE[ReportType.PORTFOLIO_DIAGNOSIS])}
              </Option>
              <Option value={ReportType.CERTIFICATION_2BSVS}>
                {t(REPORT_TITLE[ReportType.CERTIFICATION_2BSVS])}
              </Option>
            </Select>
          </Form.Item>
          <Form.Item name="signature" initialValue={null}>
            <Select
              label={t("SIGNATURE")}
              placeholder={t("SELECT")}
              allowClear={true}
            >
              <Option value={true}>{t("WITH_SIGNATURE")}</Option>
              <Option value={false}>{t("NO_SIGNATURE")}</Option>
            </Select>
          </Form.Item>
        </FilterColTypes>
        <FilterColAdditionalInfo>
          <TagFilterSelect>
            <Select
              mode="multiple"
              options={tagsList.map((tag) => ({
                value: tag.name,
                label: tag.name,
                disabled:
                  filterTags.length == 3 && !filterTags.includes(tag.name),
              }))}
              value={filterTags}
              style={{ width: 200 }}
              onChange={(tagNames: string[]) => {
                setFilterTags(tagNames);
              }}
              placeholder="Tags"
            />
          </TagFilterSelect>
          <Form.Item name="users_names" initialValue={null}>
            <LabelInput>
              <Input label={t("REQUESTED_BY")} autoComplete="off" />
            </LabelInput>
          </Form.Item>
          <Form.Item name="statuses" initialValue={ReportStatus.ALL}>
            <Select label={t("REPORT_STATUS")}>
              <Option value={ReportStatus.ALL}>
                {t(REPORT_STATUS_LABELS[ReportStatus.ALL])}
              </Option>
              <Option value={ReportStatus.PENDING}>
                {t(REPORT_STATUS_LABELS[ReportStatus.PENDING])}
              </Option>
              <Option value={ReportStatus.PROCESSING}>
                {t(REPORT_STATUS_LABELS[ReportStatus.PROCESSING])}
              </Option>
              <Option value={ReportStatus.ERROR}>
                {t(REPORT_STATUS_LABELS[ReportStatus.ERROR])}
              </Option>
              <Option value={ReportStatus.DONE}>
                {t(REPORT_STATUS_LABELS[ReportStatus.DONE])}
              </Option>
            </Select>
          </Form.Item>
        </FilterColAdditionalInfo>
        <FilterCol>
          <Form.Item name="created" initialValue={null}>
            <RangePicker
              className="filter-range-picker"
              label={t("REQUESTED_AT")}
              allowEmpty={[true, true]}
              format="DD/MM/YYYY"
              placeholder={[t("SELECT"), t("SELECT")]}
            />
          </Form.Item>
          <Button htmlType="submit">{t("APPLY")}</Button>
          <Button
            variant="outlined"
            onClick={() => {
              form.resetFields();
              setFilterTags([]);
              handleClearFiltersAndRefreshReports();
            }}
          >
            {t("CLEAR")}
          </Button>
        </FilterCol>
      </FiltersContainer>
    </FilterForm>
  );
}

export default ReportRequestFilter;
