import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import ReportRequestFilter from "./ReportRequestFilter";

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useSearchParams: () => [
    {
      get: () => {
        id: 0;
      },
    },
  ],
}));

afterEach(() => {
  cleanup();
});

describe("ReportRequestFilter component", () => {
  it("should render the component", () => {
    render(<ReportRequestFilter />);
    const component = screen.queryByTestId("pb-t-report-request-filter");
    expect(component).toBeInTheDocument();
  });
});
