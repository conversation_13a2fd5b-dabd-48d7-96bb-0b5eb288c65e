import styled from "styled-components";

export const ValidDocumentsTitle = styled.span`
  font-weight: bold;
  color: rgb(158, 164, 172);
  font-size: 14px;
`;

export const SummaryContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  border-top: 1px solid #e2e4e7;
  overflow: hidden;
`;

export const DetailCarDocument = styled.div<{ isValid: boolean }>`
  display: flex;
  align-items: center;
  margin: 0;
  list-style: none;

  span {
    display: flex;
    font-weight: bold;
    height: 100%;
    align-items: center;
    margin-left: 8px;
    margin-top: 2px;
    font-size: 16px;
    color: ${({ isValid }) => (isValid ? "#10a488" : "#BF4C62")};
  }
`;

export const SummaryCard = styled.div`
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  border: 1px solid #e2e4e7;
  padding: 16px;
  gap: 10px;
  align-items: flex-start;
  margin-top: 12px;

  .dropdown-documents-invalid {
    color: rgb(158, 164, 172);
  }

  ul {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    width: 100%;
    margin: 0;
    list-style: none;
    padding: 0;

    li {
    }
  }

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.2;
  }
`;

export const TableListInvalidCARCodes = styled.table`
  thead {
    th {
      text-align: left;
    }
  }
`;

export const TableDataInvalidCarCode = styled.td<{ index: number }>`
  font-size: "14px";
  font-weight: "500";
  height: "40px";
  background: ${({ index }) => (index % 2 == 0 ? "#fff" : "#F2F2F2")};
  padding: 6px 8px;
`;
export const InlineTableButton = styled.button`
  cursor: pointer;
  background: none;
  border: none;
`;
