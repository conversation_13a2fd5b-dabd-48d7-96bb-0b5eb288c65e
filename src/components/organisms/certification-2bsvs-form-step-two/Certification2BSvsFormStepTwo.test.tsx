import { fireEvent, render } from "@testing-library/react";
import { useCertification2BSvs } from "@/hooks/use-certification-2bsvs";
import { Certification2BSvsFormStep } from "@/store/modules/certification-2bsvs/types";
import { ReportType } from "@/types";
import { Certification2BSvsFormStepTwo } from "./Certification2bsvsFormStepTwo";

jest.mock("@/hooks/use-certification-2bsvs", () => ({
  useCertification2BSvs: jest.fn(),
}));

describe("Certification2BSvsFormStepTwo", () => {
  beforeEach(() => {
    (useCertification2BSvs as jest.Mock).mockReturnValue({
      data: {
        fileMetadata: { name: "file-name", type: "type" },
        formStep: Certification2BSvsFormStep.UPLOAD_FILE,
        validCars: ["valid-car"],
        invalidCars: [],
        handleSetFormStep: jest.fn(),
        handleClickCancel: jest.fn(),
        handleClickRequestReport: jest.fn(),
        handleSetLanguage: jest.fn(),
        reportType: ReportType.CERTIFICATION_2BSVS,
        handleUploadFile: jest.fn(),
      },
      CERTIFICATION_2BSVS_LANGUAGES: [
        {
          label: "en",
          value: "en",
        },
        { label: "es", value: "es" },
      ],
    });
  });

  it("should render correctly", () => {
    const { getByTestId } = render(<Certification2BSvsFormStepTwo />);

    expect(getByTestId("div-summary-card")).toBeInTheDocument();
  });

  it("should call download xls when button is clicked", () => {
    const handleDownloadInvalidDocumentsXLS = jest.fn();

    (useCertification2BSvs as jest.Mock).mockReturnValue({
      ...useCertification2BSvs(),
      handleDownloadInvalidDocumentsXLS,
    });

    const { getByTestId } = render(<Certification2BSvsFormStepTwo />);

    const button = getByTestId("button-download-xls");

    fireEvent.click(button);

    expect(getByTestId("div-summary-card")).toBeInTheDocument();
  });

  it("should render invalid documents table if invalid document exists on state", () => {
    (useCertification2BSvs as jest.Mock).mockReturnValue({
      ...useCertification2BSvs(),
      data: {
        ...useCertification2BSvs().data,
        invalidCars: ["invalid-car"],
      },
      isToggleInvalidDocumentsOpen: true,
    });

    const { getByTestId } = render(<Certification2BSvsFormStepTwo />);
    const component = getByTestId("table-data-invalid-car-code");

    expect(component).toBeInTheDocument();
  });

  it("should call setToggleInvalidDocumentsOpen when button is clicked", () => {
    const setIsToggleInvalidDocumentsOpen = jest.fn();
    (useCertification2BSvs as jest.Mock).mockReturnValue({
      ...useCertification2BSvs(),
      data: {
        ...useCertification2BSvs().data,
        invalidCars: ["invalid-car"],
      },
      isToggleInvalidDocumentsOpen: true,
      setIsToggleInvalidDocumentsOpen,
    });

    const { getByTestId } = render(<Certification2BSvsFormStepTwo />);
    const button = getByTestId("button-toggle-documents-car-invalid");

    fireEvent.click(button);

    expect(setIsToggleInvalidDocumentsOpen).toHaveBeenCalled();
  });
});
