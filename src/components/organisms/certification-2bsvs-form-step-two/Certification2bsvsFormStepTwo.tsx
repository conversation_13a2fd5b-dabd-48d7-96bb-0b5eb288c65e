import { useCertification2BSvs } from "@/hooks/use-certification-2bsvs";
import { FC } from "react";
import { Al<PERSON>, Button } from "antd";
import { useTranslation } from "react-i18next";
import {
  SummaryContent,
  ValidDocumentsTitle,
  DetailCarDocument,
  SummaryCard,
  InlineTableButton,
  TableDataInvalidCarCode,
  TableListInvalidCARCodes,
} from "./Certification2bsvsFormStepTwo.styles";
import { icon_person_cancel, icon_person_check } from "@/assets";
import {
  CloudDownloadOutlined,
  DownOutlined,
  UpOutlined,
} from "@ant-design/icons";

export const Certification2BSvsFormStepTwo: FC = () => {
  const {
    data,
    isToggleInvalidDocumentsOpen,
    setIsToggleInvalidDocumentsOpen,
    handleDownloadInvalidDocumentsXLS,
  } = useCertification2BSvs();
  const { t } = useTranslation();

  return (
    <>
      <SummaryCard style={{ width: "100%" }} data-testid="div-summary-card">
        <ValidDocumentsTitle>{t("VALID_CARS")}</ValidDocumentsTitle>
        <DetailCarDocument isValid>
          <img src={icon_person_check} alt="Valid document icon" />
          <span>{data.validCars.length}</span>
        </DetailCarDocument>
      </SummaryCard>

      <SummaryCard
        style={{
          display: data.invalidCars.length ? "flex" : "none",
        }}
      >
        <ValidDocumentsTitle>{t("INVALID_CARS")}</ValidDocumentsTitle>
        <DetailCarDocument isValid={false}>
          <img src={icon_person_cancel} alt="Valid document icon" />{" "}
          <span>{data.invalidCars.length}</span>
        </DetailCarDocument>
        <SummaryContent>
          <Alert message={t("INVALID_DOCUMENTS_DESCRIPTION")} type="warning" />
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Button
              size="small"
              icon={<CloudDownloadOutlined />}
              onClick={handleDownloadInvalidDocumentsXLS}
              disabled={!data.invalidCars.length}
              data-testid="button-download-xls"
            >
              {t("DOWNLOAD_XLS_RELATION")}
            </Button>
          </div>

          <TableListInvalidCARCodes>
            <thead>
              <tr>
                <th>
                  <InlineTableButton
                    data-testid="button-toggle-documents-car-invalid"
                    onClick={() => {
                      setIsToggleInvalidDocumentsOpen(
                        !isToggleInvalidDocumentsOpen
                      );
                    }}
                  >
                    {isToggleInvalidDocumentsOpen ? (
                      <DownOutlined />
                    ) : (
                      <UpOutlined />
                    )}
                  </InlineTableButton>
                  <span className="dropdown-documents-invalid">
                    {t("DOCUMENTS")}
                  </span>
                </th>
              </tr>
            </thead>
            <tbody>
              {(isToggleInvalidDocumentsOpen ? data.invalidCars : []).map(
                (car, index) => {
                  return (
                    <tr key={index}>
                      <TableDataInvalidCarCode
                        data-testid="table-data-invalid-car-code"
                        index={index}
                      >
                        {car}
                      </TableDataInvalidCarCode>
                    </tr>
                  );
                }
              )}
            </tbody>
          </TableListInvalidCARCodes>
        </SummaryContent>
      </SummaryCard>
    </>
  );
};
