import styled from "styled-components";

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: auto;
  gap: 32px;
  margin: 16px 0;

  p {
    margin: 0;
  }

  h1 {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
  }

  h2 {
    font-size: 16px;
    font-weight: 500;
  }

  h4 {
    margin-bottom: 8px;
  }

  .link-download-csv {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40%;
    border: 1px solid;
    border-radius: 8px;
    padding: 6px 2px;
    border-color: #00b277;
    color: #00b277;
    transition: opacity 0.3s;
    margin-bottom: 12px;
    background: #fff;
    font-weight: bold;
    max-width: 200px;

    &:hover {
      cursor: pointer;
      opacity: 0.7;
    }
  }

  .text-description-upload-csv {
    color: rgb(149, 149, 149);
  }
`;

export const DraggerContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  border: 1px dashed #00b277;
  border-radius: 8px;
  transition: border-color 0.2s;
  padding: 4px;
  background: rgba(31, 169, 109, 0.05);
  font-weight: 300;
  text-align: center;
  &:hover {
    cursor: pointer;
    border-color: #00b277;
  }
`;

export const DraggerWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  width: 100%;
  margin: auto 0;

  img {
    margin-bottom: 8px;
  }

  p,
  span {
    font-size: 12px;
  }

  p {
    margin: 0;
  }

  span + p {
    font-size: 10px;
    margin-top: 8px;
  }
`;

export const HighlightedText = styled.span`
  color: #10a488;
`;
