import { useCertification2BSvs } from "@/hooks/use-certification-2bsvs";
import { Certification2BSvsFormStep } from "@/store/modules/certification-2bsvs/types";
import { Certification2BSvsFormStepOne } from "./Certification2bsvsFormStepOne";
import { fireEvent, render } from "@testing-library/react";
import { ReportType } from "@types";

jest.mock("@/hooks/use-certification-2bsvs", () => ({
  useCertification2BSvs: jest.fn(),
}));

describe("Certification2BSvsFormStepOne", () => {
  beforeEach(() => {
    (useCertification2BSvs as jest.Mock).mockReturnValue({
      data: {
        fileMetadata: { name: "file-name", type: "type" },
        formStep: Certification2BSvsFormStep.UPLOAD_FILE,
        validCars: ["valid-car"],
        invalidCars: [],
        handleSetFormStep: jest.fn(),
        handleClickCancel: jest.fn(),
        handleClickRequestReport: jest.fn(),
        handleSetLanguage: jest.fn(),
        reportType: ReportType.CERTIFICATION_2BSVS,
        handleUploadFile: jest.fn(),
      },
      CERTIFICATION_2BSVS_LANGUAGES: [
        {
          label: "en",
          value: "en",
        },
        { label: "es", value: "es" },
      ],
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component correctly", () => {
    const { getByTestId } = render(<Certification2BSvsFormStepOne />);

    expect(getByTestId("section-upload-file-description")).toBeInTheDocument();
  });

  it("should call handleSetLaguange when radio button click clicked", () => {
    const handleSetLanguage = jest.fn();

    (useCertification2BSvs as jest.Mock).mockReturnValue({
      ...useCertification2BSvs(),
      data: { ...useCertification2BSvs().data, language: "es" },
      CERTIFICATION_2BSVS_LANGUAGES: [
        {
          label: "en",
          value: "en",
        },
        { label: "es", value: "es" },
      ],
      handleSetLanguage,
    });
    const { getByTestId } = render(<Certification2BSvsFormStepOne />);

    const radio = getByTestId("radio-group-languages").querySelector("input");

    fireEvent.click(radio);

    expect(handleSetLanguage).toHaveBeenCalled();
  });

  it("should call handleUploadBatchFile when a file is uploaded", () => {
    const handleUploadFile = jest.fn();

    (useCertification2BSvs as jest.Mock).mockReturnValueOnce({
      ...useCertification2BSvs(),
      data: {
        ...useCertification2BSvs().data,
        fileMetadata: { name: "", type: "" },
      },
      handleUploadFile,
    });
    const { getByTestId } = render(<Certification2BSvsFormStepOne />);
    const uploadInput = getByTestId("dragger-upload-file");

    const file = new File(["content"], "test.csv", { type: "text/csv" });

    if (uploadInput) {
      fireEvent.change(uploadInput, { target: { files: [file] } });
    }

    expect(handleUploadFile).toHaveBeenCalled();
  });
});
