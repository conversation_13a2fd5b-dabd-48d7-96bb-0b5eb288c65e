import { useCertification2BSvs } from "@/hooks/use-certification-2bsvs";
import { FC } from "react";
import { Radio } from "antd";
import { useTranslation } from "react-i18next";
import {
  Content,
  <PERSON>agger<PERSON><PERSON>r,
  <PERSON><PERSON>Wrapper,
  HighlightedText,
} from "./Certification2bsvsFormStepOne.styles";
import { MODEL_BY_REPORT_TYPE } from "@constants";
import Dragger from "antd/lib/upload";
import { RemovableListFile } from "@/components/molecules";
import { icon_cloud_upload } from "@/assets";

export const Certification2BSvsFormStepOne: FC = () => {
  const { t } = useTranslation();
  const {
    CERTIFICATION_2BSVS_LANGUAGES,
    data,
    handleSetLanguage,
    handleUploadFile,
    handleRemoveFile,
  } = useCertification2BSvs();

  return (
    <>
      <Content>
        <section>
          <h4>{t("SELECT_REPORT_LANGUAGE")}</h4>
          <Radio.Group
            size="small"
            data-testid="radio-group-languages"
            options={CERTIFICATION_2BSVS_LANGUAGES}
            value={data.language}
            onChange={({ target }) => {
              handleSetLanguage(target.value);
            }}
          />
        </section>

        <section data-testid="section-upload-file-description">
          <h2>{t("UPLOAD_DOCUMENTS_FILE")}</h2>
          <p className="text-description-upload-csv">
            {t("INFO_UPLOAD_CSV_RENOVA_BIO")}
          </p>
        </section>

        <a
          href={MODEL_BY_REPORT_TYPE[data.reportType]}
          download
          target="_blank"
          className="link-download-csv"
        >
          {t("DOWNLOAD_SAMPLE_CSV")}
        </a>

        {!data.fileMetadata.name && (
          <DraggerContainer>
            <Dragger
              name="file"
              showUploadList={false}
              accept={".csv,.txt"}
              data-testid="dragger-upload-file"
              beforeUpload={(file) => {
                handleUploadFile(file);
                return false;
              }}
            >
              <DraggerWrapper>
                <img
                  src={icon_cloud_upload}
                  alt="Upload file"
                  width="24"
                  height="24"
                />
                <p>{t("DRAG_DROP_OR")} </p>
                <HighlightedText>
                  {t("CLICK_TO_UPLOAD_FROM_FILES")}
                </HighlightedText>
              </DraggerWrapper>
            </Dragger>
          </DraggerContainer>
        )}

        {data.fileMetadata.name && (
          <RemovableListFile
            fileDetails={"Detalhes"}
            filename={data.fileMetadata.name}
            onRemove={handleRemoveFile}
            index={1}
          />
        )}
      </Content>
    </>
  );
};
