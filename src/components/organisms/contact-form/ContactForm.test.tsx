import ContactForm from "./ContactForm";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useContactForm } from "@/hooks/use-contact-form";
import { IdentificationType } from "@types";

jest.mock("@/services");

jest.mock("antd", () => ({
  ...jest.requireActual("antd"),
  notification: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("@/hooks/use-contact-form");

describe("ContactForm component", () => {
  (useContactForm as jest.Mock).mockReturnValue({
    isSaving: false,
    onRemoveItem: jest.fn(),
    createContacts: jest.fn(),
    onAddItem: jest.fn(),
    onItemChange: jest.fn(),
    value: [
      {
        key: 0,
        document: "",
        name: "",
        cities: [],
        CAR: [],
        type: IdentificationType.DOCUMENT,
      },
    ],
  });

  it("should render the component", () => {
    render(<ContactForm onClose={() => {}} open={true} />);
    const component = screen.queryByTestId("pb-t-contact-form");
    expect(component).toBeInTheDocument();
  });

  it("should render the component", () => {
    render(<ContactForm onClose={() => {}} open={true} />);
    const component = screen.queryByTestId("pb-t-contact-form");
    expect(component).toBeInTheDocument();
  });

  it("should add a new item when onAddItem is called", () => {
    const { getByTestId } = render(
      <ContactForm onClose={() => {}} open={true} />
    );
    const addButton = getByTestId("button-create-contact");
    fireEvent.click(addButton);
    const items = screen.getAllByTestId("pb-t-contact-form");
    expect(items.length).toBe(1);
  });

  it("should call createContact when createContacts is called", async () => {
    const createContacts = jest.fn();
    (useContactForm as jest.Mock).mockReturnValue({
      ...useContactForm({} as any),
      createContacts,
    });
    const { getByTestId } = render(
      <ContactForm onClose={() => {}} open={true} />
    );
    const createButton = getByTestId("button-create-contact");
    fireEvent.click(createButton);
    await waitFor(() => expect(createContacts).toHaveBeenCalled());
  });

  it("should call onRemoveItem when button action is clicked", () => {
    const onRemoveItem = jest.fn();
    (useContactForm as jest.Mock).mockReturnValueOnce({
      ...useContactForm({} as any),
      value: [
        ...useContactForm({} as any).value,
        {
          key: 1,
          document: "09999999999",
          name: "john-doe",
          cities: [],
          CAR: [],
          type: IdentificationType.DOCUMENT,
        },
      ],
      onRemoveItem,
    });
    const { getAllByTestId } = render(<ContactForm onClose={() => {}} open />);

    const buttons = getAllByTestId("pb-t-form-list-item-action");

    const button = buttons[0];

    fireEvent.click(button);

    expect(onRemoveItem).toHaveBeenCalled();
  });

  it("should call onClose if cancel button is clicked", () => {
    const onClose = jest.fn();
    const { getByTestId } = render(<ContactForm onClose={onClose} open />);

    const button = getByTestId("button-cancel-create-contact");

    fireEvent.click(button);

    expect(onClose).toHaveBeenCalled();
  });
});
