import { ContactFormItem, FormListItem } from "@components";
import { Button as Antd<PERSON><PERSON>on } from "antd";
import { <PERSON><PERSON>, Drawer } from "brainui";
import { useTranslation } from "react-i18next";
import {
  <PERSON>FormList,
  ContactFormWrapper,
  FooterOptions,
} from "./ContactForm.styles";
import { useContactForm } from "@/hooks/use-contact-form";

interface ContactFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

function ContactForm({ open, onClose, onSuccess }: ContactFormProps) {
  const { t } = useTranslation();

  const {
    createContacts,
    isSaving,
    onAddItem,
    onItemChange,
    onRemoveItem,
    value,
  } = useContactForm({ onSuccess });

  return (
    <Drawer
      data-testid="pb-t-contact-form"
      title={t("NEW_CONTACT")}
      open={open}
      closable={false}
      width={585}
      onClose={onClose}
    >
      <ContactFormWrapper>
        <ContactFormList>
          {value.map((d, index) => (
            <FormListItem
              multiple
              key={d.key}
              canRemove={index != value.length - 1}
              count={index + 1}
              disabled={isSaving}
              onAdd={onAddItem}
              onRemove={() => onRemoveItem(d)}
            >
              <ContactFormItem
                disabled={isSaving}
                onChange={(value) => onItemChange(d.key, value)}
              />
            </FormListItem>
          ))}
        </ContactFormList>

        <FooterOptions>
          <AntdButton
            data-testid="button-cancel-create-contact"
            type="ghost"
            onClick={() => onClose()}
            disabled={isSaving}
          >
            {t("CANCEL")}
          </AntdButton>
          <Button
            data-testid="button-create-contact"
            onClick={() => createContacts()}
            disabled={isSaving}
          >
            {t("CREATE_CONTACTS")}
          </Button>
        </FooterOptions>
      </ContactFormWrapper>
    </Drawer>
  );
}

export default ContactForm;
