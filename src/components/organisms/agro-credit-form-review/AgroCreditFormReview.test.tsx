import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import AgroCreditFormReview from "./AgroCreditFormReview";
import { useRequestForm } from "@/hooks/use-request-form";
import { useAgroCreditFormReview } from "@/hooks/use-agro-credit-form-review";

afterEach(() => {
  cleanup();
});

jest.mock("react-i18next", () => {
  return {
    useTranslation: jest.fn(() => ({
      t: (text) => text,
    })),
  };
});

jest.mock("@/hooks/use-request-form", () => ({
  useRequestForm: jest.fn(),
}));

jest.mock("@/hooks/use-agro-credit-form-review", () => ({
  useAgroCreditFormReview: jest.fn(),
}));

describe("AgroCreditFormReview component", () => {
  const handleCloseRequestFormMock = jest.fn();
  const downloadInvalidDocumentsMock = jest.fn();
  beforeEach(() => {
    (useAgroCreditFormReview as jest.Mock).mockReturnValue({
      downloadInvalidDocuments: downloadInvalidDocumentsMock,
    });

    (useRequestForm as jest.Mock).mockReturnValue({
      handleCloseRequestForm: handleCloseRequestFormMock,
    });
  });
  it("should render the component", () => {
    render(
      <AgroCreditFormReview
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-agro-credit-review");
    expect(component).toBeInTheDocument();
  });

  it.each(["BACK", "NEXT", "CANCEL"])(
    "should handle with button %p click",
    (text) => {
      const onChangeMock = jest.fn();
      const onBackMock = jest.fn();
      const onNextMock = jest.fn();
      const { getByText } = render(
        <AgroCreditFormReview
          data={{
            file: null,
            filename: "",
            documents: [],
            invalidRows: [],
            scores: [],
            tags: [],
          }}
          onBack={onBackMock}
          onChange={onChangeMock}
          onNext={onNextMock}
        />
      );

      const dict: Record<string, jest.Mock> = {
        BACK: onBackMock,
        NEXT: onNextMock,
        CANCEL: onChangeMock,
      };

      const button = getByText(text);

      fireEvent.click(button);

      expect(dict[text]).toHaveBeenCalled();
    }
  );

  it("should render invalid rows", () => {
    const invalidRows = [{ document: "123456789", debt: "11000" }];
    const { getByText } = render(
      <AgroCreditFormReview
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: invalidRows,
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    expect(getByText(invalidRows[0].document)).toBeInTheDocument();
  });

  it("should expand table with invalid documents", () => {
    const invalidRows = [{ document: "123456789", debt: "11000" }];
    const { getByTestId } = render(
      <AgroCreditFormReview
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: invalidRows,
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    const button = getByTestId("button-set-invalid-document-expanded");
    fireEvent.click(button);

    expect(getByTestId("icon-up")).toBeInTheDocument();
  });

  it("should show documents length", () => {
    const documents = [
      { document: "09938181825", debt: "11000" },
      { document: "34182112000100", debt: "11000" },
    ];
    const { getByText } = render(
      <AgroCreditFormReview
        data={{
          file: null,
          filename: "",
          documents,
          invalidRows: [],
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    expect(getByText("CPFs")).toBeInTheDocument();
    expect(getByText("CNPJs")).toBeInTheDocument();
  });

  it("should download invalid documents", () => {
    const invalidDocuments = [
      { document: "09938181825", debt: "11000" },
      { document: "34182112000100", debt: "11000" },
    ];
    const { getByTestId } = render(
      <AgroCreditFormReview
        data={{
          file: null,
          filename: "",
          documents: [],
          invalidRows: invalidDocuments,
          scores: [],
          tags: [],
        }}
        onBack={() => {}}
        onChange={() => {}}
        onNext={() => {}}
      />
    );

    const button = getByTestId("button-download-xml");

    fireEvent.click(button);

    expect(downloadInvalidDocumentsMock).toHaveBeenCalled();
  });
});
