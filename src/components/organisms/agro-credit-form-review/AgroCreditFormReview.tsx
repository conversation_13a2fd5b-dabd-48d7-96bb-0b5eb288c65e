import { useTranslation } from "react-i18next";
import {
  Content,
  InlineTableButton,
  Options,
  Summary,
  SummaryContent,
  SummaryCount,
  SummaryHeader,
  SummaryHeaderSection,
  Wrapper,
} from "./AgroCreditFormReview.styles";
import { <PERSON><PERSON>, But<PERSON> } from "antd";
import { useState } from "react";
import { icon_person_cancel, icon_person_check } from "../../../assets";
import { AgroCreditFormState } from "../agro-credit-form/AgroCreditForm";
import {
  CloudDownloadOutlined,
  DownOutlined,
  UpOutlined,
} from "@ant-design/icons";
import { getDocumentType } from "@utils";
import { useRequestForm } from "@/hooks/use-request-form";
import { useAgroCreditFormReview } from "@/hooks/use-agro-credit-form-review";

interface AgroCreditFormReviewProps {
  data: AgroCreditFormState;
  onChange: () => void;
  onBack: () => void;
  onNext: () => void;
}

function AgroCreditFormReview({
  data,
  onChange,
  onBack,
  onNext,
}: AgroCreditFormReviewProps) {
  const { t } = useTranslation();
  const { handleCloseRequestForm } = useRequestForm();
  const { downloadInvalidDocuments } = useAgroCreditFormReview();
  const [isInvalidDocsExpanded, setIsInvalidDocsExpanded] = useState(true);

  function handleClose() {
    onChange();
    handleCloseRequestForm();
  }

  return (
    <Wrapper data-testid="pb-t-agro-credit-review">
      <Content>
        <section>
          <h2>{t("REVIEW_UPLOADED_DOCUMENTS")}</h2>
          <p>{t("REVIEW_UPLOADED_DOCUMENTS_DESCRIPTION")}</p>
        </section>

        <Summary style={{ flexShrink: 0 }}>
          <SummaryHeader>
            <SummaryHeaderSection>
              <h2>{t("VALID_DOCUMENTS")}</h2>
              <SummaryCount>
                <img src={icon_person_check} alt="Valid document icon" />
                <span style={{ color: "#118385" }}>
                  {data.documents.length}
                </span>
              </SummaryCount>
            </SummaryHeaderSection>
            <SummaryHeaderSection
              style={{ color: "#8A9292", fontSize: "12px" }}
            >
              <p>
                <strong>
                  {
                    data.documents.filter(
                      (d) => getDocumentType(d.document) == "CPF"
                    ).length
                  }
                </strong>{" "}
                CPFs
              </p>
              <p>
                <strong>
                  {
                    data.documents.filter(
                      (d) => getDocumentType(d.document) == "CNPJ"
                    ).length
                  }
                </strong>{" "}
                CNPJs
              </p>
            </SummaryHeaderSection>
          </SummaryHeader>
        </Summary>

        <Summary>
          <SummaryHeader>
            <SummaryHeaderSection>
              <h2>{t("INVALID_DOCUMENTS")}</h2>
              <SummaryCount>
                <img src={icon_person_cancel} alt="Invalid document icon" />
                <span style={{ color: "#A8001F" }}>
                  {data.invalidRows.length}
                </span>
              </SummaryCount>
            </SummaryHeaderSection>
          </SummaryHeader>
          <SummaryContent>
            <Alert
              message={t("INVALID_DOCUMENTS_DESCRIPTION")}
              type="warning"
            />
            <div
              style={{
                overflow: "auto",
                display: "flex",
                flexDirection: "column",
                width: "100%",
              }}
            >
              <table>
                <thead>
                  <tr>
                    <th style={{ textAlign: "left" }}>
                      <div
                        style={{
                          display: "flex",
                          gap: "5px",
                          alignItems: "center",
                          color: "#717171",
                        }}
                      >
                        <InlineTableButton
                          data-testid="button-set-invalid-document-expanded"
                          onClick={() => {
                            setIsInvalidDocsExpanded(!isInvalidDocsExpanded);
                          }}
                        >
                          {isInvalidDocsExpanded ? (
                            <DownOutlined data-testid="icon-down" />
                          ) : (
                            <UpOutlined data-testid="icon-up" />
                          )}
                        </InlineTableButton>
                        {t("DOCUMENT")}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {(isInvalidDocsExpanded ? data.invalidRows : []).map(
                    (row, index) => {
                      return (
                        <tr key={index}>
                          <td
                            style={{
                              fontSize: "14px",
                              fontWeight: "500",
                              height: "40px",
                              background: index % 2 == 0 ? "#fff" : "#F2F2F2",
                            }}
                          >
                            {row.document}
                          </td>
                        </tr>
                      );
                    }
                  )}
                </tbody>
              </table>
            </div>
            <div style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                size="small"
                data-testid="button-download-xml"
                icon={<CloudDownloadOutlined />}
                onClick={() => downloadInvalidDocuments(data)}
                disabled={!data.invalidRows.length}
              >
                {t("DOWNLOAD_XLS_RELATION")}
              </Button>
            </div>
          </SummaryContent>
        </Summary>
      </Content>
      <Options>
        <Button onClick={handleClose}>{t("CANCEL")}</Button>
        <div style={{ display: "flex", gap: "16px" }}>
          <Button onClick={onBack}>{t("BACK")}</Button>
          <Button type="primary" onClick={onNext}>
            {t("NEXT")}
          </Button>
        </div>
      </Options>
    </Wrapper>
  );
}

export default AgroCreditFormReview;
