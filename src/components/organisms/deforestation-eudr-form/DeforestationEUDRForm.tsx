import { FC, useEffect } from "react";
import { Select } from "antd";
import { Wrapper, Content } from "./DeforestationEUDRForm.styles";
import { useReportAnalysisDeforestationEUDR } from "@/hooks/use-report-analysis-deforestation-eudr";
import { Commodities } from "@utils";

export const DeforestationEUDRForm: FC<{
  onChange: (data: any) => void;
  isSubmitted: boolean;
  data: any;
}> = ({ onChange, data, isSubmitted }) => {
  const {
    selectLanguageOptions,
    selectLanguagePlaceholder,
    languageSelected,
    setLanguageSelected,
    translate,
  } = useReportAnalysisDeforestationEUDR();

  useEffect(() => {
    const newValue = {
      ...data,
      data: {
        ...(data.data || {}),
        language: languageSelected,
      },
    };
    onChange(newValue);
  }, [languageSelected]);

  return (
    <Content>
      <Wrapper data-testid="deforestation-eudr-wrapper">
        <Select
          placeholder={selectLanguagePlaceholder}
          value={languageSelected}
          options={selectLanguageOptions}
          id="deforestation-eudr-language-select"
          onSelect={(language: string) => {
            setLanguageSelected(language);
          }}
        />
        <Select
          defaultValue={null}
          placeholder={translate("COMMODITY")}
          value={data.data?.commodity}
          options={Object.values(Commodities).map((c) => ({
            value: c,
            label: translate(c),
          }))}
          onSelect={(selectedCommodity: string) => {
            const newValue = {
              ...data,
              data: {
                ...(data.data || {}),
                commodity: selectedCommodity,
              },
            };
            onChange(newValue);
          }}
          status={isSubmitted && !data.data?.commodity ? "error" : ""}
        />
      </Wrapper>
    </Content>
  );
};
