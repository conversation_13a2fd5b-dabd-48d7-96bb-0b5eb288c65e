import { Form } from "antd";
import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  overflow: hidden;
`;

export const Content = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
  overflow: auto;
  gap: 10px;
  padding-right: 20px;
`;

export const ArForm = styled(Form)`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  gap: 14px;
`;

export const Actions = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 14px;
`;
