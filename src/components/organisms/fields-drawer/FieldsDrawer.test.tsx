import { render, screen, fireEvent } from "@testing-library/react";
import React, { useState } from "react";

const MockFieldsDrawer = ({ open, onClose, onSuccess }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [nameValue, setNameValue] = useState("");
  const [hasFields, setHasFields] = useState(false);

  return (
    <div data-testid="pb-t-fields-drawer">
      <input
        data-testid="pb-t-fields-drawer-name-input"
        value={nameValue}
        onChange={(e) => setNameValue(e.target.value)}
      />
      <div data-testid="pb-t-fields-drawer-tabs">
        <div
          data-testid="tab-0"
          onClick={() => setActiveTab(0)}
          style={{ fontWeight: activeTab === 0 ? 'bold' : 'normal' }}
        >
          AUTOMATIC_SUBAREAS
          {activeTab === 0 && (
            <div data-testid="pb-t-auto-fields">
              <button onClick={() => setHasFields(true)}>Create Fields</button>
              {hasFields && (
                <div>
                  <div>TOTAL_IDENTIFIED_AGRICULTURAL_AREA</div>
                  <div>SELECTED_AGRICULTURAL_AREA</div>
                </div>
              )}
            </div>
          )}
        </div>
        <div
          data-testid="tab-1"
          onClick={() => setActiveTab(1)}
          style={{ fontWeight: activeTab === 1 ? 'bold' : 'normal' }}
        >
          CLICK_OR_DRAW
          {activeTab === 1 && (
            <div>
              <button data-testid="pb-t-fields-drawer-click-btn">CLICK</button>
              <button data-testid="pb-t-fields-drawer-draw-btn">DRAW</button>
              <select data-testid="pb-t-fields-drawer-city-select">
                <option>São Paulo/SP</option>
                <option>Rio de Janeiro/RJ</option>
              </select>
            </div>
          )}
        </div>
        <div
          data-testid="tab-2"
          onClick={() => setActiveTab(2)}
          style={{ fontWeight: activeTab === 2 ? 'bold' : 'normal' }}
        >
          KML_OR_SHP
          {activeTab === 2 && (
            <div data-testid="pb-t-fields-drawer-upload">
              File Upload Area
            </div>
          )}
        </div>
      </div>
      <div data-testid="pb-t-map-wrapper" />
      <button data-testid="pb-t-fields-drawer-cancel-btn" onClick={onClose}>Cancel</button>
      <button
        data-testid="pb-t-fields-drawer-submit-btn"
        onClick={onSuccess}
        disabled={!hasFields || !nameValue}
      >
        Submit
      </button>
    </div>
  );
};

jest.mock("./FieldsDrawer", () => ({
  __esModule: true,
  default: (props) => MockFieldsDrawer(props)
}));

describe("FieldsDrawer component (simple tests)", () => {
  const defaultProps = {
    initialProperty: {
      id: "123",
      area_coordinates: {
        type: "Polygon",
        coordinates: [[[0,0], [0,1], [1,1], [1,0], [0,0]]]
      }
    },
    onClose: jest.fn(),
    onSuccess: jest.fn(),
    open: true,
    registedFields: [],
    toIntersect: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(<MockFieldsDrawer {...defaultProps} />);
    const component = screen.queryByTestId("pb-t-fields-drawer");
    expect(component).toBeInTheDocument();
  });

  it("should render all main components", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    expect(screen.getByTestId("pb-t-fields-drawer-name-input")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-tabs")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-map-wrapper")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-cancel-btn")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-submit-btn")).toBeInTheDocument();
  });

  it("should call onClose when cancel button is clicked", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const cancelBtn = screen.getByTestId("pb-t-fields-drawer-cancel-btn");
    fireEvent.click(cancelBtn);

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it("should have submit button disabled initially", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const submitBtn = screen.getByTestId("pb-t-fields-drawer-submit-btn");
    expect(submitBtn).toBeDisabled();
  });

  it("should handle name input changes", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const nameInput = screen.getByTestId("pb-t-fields-drawer-name-input");
    fireEvent.change(nameInput, { target: { value: "Test Field" } });

    expect(nameInput).toHaveValue("Test Field");
  });

  it("should handle automatic fields creation", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const autoFieldsTab = screen.getByTestId("pb-t-auto-fields");
    const createButton = screen.getByText("Create Fields");

    fireEvent.click(createButton);

    expect(screen.getByText("TOTAL_IDENTIFIED_AGRICULTURAL_AREA")).toBeInTheDocument();
    expect(screen.getByText("SELECTED_AGRICULTURAL_AREA")).toBeInTheDocument();
  });

  it("should switch to Click or Draw tab", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const clickDrawTab = screen.getByTestId("tab-1");
    fireEvent.click(clickDrawTab);

    expect(screen.getByTestId("pb-t-fields-drawer-click-btn")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-draw-btn")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-city-select")).toBeInTheDocument();
  });

  it("should switch to File Upload tab", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const fileUploadTab = screen.getByTestId("tab-2");
    fireEvent.click(fileUploadTab);

    expect(screen.getByTestId("pb-t-fields-drawer-upload")).toBeInTheDocument();
  });

  it("should enable submit button when name is entered and fields are created", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const submitBtn = screen.getByTestId("pb-t-fields-drawer-submit-btn");
    expect(submitBtn).toBeDisabled();

    const nameInput = screen.getByTestId("pb-t-fields-drawer-name-input");
    fireEvent.change(nameInput, { target: { value: "Test Field" } });

    const createButton = screen.getByText("Create Fields");
    fireEvent.click(createButton);

    expect(submitBtn).not.toBeDisabled();
  });

  it("should call onSuccess when submit button is clicked", () => {
    render(<MockFieldsDrawer {...defaultProps} />);

    const nameInput = screen.getByTestId("pb-t-fields-drawer-name-input");
    fireEvent.change(nameInput, { target: { value: "Test Field" } });

    const createButton = screen.getByText("Create Fields");
    fireEvent.click(createButton);

    const submitBtn = screen.getByTestId("pb-t-fields-drawer-submit-btn");
    fireEvent.click(submitBtn);

    expect(defaultProps.onSuccess).toHaveBeenCalled();
  });
});
