import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FileUploadTab from "./FileUploadTab";
import { Feature } from 'ol';
import Point from 'ol/geom/Point';
import LineString from 'ol/geom/LineString';
import Polygon from 'ol/geom/Polygon';

jest.mock("styled-components", () => {
  const styledFunction = (styles) => (props) => props.children || null;

  const styled = (Component) => styledFunction;

  const htmlElements = [
    'div', 'span', 'small', 'p', 'svg', 'img', 'button', 'input', 'select',
    'a', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'form', 'label',
    'table', 'tr', 'td', 'th', 'thead', 'tbody', 'textarea', 'article',
    'section', 'nav', 'aside', 'header', 'footer', 'main', 'path', 'g',
    'circle', 'rect', 'polygon', 'polyline', 'line'
  ];

  htmlElements.forEach(element => {
    styled[element] = styledFunction;
  });

  styled.createGlobalStyle = () => () => null;
  styled.css = (...args) => args;
  styled.keyframes = () => 'animation-name';
  styled.ThemeProvider = ({ children }) => children;

  return styled;
});

jest.mock("brainui", () => {
  const SelectMock = ({
    children,
    onChange,
    onSelect,
    onDeselect,
    onClear,
    value,
    ...props
  }) => (
    <div data-testid={props["data-testid"]}>
      <select
        onChange={(e) => onChange && onChange([e.target.value])}
        data-testid="select-input"
      >
        {children}
      </select>
      <button onClick={() => onSelect && onSelect("Test City/SP")} data-testid="select-button">
        Select
      </button>
      <button onClick={() => onDeselect && onDeselect("Test City/SP")} data-testid="deselect-button">
        Deselect
      </button>
      <button onClick={() => onClear && onClear()} data-testid="clear-button">
        Clear
      </button>
    </div>
  );

  SelectMock.Option = ({ children, value, disabled }) => (
    <option value={value} disabled={disabled}>
      {children}
    </option>
  );

  return {
    Dragger: ({ children, ...props }) => (
      <div data-testid={props["data-testid"]}>
        <div>Dragger Component</div>
        {children}
      </div>
    ),
    Button: ({ children, onClick, ...props }) => (
      <button onClick={onClick} data-testid={props["data-testid"]} {...props}>
        {children}
      </button>
    ),
    Select: SelectMock,
  };
});

jest.mock("@components", () => ({
  GeometryCard: ({
    uid,
    name,
    selected,
    onPointerEnter,
    onPointerleave,
    onClick,
    onRemove,
    onFocus,
    geometry
  }) => (
    <div data-testid={`geometry-card-${uid}`}>
      <div>{name}</div>
      <button
        data-testid={`select-button-${uid}`}
        onClick={() => onClick && onClick()}
      >
        {selected ? "Selected" : "Not Selected"}
      </button>
      <button
        data-testid={`remove-button-${uid}`}
        onClick={() => onRemove && onRemove()}
      >
        Remove
      </button>
      <button
        data-testid={`focus-button-${uid}`}
        onClick={() => onFocus && onFocus()}
      >
        Focus
      </button>
      <button
        data-testid={`hover-button-${uid}`}
        onMouseEnter={() => onPointerEnter && onPointerEnter()}
        onMouseLeave={() => onPointerleave && onPointerleave()}
      >
        Hover
      </button>
    </div>
  ),
}));

jest.mock("@utils", () => ({
  formatNumber: jest.fn((num) => num.toString()),
  getExtent: jest.fn().mockReturnValue([0, 0, 1, 1]),
}));

jest.mock("../FieldsDrawer.styles", () => ({
  GeometryWrapper: ({ children }) => <div data-testid="geometry-wrapper">{children}</div>,
}));

jest.mock("../../../../components/atoms", () => ({
  GeometryIcon: ({ geometry, fill, onClick }) => (
    <div data-testid="geometry-icon">GeometryIcon</div>
  ),
}));

describe("FileUploadTab", () => {
  const mockUploadProps = {
    name: "file",
    multiple: true,
    showUploadList: false,
    accept: ".kml,.shp",
    beforeUpload: jest.fn(),
  };

  const mockSetFields = jest.fn();
  const mockUpdateFieldsOnMap = jest.fn();

  const mockMap = {
    getView: jest.fn().mockReturnValue({
      fit: jest.fn(),
    }),
  };

  const mockFeaturePoint: Feature<Point> = new Feature(new Point([0, 0]));
  const mockFeatureLine: Feature<LineString> = new Feature(new LineString([[0, 0], [1, 1]]));
  const mockFeaturePolygon: Feature<Polygon> = new Feature(new Polygon([[[0, 0], [1, 1], [1, 0], [0, 0]]]));

  const mockFields = [
    {
      uid: "field-1",
      area: 10,
      selected: true,
      features: [mockFeaturePoint],
      geometry: {},
      car: "",
      origin: "MANUAL" as "MANUAL",
    },
    {
      uid: "field-2",
      area: 20,
      selected: false,
      features: [mockFeatureLine],
      geometry: {},
      car: "",
      origin: "MANUAL" as "MANUAL",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component with all required elements", () => {
    render(
      <FileUploadTab
        uploadProps={mockUploadProps}
        fields={mockFields}
        setFields={mockSetFields}
        updateFieldsOnMap={mockUpdateFieldsOnMap}
        map={mockMap}
      />
    );

    expect(screen.getByTestId("pb-t-fields-drawer-upload")).toBeInTheDocument();
    expect(screen.getByTestId("geometry-card-field-1")).toBeInTheDocument();
    expect(screen.getByTestId("geometry-card-field-2")).toBeInTheDocument();
  });

  it("should toggle field selection when clicked", () => {
    render(
      <FileUploadTab
        uploadProps={mockUploadProps}
        fields={mockFields}
        setFields={mockSetFields}
        updateFieldsOnMap={mockUpdateFieldsOnMap}
        map={mockMap}
      />
    );

    const selectButton = screen.getByTestId("select-button-field-1");
    fireEvent.click(selectButton);

    expect(mockSetFields).toHaveBeenCalledWith([
      { ...mockFields[0], selected: false },
      mockFields[1],
    ]);
  });

  it("should remove field when remove button is clicked", () => {
    render(
      <FileUploadTab
        uploadProps={mockUploadProps}
        fields={mockFields}
        setFields={mockSetFields}
        updateFieldsOnMap={mockUpdateFieldsOnMap}
        map={mockMap}
      />
    );

    const removeButton = screen.getByTestId("remove-button-field-1");
    fireEvent.click(removeButton);

    expect(mockSetFields).toHaveBeenCalledWith([mockFields[1]]);
    expect(mockUpdateFieldsOnMap).toHaveBeenCalledWith([mockFields[1]]);
  });

  it("should focus on field when focus button is clicked", () => {
    render(
      <FileUploadTab
        uploadProps={mockUploadProps}
        fields={mockFields}
        setFields={mockSetFields}
        updateFieldsOnMap={mockUpdateFieldsOnMap}
        map={mockMap}
      />
    );

    const focusButton = screen.getByTestId("focus-button-field-1");
    fireEvent.click(focusButton);

    expect(mockMap.getView().fit).toHaveBeenCalled();
  });
})
