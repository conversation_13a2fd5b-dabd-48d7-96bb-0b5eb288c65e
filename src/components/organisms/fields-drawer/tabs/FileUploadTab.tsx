import React from "react";
import { <PERSON><PERSON> } from "brainui";
import { UploadProps } from "antd";
import { GeometryCard } from "@components";
import { formatNumber, getExtent } from "@utils";
import { GeometryWrapper } from "../FieldsDrawer.styles";
import { Field } from "../../../../hooks/use-map-interactions-fields";

interface FileUploadTabProps {
  uploadProps: UploadProps;
  fields: Field[];
  setFields: (fields: Field[]) => void;
  updateFieldsOnMap: (fields: Field[]) => void;
  map: any;
}

const FileUploadTab: React.FC<FileUploadTabProps> = ({
  uploadProps,
  fields,
  setFields,
  updateFieldsOnMap,
  map,
}) => {
  return (
    <div>
      <Dragger
        data-testid="pb-t-fields-drawer-upload"
        {...uploadProps}
        fileList={[]}
        onChange={() => {}}
        onDelete={() => {}}
      />
      <GeometryWrapper>
        {fields.map((field) => (
          <GeometryCard
            key={field.uid}
            uid={field.uid}
            name={`Área de ${formatNumber(field.area, 2)} ha`}
            selected={field.selected}
            geometry={field.geometry}
            onPointerEnter={() => {
              field.features.forEach((feature) =>
                feature.set("highlight", true)
              );
            }}
            onPointerleave={() => {
              field.features.forEach((feature) =>
                feature.set("highlight", false)
              );
            }}
            onClick={() => {
              const fieldIndex = fields.findIndex(
                (p) => p.uid === field.uid
              );
              if (fieldIndex < 0) return;
              const updatedFields = [...fields];
              field.features.forEach((feature) =>
                feature.set("selected", !field.selected)
              );
              updatedFields.splice(fieldIndex, 1, {
                ...field,
                selected: !field.selected,
              });
              setFields(updatedFields);
            }}
            onRemove={() => {
              const updatedFields = fields.filter(
                (p) => p.uid !== field.uid
              );
              setFields(updatedFields);
              updateFieldsOnMap(updatedFields);
            }}
            onFocus={() => {
              if (!map) return;
              const extent = getExtent(field.features);
              map
                .getView()
                .fit(extent, { padding: [25, 25, 25, 25] });
            }}
          />
        ))}
      </GeometryWrapper>
    </div>
  );
};

export default FileUploadTab;
