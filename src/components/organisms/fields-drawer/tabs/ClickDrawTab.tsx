import React, { useState } from "react";
import { Button, Select } from "brainui";
import { useTranslation } from "react-i18next";
import { FaCompressArrowsAlt, FaPencilAlt } from "react-icons/fa";
import { MapInteraction } from "../../../../hooks/use-map-interactions-fields";
import { ClickDrawOptions, ClickDrawWrapper } from "../FieldsDrawer.styles";
import cities from "../../../../assets/data/cities.json";

interface ClickDrawTabProps {
  interaction: MapInteraction;
  changeInteraction: (interaction: MapInteraction) => void;
  map: any;
  selectedCities: any[];
  setSelectedCities: (cities: any[]) => void;
}

const ClickDrawTab: React.FC<ClickDrawTabProps> = ({
  interaction,
  changeInteraction,
  map,
  selectedCities,
  setSelectedCities,
}) => {
  const { t } = useTranslation();

  return (
    <ClickDrawWrapper>
      <ClickDrawOptions>
        <Button
          data-testid="pb-t-fields-drawer-click-btn"
          size="small"
          icon={<FaCompressArrowsAlt />}
          onClick={() => {
            changeInteraction(MapInteraction.CLICK);
          }}
          variant={
            interaction === MapInteraction.CLICK ? "standard" : "outlined"
          }
        >
          {t("CLICK")}
        </Button>
        <Button
          data-testid="pb-t-fields-drawer-draw-btn"
          size="small"
          icon={<FaPencilAlt />}
          onClick={() => {
            changeInteraction(MapInteraction.DRAW);
          }}
          variant={
            interaction === MapInteraction.DRAW ? "standard" : "outlined"
          }
        >
          {t("Desenhe")}
        </Button>
      </ClickDrawOptions>
      <Select
        data-testid="pb-t-fields-drawer-city-select"
        label={t("PROPERTY_CITY")}
        mode="multiple"
        allowClear
        placeholder={t("SEARCH_PROPERTY_CITY")}
        showSearch
        filterOption={(input, option) =>
          option.value
            .toLocaleLowerCase()
            .includes(input.toLocaleLowerCase())
        }
        value={selectedCities.map(
          (city) => `${city.name}/${city.uf}`
        )}
        onChange={(nameUf: string[]) => {
          if (!nameUf.length) return;
          const [name, uf] = nameUf[0].split("/");
          const city = cities.find(
            (c) => c.name === name && c.uf === uf
          );
          if (!city || !map) return;
          const view = map.getView();
          view.setCenter([city.longitude, city.latitude]);
          view.setZoom(13);
        }}
        onSelect={(nameUf: string) => {
          if (!nameUf.length) return;
          const [name, uf] = nameUf.split("/");
          const city = cities.find(
            (c) => c.name === name && c.uf === uf
          );
          if (!city) return;
          setSelectedCities([...selectedCities, city]);
        }}
        onDeselect={(nameUf: string) => {
          if (!nameUf.length) return;
          const [name, uf] = nameUf.split("/");
          const city = cities.find(
            (c) => c.name === name && c.uf === uf
          );
          if (!city) return;
          setSelectedCities(
            selectedCities.filter(
              (c) => c.ibge_code !== city.ibge_code
            )
          );
        }}
        onClear={() => {
          setSelectedCities([]);
        }}
      >
        {cities.map((city) => (
          <Select.Option
            key={city.ibge_code}
            value={`${city.name}/${city.uf}`}
            disabled={
              selectedCities.length === 5 &&
              !selectedCities.find(
                (c) => c.ibge_code === city.ibge_code
              )
            }
          >
            {city.name}/{city.uf}
          </Select.Option>
        ))}
      </Select>
    </ClickDrawWrapper>
  );
};

export default ClickDrawTab;
