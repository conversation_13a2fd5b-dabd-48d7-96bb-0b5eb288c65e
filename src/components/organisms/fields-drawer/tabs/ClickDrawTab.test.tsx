import { render, screen, fireEvent } from "@testing-library/react";
import { MapInteraction } from "../../../../hooks/use-map-interactions-fields";

jest.mock("styled-components", () => {
  const styledFunction = (styles) => (props) => props.children || null;

  const styled = (Component) => styledFunction;

  const htmlElements = [
    'div', 'span', 'small', 'p', 'svg', 'img', 'button', 'input', 'select',
    'a', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'form', 'label',
    'table', 'tr', 'td', 'th', 'thead', 'tbody', 'textarea', 'article',
    'section', 'nav', 'aside', 'header', 'footer', 'main', 'path', 'g',
    'circle', 'rect', 'polygon', 'polyline', 'line'
  ];

  htmlElements.forEach(element => {
    styled[element] = styledFunction;
  });

  styled.createGlobalStyle = () => () => null;
  styled.css = (...args) => args;
  styled.keyframes = () => 'animation-name';
  styled.ThemeProvider = ({ children }) => children;

  return styled;
});

// Mock cities data with multiple cities for testing
jest.mock("../../../../assets/data/cities.json", () => [
  { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 },
  { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 },
  { name: "Third City", uf: "MG", ibge_code: "789", longitude: -43.9378, latitude: -19.9208 },
  { name: "Fourth City", uf: "RS", ibge_code: "101", longitude: -51.2177, latitude: -30.0346 },
  { name: "Fifth City", uf: "PR", ibge_code: "112", longitude: -49.2671, latitude: -25.4284 },
  { name: "Sixth City", uf: "SC", ibge_code: "131", longitude: -48.5482, latitude: -27.5945 }
]);

jest.mock("brainui", () => {
  const SelectMock = ({
    children,
    onChange,
    onSelect,
    onDeselect,
    onClear,
    value,
    filterOption,
    ...props
  }) => (
    <div data-testid={props["data-testid"]}>
      <select
        onChange={(e) => onChange && onChange([e.target.value])}
        data-testid="select-input"
        value={value}
      >
        {children}
      </select>
      <input
        data-testid="filter-input"
        onChange={(e) => {
          // Just call filterOption with a mock value to test it
          if (filterOption) {
            filterOption(e.target.value, { value: "Test City/SP" });
          }
        }}
      />
      <button onClick={() => onSelect && onSelect("Test City/SP")} data-testid="select-button">
        Select Test City
      </button>
      <button onClick={() => onSelect && onSelect("Another City/RJ")} data-testid="select-button-2">
        Select Another City
      </button>
      <button onClick={() => onDeselect && onDeselect("Test City/SP")} data-testid="deselect-button">
        Deselect Test City
      </button>
      <button onClick={() => onDeselect && onDeselect("Another City/RJ")} data-testid="deselect-button-2">
        Deselect Another City
      </button>
      <button onClick={() => onClear && onClear()} data-testid="clear-button">
        Clear
      </button>
    </div>
  );

  SelectMock.Option = ({ children, value, disabled }) => (
    <option value={value} disabled={disabled} data-testid={`option-${value}`}>
      {children}
    </option>
  );

  return {
    Button: ({ children, onClick, variant, ...props }) => (
      <button
        onClick={onClick}
        data-testid={props["data-testid"]}
        data-variant={variant}
      >
        {children}
      </button>
    ),
    Select: SelectMock,
  };
});

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

jest.mock("../FieldsDrawer.styles", () => ({
  ClickDrawWrapper: ({ children }) => <div data-testid="click-draw-wrapper">{children}</div>,
  ClickDrawOptions: ({ children }) => <div data-testid="click-draw-options">{children}</div>,
}));

jest.mock("../../../../components/atoms", () => ({
  GeometryIcon: () => (
    <div data-testid="geometry-icon">GeometryIcon</div>
  ),
}));

import ClickDrawTab from "./ClickDrawTab";

describe("ClickDrawTab", () => {
  const mockChangeInteraction = jest.fn();
  const mockSetSelectedCities = jest.fn();
  const mockSetCenter = jest.fn();
  const mockSetZoom = jest.fn();
  const mockMap = {
    getView: jest.fn().mockReturnValue({
      setCenter: mockSetCenter,
      setZoom: mockSetZoom,
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component with all required elements", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    expect(screen.getByTestId("pb-t-fields-drawer-click-btn")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-draw-btn")).toBeInTheDocument();
    expect(screen.getByTestId("pb-t-fields-drawer-city-select")).toBeInTheDocument();
  });

  it("should call changeInteraction when click button is clicked", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.DRAW}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const clickButton = screen.getByTestId("pb-t-fields-drawer-click-btn");
    fireEvent.click(clickButton);

    expect(mockChangeInteraction).toHaveBeenCalledWith(MapInteraction.CLICK);
  });

  it("should call changeInteraction when draw button is clicked", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const drawButton = screen.getByTestId("pb-t-fields-drawer-draw-btn");
    fireEvent.click(drawButton);

    expect(mockChangeInteraction).toHaveBeenCalledWith(MapInteraction.DRAW);
  });

  it("should apply correct variant to buttons based on current interaction", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const clickButton = screen.getByTestId("pb-t-fields-drawer-click-btn");
    const drawButton = screen.getByTestId("pb-t-fields-drawer-draw-btn");

    expect(clickButton).toHaveAttribute('data-variant', 'standard');
    expect(drawButton).toHaveAttribute('data-variant', 'outlined');
  });

  it("should handle selecting a city", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const selectButton = screen.getByTestId("select-button");
    fireEvent.click(selectButton);

    expect(mockSetSelectedCities).toHaveBeenCalledWith([
      { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 }
    ]);
  });

  it("should handle selecting multiple cities", () => {
    const initialSelectedCities = [
      { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 }
    ];

    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={initialSelectedCities}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const selectButton = screen.getByTestId("select-button-2");
    fireEvent.click(selectButton);

    expect(mockSetSelectedCities).toHaveBeenCalledWith([
      ...initialSelectedCities,
      { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 }
    ]);
  });

  it("should handle deselecting a city", () => {
    const initialSelectedCities = [
      { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 },
      { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 }
    ];

    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={initialSelectedCities}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const deselectButton = screen.getByTestId("deselect-button");
    fireEvent.click(deselectButton);

    expect(mockSetSelectedCities).toHaveBeenCalledWith([
      { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 }
    ]);
  });

  it("should handle clear all cities", () => {
    const selectedCities = [
      { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 },
      { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 }
    ];

    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={selectedCities}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const clearButton = screen.getByTestId("clear-button");
    fireEvent.click(clearButton);

    expect(mockSetSelectedCities).toHaveBeenCalledWith([]);
  });

  it("should update map view when a city is selected", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    // Simulate selecting a city through the onChange handler
    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "Test City/SP" } });

    expect(mockMap.getView).toHaveBeenCalled();
    expect(mockSetCenter).toHaveBeenCalledWith([-46.6333, -23.5505]);
    expect(mockSetZoom).toHaveBeenCalledWith(13);
  });

  it("should not update map view when no city is found", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    // Simulate selecting a non-existent city
    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "NonExistent City/XX" } });

    expect(mockSetCenter).not.toHaveBeenCalled();
    expect(mockSetZoom).not.toHaveBeenCalled();
  });

  it("should not update map view when map is not available", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={null}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    // Simulate selecting a city
    const selectInput = screen.getByTestId("select-input");
    fireEvent.change(selectInput, { target: { value: "Test City/SP" } });

    expect(mockSetCenter).not.toHaveBeenCalled();
    expect(mockSetZoom).not.toHaveBeenCalled();
  });

  it("should handle filtering cities", () => {
    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={[]}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    const filterInput = screen.getByTestId("filter-input");
    fireEvent.change(filterInput, { target: { value: "test" } });

    // This is just testing that the filter function is called
    // The actual filtering is done by the Select component
  });

  it("should display city values correctly", () => {
    const selectedCities = [
      { name: "Test City", uf: "SP", ibge_code: "123", longitude: -46.6333, latitude: -23.5505 },
      { name: "Another City", uf: "RJ", ibge_code: "456", longitude: -43.1729, latitude: -22.9068 }
    ];

    render(
      <ClickDrawTab
        interaction={MapInteraction.CLICK}
        changeInteraction={mockChangeInteraction}
        map={mockMap}
        selectedCities={selectedCities}
        setSelectedCities={mockSetSelectedCities}
      />
    );

    // Just verify the component renders with the selected cities
    const citySelect = screen.getByTestId("pb-t-fields-drawer-city-select");
    expect(citySelect).toBeInTheDocument();
  });
});
