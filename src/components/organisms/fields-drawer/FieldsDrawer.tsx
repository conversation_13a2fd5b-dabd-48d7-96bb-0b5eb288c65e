import React, { useState, useRef } from "react";
import { InfoCircleOutlined } from "@ant-design/icons";
import { AutoFieldsTab, MapWrapper } from "@components";
import { Button as AntdButton, Form, Popconfirm, Switch, Tabs } from "antd";
import { <PERSON><PERSON>, Drawer } from "brainui";
import { useTranslation } from "react-i18next";
import { AiOutlineInfoCircle } from "react-icons/ai";
import VectorSource from "ol/source/Vector";
import VectorLayer from "ol/layer/Vector";
import { PROPERTY_STYLE, SUBAREA_STYLE } from "@constants";
import {
  AreaText,
  Content,
  FooterOptions,
  InfoText,
  LabelInput,
  PopConfirmTitle,
  SwitchSelectionContainer,
  Wrapper,
} from "./FieldsDrawer.styles";
import { useMapInteractions } from "../../../hooks/use-map-interactions-fields";
import { useFileProcessing } from "../../../hooks/use-file-processing-fields";
import { FieldsTabs, useFieldsManagement } from "../../../hooks/use-fields-management";
import ClickDrawTab from "./tabs/ClickDrawTab";
import FileUploadTab from "./tabs/FileUploadTab";

interface FieldsDrawerProps {
  open: boolean;
  registedFields: any[];
  onClose: () => void;
  onSuccess: () => void;
  initialProperty: any;
  toIntersect: boolean;
}

function FieldsDrawer({
  open,
  registedFields,
  onClose,
  onSuccess,
  initialProperty,
  toIntersect,
}: FieldsDrawerProps) {
  const { t } = useTranslation();
  const [source] = useState(new VectorSource());
  const [initialGeometrySource] = useState(new VectorSource());
  const [form] = Form.useForm();
  const [selectedCities, setSelectedCities] = useState([]);
  const [nextTab, setNextTab] = useState<FieldsTabs | null>(null);

  const {
    fields,
    fieldName,
    isSubmitted,
    isSaving,
    currentTab,
    selectedFields,
    totalAreaAutomatedFields,
    selectedAreaAutomatedFields,
    setFields,
    setFieldName,
    saveFields,
    handleClose: closeFields,
    handleAutomaticFields,
    setNextTab: changeTab,
  } = useFieldsManagement({
    initialProperty,
    onSuccess,
    onClose,
    registedFields,
  });

  const {
    map,
    interaction,
    setMap,
    handleMapClick,
    changeInteraction,
    updateFieldsOnMap,
    setActiveFields,
    handleToggleAllAutomaticFields,
  } = useMapInteractions({
    source,
    initialGeometrySource,
    initialProperty,
    toIntersect,
    setFields,
    fields,
  });

  const { uploadProps } = useFileProcessing({
    fields,
    setFields,
    updateFieldsOnMap,
    initialProperty,
    toIntersect,
  });

  const POP_CONFIRM_PROPS = {
    trigger: "click",
    icon: <></>,
    title: (
      <PopConfirmTitle>
        <AiOutlineInfoCircle color="#ebbd59" />
        <span>{t("ALERT_TAB_CHANGE_SUBAREA")}</span>
      </PopConfirmTitle>
    ),
    onConfirm: () => onTabChange(nextTab),
    onCancel: () => onTabChange(currentTab),
    okText: t("YES"),
    cancelText: t("NO"),
  };

  function onTabChange(tab) {
    changeTab(tab);
    setNextTab(null);
  }

  function handleClose() {
    setSelectedCities([]);
    if (map) {
      map.getView().setZoom(13);
    }
    closeFields();
  }

  return (
    <Drawer
      title={t("REGISTER_NEW_SUBAREA")}
      open={open}
      closable={false}
      width={650}
      onClose={handleClose}
      form={form}
      data-testid="pb-t-fields-drawer"
    >
      <Wrapper>
        <Content>
          <LabelInput
            data-testid="pb-t-fields-drawer-name-input"
            label={t(
              [0, 2].includes(currentTab) ? "SUBAREA_PREFIX" : "SUBAREA_NAME"
            )}
            value={fieldName}
            status={isSubmitted && !fieldName.length ? "error" : ""}
            disabled={isSaving}
            onChange={({ target }) => {
              setFieldName(target.value);
            }}
            size="large"
          />
          <Tabs
            data-testid="pb-t-fields-drawer-tabs"
            defaultActiveKey={"0"}
            activeKey={String(currentTab)}
            onChange={(activeKey: string) => setNextTab(Number(activeKey))}
            items={[
              {
                key: "0",
                label: (
                  <Popconfirm {...POP_CONFIRM_PROPS}>
                    {t("AUTOMATIC_SUBAREAS")}
                  </Popconfirm>
                ),
                children: (
                  <div>
                    <AutoFieldsTab
                      propertyId={initialProperty?.id}
                      onCreateFields={handleAutomaticFields}
                    />
                    {fields.length > 0 && (
                      <SwitchSelectionContainer>
                        <Switch
                          checked={
                            selectedFields.length === fields.length
                          }
                          onChange={(checked) =>
                            handleToggleAllAutomaticFields(checked)
                          }
                        />
                        <span>{t("SELECT_ALL_SUBAREAS")}</span>
                      </SwitchSelectionContainer>
                    )}
                  </div>
                ),
              },
              {
                key: "1",
                label: (
                  <Popconfirm {...POP_CONFIRM_PROPS}>
                    {t("CLICK_OR_DRAW")}
                  </Popconfirm>
                ),
                children: (
                  <ClickDrawTab
                    interaction={interaction}
                    changeInteraction={changeInteraction}
                    map={map}
                    selectedCities={selectedCities}
                    setSelectedCities={setSelectedCities}
                  />
                ),
              },
              {
                key: "2",
                label: (
                  <Popconfirm {...POP_CONFIRM_PROPS}>
                    {t("KML_OR_SHP")}
                  </Popconfirm>
                ),
                children: (
                  <FileUploadTab
                    uploadProps={uploadProps}
                    fields={fields}
                    setFields={setFields}
                    updateFieldsOnMap={updateFieldsOnMap}
                    map={map}
                  />
                ),
              },
            ]}
          />
          {currentTab === FieldsTabs.FILE_UPLOAD && fields.length > 0 && (
            <InfoText>
              <InfoCircleOutlined size={15} />
              {t("INFO_SUBAREA_ICON_CLICK")}
            </InfoText>
          )}
          <MapWrapper
            data-testid="pb-t-fields-drawer-map"
            size="large"
            onClick={(e, mapInstance) => handleMapClick(e, mapInstance, currentTab)}
            onMapReady={(mapInstance) => {
              setMap(mapInstance);
              const layer = new VectorLayer({
                source,
                style: (feature) => SUBAREA_STYLE(feature),
              });
              layer.set("id", "fields_layer");
              const initialGeometrylayer = new VectorLayer({
                source: initialGeometrySource,
                style: PROPERTY_STYLE,
              });

              mapInstance.addLayer(layer);
              mapInstance.addLayer(initialGeometrylayer);
            }}
          />
          {currentTab === FieldsTabs.AUTO_FIELDS && (
            <div>
              <AreaText>
                {t("TOTAL_IDENTIFIED_AGRICULTURAL_AREA", {
                  value: totalAreaAutomatedFields.toFixed(2),
                })}
              </AreaText>
              <AreaText>
                {t("SELECTED_AGRICULTURAL_AREA", {
                  value: selectedAreaAutomatedFields.toFixed(2),
                })}
              </AreaText>
            </div>
          )}
        </Content>
        <FooterOptions>
          <AntdButton
            data-testid="pb-t-fields-drawer-cancel-btn"
            type="ghost"
            onClick={() => handleClose()}
            disabled={isSaving}
          >
            {t("CANCEL")}
          </AntdButton>
          <Button
            data-testid="pb-t-fields-drawer-submit-btn"
            onClick={() => saveFields()}
            disabled={isSaving || !selectedFields.length}
          >
            {selectedFields.length > 1
              ? t("REGISTER_N_SUBAREAS", { value: selectedFields.length })
              : t("REGISTER")}
          </Button>
        </FooterOptions>
      </Wrapper>
    </Drawer>
  );
}

export default FieldsDrawer;
