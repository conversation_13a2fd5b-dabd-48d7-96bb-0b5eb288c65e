import React from "react";
import { Select, Space } from "antd";

export type MultiSelectProps = {
  options: Array<{ label: string; value: string }>;
  onChange: (value: Array<string>) => void;
  placeholder: string;
  valuesSelected: Array<string>;
  defaultValue?: Array<string>;
};

export const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  onChange,
  placeholder,
  valuesSelected,
  defaultValue = [],
}) => {
  return (
    <Space direction="vertical" style={{ width: "100%" }}>
      <Select
        mode="multiple"
        size={"medium" as any}
        placeholder={placeholder}
        onChange={onChange}
        style={{ width: "100%" }}
        value={valuesSelected}
        defaultValue={defaultValue}
        data-testid="select-multi-select"
      >
        {options.map((item) => (
          <Select.Option
            key={item.value}
            data-testid={`option-multi-select-${item.value}`}
            value={item.value}
          >
            {item.label}
          </Select.Option>
        ))}
      </Select>
    </Space>
  );
};
