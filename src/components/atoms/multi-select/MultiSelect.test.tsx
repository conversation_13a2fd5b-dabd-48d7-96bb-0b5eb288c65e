import React from 'react';
import { render, screen } from '@testing-library/react';
import { MultiSelect } from './MultiSelect';

jest.mock('antd', () => ({
  Select: ({ children, 'data-testid': testId }) => (
    <div data-testid={testId}>{children}</div>
  ),
  Space: ({ children }) => <div>{children}</div>,
}));

(jest.requireMock('antd').Select).Option = ({ children, 'data-testid': testId }) => (
  <div data-testid={testId}>{children}</div>
);

describe('MultiSelect', () => {
  const mockOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' }
  ];

  const mockOnChange = jest.fn();

  it('should render with the correct props', () => {
    render(
      <MultiSelect
        options={mockOptions}
        onChange={mockOnChange}
        placeholder="Select options"
        valuesSelected={['option1']}
      />
    );

    const selectComponent = screen.getByTestId('select-multi-select');
    expect(selectComponent).toBeInTheDocument();

    mockOptions.forEach(option => {
      const optionElement = screen.getByTestId(`option-multi-select-${option.value}`);
      expect(optionElement).toBeInTheDocument();
      expect(optionElement.textContent).toBe(option.label);
    });
  });

  it('should render with default values', () => {
    render(
      <MultiSelect
        options={mockOptions}
        onChange={mockOnChange}
        placeholder="Select options"
        valuesSelected={['option1']}
        defaultValue={['option2']}
      />
    );

    const selectComponent = screen.getByTestId('select-multi-select');
    expect(selectComponent).toBeInTheDocument();
  });
});
