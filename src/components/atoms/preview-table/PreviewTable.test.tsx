import React from "react";
import { render, screen } from "@testing-library/react";
import PreviewTable from "./PreviewTable";
import { IPreviewTableProps } from "./PreviewTable.interfaces";

describe("PreviewTable", () => {
  const mockData: IPreviewTableProps["data"] = [
    { document: "123456789011", name: "Test 1" },
    { document: "123456789012", name: "Test 2" },
  ];

  it("renders table headers correctly", () => {
    render(<PreviewTable data={mockData} expanded={true} />);
    expect(screen.getByText("document")).toBeInTheDocument();
    expect(screen.getByText("name")).toBeInTheDocument();
  });

  it("renders table rows correctly when expanded is true", () => {
    render(<PreviewTable data={mockData} expanded={true} />);
    expect(screen.getByText("123456789011")).toBeInTheDocument();
    expect(screen.getByText("123456789012")).toBeInTheDocument();
    expect(screen.getByText("Test 1")).toBeInTheDocument();
    expect(screen.getByText("Test 2")).toBeInTheDocument();
  });

  it("does not render table rows when expanded is false", () => {
    render(<PreviewTable data={mockData} expanded={false} />);
    expect(screen.queryByText("123456789011")).not.toBeInTheDocument();
    expect(screen.queryByText("123456789012")).not.toBeInTheDocument();
    expect(screen.queryByText("Test 1")).not.toBeInTheDocument();
    expect(screen.queryByText("Test 2")).not.toBeInTheDocument();
  });

  it("applies alternating row background colors", () => {
    render(<PreviewTable data={mockData} expanded={true} />);

    const rows = screen.getAllByRole("row").slice(1);

    expect(rows[0].querySelector("td")).toHaveStyle("background: #fff");

    expect(rows[1].querySelector("td")).toHaveStyle("background: #F2F2F2");
  });
});
