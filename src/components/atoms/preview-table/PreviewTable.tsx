import { IPreviewTableProps } from "./PreviewTable.interfaces";
import { Table } from "./PreviewTable.styles";

function PreviewTable({ data, expanded }: IPreviewTableProps) {
  const columns = Object.keys(data[0] ?? {});
  const rows = Object.values(data);

  return (
    <Table>
      <thead>
        <tr>
          {columns.map((column, index) => (
            <th key={`th-${index}`} style={{ textAlign: "left" }}>
              <div
                style={{
                  display: "flex",
                  gap: "5px",
                  alignItems: "center",
                  color: "#717171",
                }}
              >
                {column}
              </div>
            </th>
          ))}
        </tr>
      </thead>
      {expanded && (
        <tbody>
          {rows.map((row, index) => {
            return (
              <tr key={`row-${index}`}>
                {Object.values(row).map((value, idx) => (
                  <td
                    key={`col-${idx}`}
                    style={{
                      fontSize: "14px",
                      fontWeight: "500",
                      height: "40px",
                      background: index % 2 == 0 ? "#fff" : "#F2F2F2",
                    }}
                  >
                    {value}
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      )}
    </Table>
  );
}

export default PreviewTable;
