import { cleanup, render, screen } from "@testing-library/react";
import React from "react";
import MapWrapper from "./MapWrapper";

class TestResizeObserver {
  observe() {}
  unobserve() {}
}

beforeAll(() => {
  (window as any).ResizeObserver = TestResizeObserver;
});

afterEach(() => {
  cleanup();
});

describe("MapWrapper component", () => {
  it("should render the component", () => {
    render(<MapWrapper onClick={() => {}}></MapWrapper>);
    const component = screen.queryByTestId("pb-t-map-wrapper");
    expect(component).toBeInTheDocument();
  });
});
