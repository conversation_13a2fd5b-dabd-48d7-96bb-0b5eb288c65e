import { BASEMAP_SOURCE_URLS } from "@constants";
import { BasemapType } from "@types";
import { Map, MapBrowserEvent, View } from "ol";
import { Zoom } from "ol/control";
import TileLayer from "ol/layer/Tile";
import XY<PERSON> from "ol/source/XYZ";
import { useEffect, useRef, useState } from "react";
import { MapContainer } from "./MapWrapper.styles";
import "./styles.css";

interface MapWrapperProps {
  readonly size?: "default" | "large";
  readonly onClick: (event: MapBrowserEvent<MouseEvent>, map: Map) => void;
  readonly onMapReady?: (map: Map) => void;
}

function MapWrapper({ size, onClick, onMapReady }: MapWrapperProps) {
  const mapElement = useRef<HTMLDivElement>(null);
  const [layer] = useState(new TileLayer());
  const [basemapType] = useState(BasemapType.Satellite);

  // Initialize map on first render logic:
  useEffect(() => {
    layer.setSource(
      new XYZ({
        url: BASEMAP_SOURCE_URLS[basemapType],
      })
    );

    // Create the map:
    const initialMap = new Map({
      target: mapElement.current,
      layers: [
        new TileLayer({
          source: new XYZ({
            url: BASEMAP_SOURCE_URLS[basemapType],
          }),
        }),
      ],
      view: new View({
        projection: "EPSG:4326",
        center: [-55.15886688, -9.30063984],
        zoom: 5,
        maxZoom: 17,
      }),
      controls: [new Zoom()],
    });
    onMapReady && onMapReady(initialMap);
    initialMap.on("singleclick", (e) => onClick(e, initialMap));
  }, []);

  return (
    <MapContainer
      ref={mapElement}
      className={
        "map-container" + (size == "large" ? " map-container--lg" : "")
      }
      data-testid="pb-t-map-wrapper"
    ></MapContainer>
  );
}

export default MapWrapper;
