import { AutoComplete } from "antd";
import { FC } from "react";

export type MultiSelectAutoCompleteProps = {
  search: string;
  placeholder: string;
  isDisabled: boolean;
  onTypeSearch: (data: string) => void;
  onChange: (data: string) => void;
  children: React.ReactNode;
};

export const MultiSelectAutoComplete: FC<MultiSelectAutoCompleteProps> = ({
  isDisabled,
  onChange,
  onTypeSearch,
  placeholder,
  search,
  children,
}) => {
  return (
    <AutoComplete
      className="auto-complete-municipality"
      value={search}
      placeholder={placeholder}
      disabled={isDisabled}
      onSearch={onTypeSearch}
      filterOption={(inputValue, option) => {
        return option.children[0]
          .toString()
          .toLowerCase()
          .startsWith(inputValue.toLowerCase());
      }}
      onChange={onChange}
    >
      {children}
    </AutoComplete>
  );
};
