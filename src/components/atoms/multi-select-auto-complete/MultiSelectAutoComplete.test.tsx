import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { MultiSelectAutoComplete, MultiSelectAutoCompleteProps } from "./MultiSelectAutoComplete";
import { AutoComplete } from "antd";

jest.mock("antd", () => ({
  AutoComplete: jest.fn(({ children, ...props }) => (
    <div>
      <input {...props} data-testid="auto-complete" />
      {children}
    </div>
  )),
}));

const defaultProps: MultiSelectAutoCompleteProps = {
  search: "",
  placeholder: "Select municipality",
  isDisabled: false,
  onTypeSearch: jest.fn(),
  onChange: jest.fn(),
  children: <div>Option 1</div>,
};

describe("MultiSelectAutoComplete", () => {
  it("renders correctly", () => {
    render(<MultiSelectAutoComplete {...defaultProps} />);
    expect(screen.getByPlaceholderText("Select municipality")).toBeInTheDocument();
  });

  it("disables the input when isDisabled is true", () => {
    render(<MultiSelectAutoComplete {...defaultProps} isDisabled={true} />);
    expect(screen.getByPlaceholderText("Select municipality")).toBeDisabled();
  });
});
