import { render, screen } from "@testing-library/react";
import { TFunction } from "i18next";
import { getDescriptionGenericError } from "./NotificationDescriptionGenericError";

describe("getDescriptionGenericError", () => {
  const createMockT = () => {
    return ((key: string, options?: { minutes: number }) => {
      const translations: { [key: string]: string } = {
        ERR_GENERIC_QUERY: "An error occurred.",
        TRY_AGAIN_IN_MINUTES: `Please try again in ${options?.minutes} minutes.`,
      };
      return translations[key];
    }) as TFunction;
  };

  it("renders the correct error message with default minutes", () => {
    const mockT = createMockT();

    const { container } = render(getDescriptionGenericError(mockT));

    expect(container.textContent).toBe(
      "An error occurred.Please try again in 5 minutes."
    );

    expect(container.firstChild?.nodeName).toBe("SPAN");

    const boldElement = container.querySelector("b");
    expect(boldElement).toBeInTheDocument();
    expect(boldElement?.textContent).toBe("Please try again in 5 minutes.");
  });

  it("renders the correct error message with custom minutes", () => {
    const mockT = createMockT();

    const { container } = render(getDescriptionGenericError(mockT, 10));

    expect(container.textContent).toBe(
      "An error occurred.Please try again in 10 minutes."
    );

    const boldElement = container.querySelector("b");
    expect(boldElement).toBeInTheDocument();
    expect(boldElement?.textContent).toBe("Please try again in 10 minutes.");
  });
});
