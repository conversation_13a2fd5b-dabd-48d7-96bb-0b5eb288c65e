import { CloseOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { OptionAction, OptionItemWrapper } from "./OptionItem.styles";

interface OptionItemProps {
  value: any;
  label: string;
  disabled?: boolean;
  onRemove: (value: any) => void;
}

function OptionItem({ value, label, disabled, onRemove }: OptionItemProps) {
  const { t } = useTranslation();

  return (
    <OptionItemWrapper>
      {label}
      <Tooltip title={t("REMOVE")}>
        <OptionAction
          data-testid="option-action-item-remove"
          aria-label={t("REMOVE")}
          onClick={() => {
            onRemove(value);
          }}
          disabled={disabled}
        >
          <CloseOutlined />
        </OptionAction>
      </Tooltip>
    </OptionItemWrapper>
  );
}

export default OptionItem;
