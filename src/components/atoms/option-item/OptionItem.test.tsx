import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import OptionItem from "./OptionItem";
import { useTranslation } from "react-i18next";
import { CloseOutlined } from "@ant-design/icons";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

jest.mock("@ant-design/icons", () => ({
  CloseOutlined: () => <div data-testid="close-icon">X</div>,
}));

describe("OptionItem", () => {
  const mockOnRemove = jest.fn();

  const defaultProps = {
    value: "test-value",
    label: "Test Label",
    disabled: false,
    onRemove: mockOnRemove,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the label", () => {
    render(<OptionItem {...defaultProps} />);
    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  it("renders the remove button with tooltip", () => {
    render(<OptionItem {...defaultProps} />);
    const button = screen.getByRole("button", { name: "REMOVE" });
    expect(button).toBeInTheDocument();
  });

  it("calls onRemove with the correct value when remove button is clicked", () => {
    render(<OptionItem {...defaultProps} />);
    const button = screen.getByRole("button", { name: "REMOVE" });
    fireEvent.click(button);
    expect(mockOnRemove).toHaveBeenCalledWith("test-value");
  });

  it("disables the remove button when disabled prop is true", () => {
    render(<OptionItem {...defaultProps} disabled={true} />);
    const button = screen.getByRole("button", { name: "REMOVE" });
    expect(button).toBeDisabled();
  });

  it("renders the CloseOutlined icon in the button", () => {
    render(<OptionItem {...defaultProps} />);
    const closeIcon = screen.getByTestId("close-icon");
    expect(closeIcon).toBeInTheDocument();
  });

  it("renders the component with the correct structure", () => {
    const { container } = render(<OptionItem {...defaultProps} />);
    const wrapper = container.firstChild as HTMLElement;

    expect(wrapper.className).toContain("sc-");

    expect(wrapper).toContainElement(screen.getByText("Test Label"));
    expect(wrapper).toContainElement(screen.getByRole("button", { name: "REMOVE" }));
  });

  it("applies the correct styling to the button", () => {
    render(<OptionItem {...defaultProps} />);
    const button = screen.getByRole("button", { name: "REMOVE" });

    expect(button.className).toContain("sc-");
  });

  it("does not call onRemove when button is disabled and clicked", () => {
    render(<OptionItem {...defaultProps} disabled={true} />);
    const button = screen.getByRole("button", { name: "REMOVE" });

    fireEvent.click(button);
    expect(mockOnRemove).not.toHaveBeenCalled();
  });

  it("renders with a complex object value", () => {
    const complexValue = { id: 123, name: "Complex Object" };
    render(
      <OptionItem
        value={complexValue}
        label="Complex Label"
        onRemove={mockOnRemove}
      />
    );

    expect(screen.getByText("Complex Label")).toBeInTheDocument();

    const button = screen.getByRole("button", { name: "REMOVE" });
    fireEvent.click(button);
    expect(mockOnRemove).toHaveBeenCalledWith(complexValue);
  });

  it("renders with a numeric value", () => {
    render(
      <OptionItem
        value={42}
        label="Numeric Label"
        onRemove={mockOnRemove}
      />
    );

    expect(screen.getByText("Numeric Label")).toBeInTheDocument();

    const button = screen.getByRole("button", { name: "REMOVE" });
    fireEvent.click(button);
    expect(mockOnRemove).toHaveBeenCalledWith(42);
  });
});
