import styled from "styled-components";

export const OptionItemWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  font-weight: bold;
  line-height: 22px;
  padding: 8px;
  background: #fff;
  border-bottom: 1px solid #e2e4e7;
`;

export const OptionAction = styled.button`
  cursor: pointer;
  border: 1px solid #5e6976;
  border-radius: 50px;
  height: 20px;
  width: 20px;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
`;
