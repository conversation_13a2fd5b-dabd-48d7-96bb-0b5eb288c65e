import { ReportRequestForm } from "@components";
import { Drawer } from "antd";
import { useTranslation } from "react-i18next";
import { useRequestFormContext } from "../../../hooks/use-request-form-context";

function FormWrapper() {
  const { t } = useTranslation();
  const { isRequestFormOpen, reportMetadata, closeRequestForm } =
    useRequestFormContext();

  function handleClose() {
    closeRequestForm();
  }

  let form = <ReportRequestForm />;

  if (reportMetadata?.form) {
    form = reportMetadata.form();
  }

  return (
    <Drawer
      title={t(reportMetadata?.title)}
      open={isRequestFormOpen}
      closable={false}
      width={reportMetadata?.formWidth || 960}
      onClose={handleClose}
      destroyOnClose={true}
      data-testid="pb-t-form-wrapper"
    >
      {form}
    </Drawer>
  );
}

export default FormWrapper;
