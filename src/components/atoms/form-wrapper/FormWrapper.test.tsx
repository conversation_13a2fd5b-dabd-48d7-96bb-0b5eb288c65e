import { cleanup, render, screen, fireEvent } from "@testing-library/react";
import React from "react";
import FormWrapper from "./FormWrapper";
import { useRequestFormContext } from "../../../hooks/use-request-form-context";

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

jest.mock("../../molecules/form-list-item/FormListItem");

jest.mock("../../../hooks/use-request-form-context", () => {
  return {
    useRequestFormContext: jest.fn(() => ({
      isRequestFormOpen: true,
      reportMetadata: {},
      openRequestForm: () => {},
      closeRequestForm: jest.fn(),
    })),
  };
});

jest.mock("../../../hooks/use-account-details", () => {
  return {
    useAccountDetails: jest.fn(() => ({
      handleGetDetailsAccount: jest.fn(),
      hasResource: jest.fn(() => false),
    })),
  };
});

describe("FormWrapper component", () => {
  it("should render the component when isRequestFormOpen is true", async () => {
    (useRequestFormContext as jest.Mock).mockReturnValue({
      isRequestFormOpen: true,
      reportMetadata: {},
      openRequestForm: () => {},
      closeRequestForm: jest.fn(),
    });

    render(<FormWrapper />);
    const element = screen.queryByTestId("pb-t-form-wrapper");
    expect(element).toBeInTheDocument();
  });

  it("should not render the component when isRequestFormOpen is false", async () => {
    (useRequestFormContext as jest.Mock).mockReturnValue({
      isRequestFormOpen: false,
      reportMetadata: {},
      openRequestForm: () => {},
      closeRequestForm: jest.fn(),
    });

    render(<FormWrapper />);
    const element = screen.queryByTestId("pb-t-form-wrapper");
    expect(element).not.toBeInTheDocument();
  });

  it("should render the custom form if provided", () => {
    (useRequestFormContext as jest.Mock).mockReturnValue({
      isRequestFormOpen: true,
      reportMetadata: {
        form: () => <div data-testid="custom-form"></div>,
      },
      openRequestForm: () => {},
      closeRequestForm: jest.fn(),
    });

    render(<FormWrapper />);
    const customForm = screen.queryByTestId("custom-form");
    expect(customForm).toBeInTheDocument();
  });

  it("should display the correct title", () => {
    const title = "Test Title";
    (useRequestFormContext as jest.Mock).mockReturnValue({
      isRequestFormOpen: true,
      reportMetadata: {
        title,
      },
      openRequestForm: () => {},
      closeRequestForm: jest.fn(),
    });

    render(<FormWrapper />);
    const drawerTitle = screen.getByText(title);
    expect(drawerTitle).toBeInTheDocument();
  });

  it("should use custom formWidth when provided", () => {
    const customWidth = 1200;
    (useRequestFormContext as jest.Mock).mockReturnValue({
      isRequestFormOpen: true,
      reportMetadata: {
        title: "Test Title",
        formWidth: customWidth,
      },
      openRequestForm: () => {},
      closeRequestForm: jest.fn(),
    });

    render(<FormWrapper />);
    const element = screen.queryByTestId("pb-t-form-wrapper");
    expect(element).toBeInTheDocument();
  });

  it("should call closeRequestForm when the drawer is closed", () => {
    const closeRequestFormMock = jest.fn();
    (useRequestFormContext as jest.Mock).mockReturnValue({
      isRequestFormOpen: true,
      reportMetadata: {},
      openRequestForm: () => {},
      closeRequestForm: closeRequestFormMock,
    });

    render(<FormWrapper />);

    const drawer = screen.getByTestId("pb-t-form-wrapper");
    expect(drawer).toBeInTheDocument();

    expect(closeRequestFormMock).not.toHaveBeenCalled();

  });
});
