import React from "react";
import { render } from "@testing-library/react";
import { SectionBatchFileImported } from "./SectionBatchFileImported";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe("SectionBatchFileImported", () => {
  const documentsList = ["doc1", "doc2"];
  const invalidDocumentsList = ["invalidDoc1"];

  it("renders the summary card with valid documents", () => {
    const { getByText } = render(
      <SectionBatchFileImported
        documentsList={documentsList}
        invalidDocumentsList={[]}
      />
    );

    expect(getByText("SUMMARY")).toBeInTheDocument();
    expect(getByText("VALID_DOCUMENTS")).toBeInTheDocument();
    expect(getByText(`${documentsList.length} DOCUMENTS`)).toBeInTheDocument();
  });

  it("renders the summary card with invalid documents", () => {
    const { getByText } = render(
      <SectionBatchFileImported
        documentsList={documentsList}
        invalidDocumentsList={invalidDocumentsList}
      />
    );

    expect(getByText("SUMMARY")).toBeInTheDocument();
    expect(getByText("VALID_DOCUMENTS")).toBeInTheDocument();
    expect(getByText(`${documentsList.length} DOCUMENTS`)).toBeInTheDocument();
    expect(getByText("INVALID_DOCUMENTS")).toBeInTheDocument();
    expect(
      getByText(`${invalidDocumentsList.length} DOCUMENTS`)
    ).toBeInTheDocument();
  });

  it("does not render invalid documents section when there are no invalid documents", () => {
    const { queryByText } = render(
      <SectionBatchFileImported
        documentsList={documentsList}
        invalidDocumentsList={[]}
      />
    );

    expect(queryByText("INVALID_DOCUMENTS")).not.toBeInTheDocument();
  });
});
