import { useTranslation } from "react-i18next";
import { Overline, SummaryCard } from "./SectionBatchFileImported.styles";
import { FC } from "react";

export type SectionBatchFileImportedProps = {
  documentsList: string[];
  invalidDocumentsList: string[];
};

export const SectionBatchFileImported: FC<SectionBatchFileImportedProps> = ({
  documentsList,
  invalidDocumentsList,
}) => {
  const { t } = useTranslation();
  return (
    <section>
      <SummaryCard>
        <Overline>{t("SUMMARY")}</Overline>
        <p>{t("VALID_DOCUMENTS")}</p>
        <ul>
          <li>
            {documentsList.length} {t("DOCUMENTS")}
          </li>
        </ul>

        {invalidDocumentsList.length > 0 && (
          <>
            <p>{t("INVALID_DOCUMENTS")}</p>
            <ul>
              <li>
                {invalidDocumentsList.length} {t("DOCUMENTS")}
              </li>
            </ul>
          </>
        )}
      </SummaryCard>
    </section>
  );
};
