import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import {
  TableInvalidDocuments,
  TableInvalidDocumentsProps,
} from "./TableInvalidDocuments";

const defaultProps: TableInvalidDocumentsProps = {
  isExpanded: false,
  invalidDocumentsList: ["Document 1", "Document 2"],
  onClickToggleExpand: jest.fn(),
  title: "Invalid Documents",
  containerClassname: "test-classname",
};

describe("TableInvalidDocuments", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the table with the correct title", () => {
    render(<TableInvalidDocuments {...defaultProps} />);
    expect(screen.getByText("Invalid Documents")).toBeInTheDocument();
  });

  it("renders the UpOutlined icon when not expanded", () => {
    render(<TableInvalidDocuments {...defaultProps} />);
    expect(
      screen.getByTestId("icon-arrowup-table-invalid-documents")
    ).toBeInTheDocument();
  });

  it("should use default styling when no className is provided", () => {
    const { containerClassname, ...rest } = defaultProps;
    render(<TableInvalidDocuments {...rest} />);
    const table = screen.getByRole("table");
    expect(table).not.toHaveClass("test-classname");
    expect(table.className).toBe("");
  });

  it("renders the DownOutlined icon when expanded", () => {
    render(<TableInvalidDocuments {...defaultProps} isExpanded={true} />);
    expect(
      screen.getByTestId("icon-arrowdown-table-invalid-documents")
    ).toBeInTheDocument();
  });

  it("calls onClickToggleExpand when the button is clicked", () => {
    render(<TableInvalidDocuments {...defaultProps} />);
    fireEvent.click(
      screen.getByTestId("button-click-table-toggle-invalid-documents")
    );
    expect(defaultProps.onClickToggleExpand).toHaveBeenCalled();
  });

  it("renders the invalid documents list when expanded", () => {
    render(<TableInvalidDocuments {...defaultProps} isExpanded={true} />);
    expect(screen.getByText("Document 1")).toBeInTheDocument();
    expect(screen.getByText("Document 2")).toBeInTheDocument();
  });

  it("does not render the invalid documents list when not expanded", () => {
    render(<TableInvalidDocuments {...defaultProps} />);
    expect(screen.queryByText("Document 1")).not.toBeInTheDocument();
    expect(screen.queryByText("Document 2")).not.toBeInTheDocument();
  });

  it("applies the correct container classname", () => {
    render(<TableInvalidDocuments {...defaultProps} />);
    expect(screen.getByRole("table")).toHaveClass("test-classname");
  });

  it("renders correctly with an empty list of invalid documents", () => {
    render(
      <TableInvalidDocuments
        {...defaultProps}
        invalidDocumentsList={[]}
        isExpanded={true}
      />
    );
    expect(screen.getByRole("table")).toBeInTheDocument();
    const tableBody = screen.getByRole("table").querySelector("tbody");
    expect(tableBody.children.length).toBe(0);
  });

  it("renders correctly with a large list of invalid documents", () => {
    const largeList = Array.from({ length: 10 }, (_, i) => `Document ${i + 1}`);
    render(
      <TableInvalidDocuments
        {...defaultProps}
        invalidDocumentsList={largeList}
        isExpanded={true}
      />
    );
    largeList.forEach(doc => {
      expect(screen.getByText(doc)).toBeInTheDocument();
    });
  });

  it("renders alternating row backgrounds for even and odd rows", () => {
    render(<TableInvalidDocuments {...defaultProps} isExpanded={true} />);

    const rows = screen.getByRole("table").querySelectorAll("tbody tr");

    const firstRowCell = rows[0].querySelector("td");
    expect(firstRowCell).toHaveStyle("background: #fff");

    const secondRowCell = rows[1].querySelector("td");
    expect(secondRowCell).toHaveStyle("background: #F2F2F2");
  });

  it("renders the correct styling for table cells", () => {
    render(<TableInvalidDocuments {...defaultProps} isExpanded={true} />);

    const cell = screen.getByText("Document 1");
    expect(cell).toHaveStyle("fontSize: 14px");
    expect(cell).toHaveStyle("fontWeight: 500");
    expect(cell).toHaveStyle("height: 40px");
  });

  it("renders the header with correct styling", () => {
    render(<TableInvalidDocuments {...defaultProps} />);

    const headerCell = screen.getByRole("table").querySelector("th");
    expect(headerCell).toHaveStyle("textAlign: left");

    const headerContent = headerCell.querySelector("div");
    expect(headerContent).toHaveStyle("display: flex");
    expect(headerContent).toHaveStyle("gap: 5px");
    expect(headerContent).toHaveStyle("alignItems: center");
    expect(headerContent).toHaveStyle("color: #717171");
  });

  it("renders the button with correct styling", () => {
    render(<TableInvalidDocuments {...defaultProps} />);

    const button = screen.getByTestId("button-click-table-toggle-invalid-documents");
    expect(button.className).not.toBe("");
    expect(button.className.startsWith("sc-")).toBe(true);
  });
});
