import { DownOutlined, UpOutlined } from "@ant-design/icons";
import { InlineTableButton } from "./TableInvalidDocuments.styles";
import { FC } from "react";

export type TableInvalidDocumentsProps = {
  isExpanded: boolean;
  invalidDocumentsList: string[];
  onClickToggleExpand: () => void;
  title: string;
  containerClassname?: string;
};

export const TableInvalidDocuments: FC<TableInvalidDocumentsProps> = ({
  isExpanded,
  onClickToggleExpand,
  invalidDocumentsList,
  title,
  containerClassname,
}) => {
  return (
    <table className={containerClassname ?? ""}>
      <thead>
        <tr>
          <th style={{ textAlign: "left" }}>
            <div
              style={{
                display: "flex",
                gap: "5px",
                alignItems: "center",
                color: "#717171",
              }}
            >
              <InlineTableButton
                data-testid="button-click-table-toggle-invalid-documents"
                onClick={onClickToggleExpand}
              >
                {isExpanded ? (
                  <DownOutlined data-testid="icon-arrowdown-table-invalid-documents" />
                ) : (
                  <UpOutlined data-testid="icon-arrowup-table-invalid-documents" />
                )}
              </InlineTableButton>
              {title}
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        {(isExpanded ? invalidDocumentsList : []).map((document, index) => {
          return (
            <tr key={index}>
              <td
                style={{
                  fontSize: "14px",
                  fontWeight: "500",
                  height: "40px",
                  background: index % 2 == 0 ? "#fff" : "#F2F2F2",
                }}
              >
                {document}
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
};
