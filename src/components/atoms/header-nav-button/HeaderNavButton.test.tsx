import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import HeaderNavButton from "./HeaderNavButton";

afterEach(() => {
  cleanup();
});

describe("HeaderNavButton component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(
      <HeaderNavButton
        label=""
        tabKey={1}
        selected={false}
        onClick={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn");
    expect(component).toBeInTheDocument();
  });

  it("should render the prop label", () => {
    render(
      <HeaderNavButton
        label="Test"
        tabKey={1}
        selected={false}
        onClick={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn");
    expect(component?.textContent).toBe("Test");
  });

  it("should call the click callback when clicked", () => {
    const mockClick = jest.fn();
    render(
      <HeaderNavButton
        label=""
        tabKey={1}
        selected={false}
        onClick={mockClick}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn")!;
    fireEvent.click(component.querySelector("div")!);
    expect(mockClick).toHaveBeenCalled();
    expect(mockClick).toHaveBeenCalledWith(1);
  });

  it("should render the inkbar when selected is true", () => {
    render(
      <HeaderNavButton
        label="Test"
        tabKey={1}
        selected={true}
        onClick={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn");
    const inkbar = component?.querySelector("div[class^='sc-']"); // Styled component
    expect(inkbar).toBeInTheDocument();
  });

  it("should not render the inkbar when selected is false", () => {
    render(
      <HeaderNavButton
        label="Test"
        tabKey={1}
        selected={false}
        onClick={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn");
    const inkbar = component?.querySelector("div[class^='sc-']"); // Styled component
    expect(inkbar).not.toBeInTheDocument();
  });

  it("should set aria-selected attribute based on selected prop", () => {
    render(
      <HeaderNavButton
        label="Test"
        tabKey={1}
        selected={true}
        onClick={() => {}}
      />
    );
    const tabButton = screen.queryByTestId("pb-t-header-nav-btn")?.querySelector("div[role='tab']");
    expect(tabButton).toHaveAttribute("aria-selected", "true");
  });

  it("should set tabIndex attribute based on tabKey prop", () => {
    const tabKey = 5;
    render(
      <HeaderNavButton
        label="Test"
        tabKey={tabKey}
        selected={false}
        onClick={() => {}}
      />
    );
    const tabButton = screen.queryByTestId("pb-t-header-nav-btn")?.querySelector("div[role='tab']");
    expect(tabButton).toHaveAttribute("tabIndex", tabKey.toString());
  });

  it("should handle undefined onClick prop", () => {
    render(
      <HeaderNavButton
        label="Test"
        tabKey={1}
        selected={false}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn")!;
    expect(() => {
      fireEvent.click(component.querySelector("div")!);
    }).not.toThrow();
  });

  it("should have the correct class names", () => {
    render(
      <HeaderNavButton
        label="Test"
        tabKey={1}
        selected={false}
        onClick={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-header-nav-btn");
    expect(component).toHaveClass("ant-tabs-tab");

    const tabButton = component?.querySelector("div[role='tab']");
    expect(tabButton).toHaveClass("ant-tabs-tab-btn");
  });
});
