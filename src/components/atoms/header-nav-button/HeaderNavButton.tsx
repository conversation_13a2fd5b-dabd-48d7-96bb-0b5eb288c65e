import { Inkbar } from "./HeaderNavButton.styles";

export interface HeaderNavButtonData {
  label: string;
  tabKey: number;
  selected: boolean;
}

type HeaderNavButtonProps = {
  onClick?: (tabKey: number) => void;
} & HeaderNavButtonData;

function HeaderNavButton({
  label,
  tabKey,
  selected,
  onClick,
}: HeaderNavButtonProps) {
  return (
    <div className="ant-tabs-tab" data-testid="pb-t-header-nav-btn">
      <div
        role="tab"
        aria-selected={selected}
        className="ant-tabs-tab-btn"
        tabIndex={tabKey}
        onClick={() => onClick && onClick(tabKey)}
      >
        {label}
      </div>
      {selected && <Inkbar />}
    </div>
  );
}

export default HeaderNavButton;
