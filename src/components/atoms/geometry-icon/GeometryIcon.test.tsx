import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import GeometryIcon from "./GeometryIcon";
import { geojsonToFeatures, getExtent } from "@utils";

jest.mock("@utils", () => ({
  geojsonToFeatures: jest.fn(),
  getExtent: jest.fn(() => [0, 0, 1, 1])
}));

const MOCK_POLYGON_GEOMETRY = {
  coordinates: [
    [
      [-45.47873654856133, -12.997222865500703],
      [-45.47873654856133, -12.998973850770838],
      [-45.47648145785058, -12.998973850770838],
      [-45.47648145785058, -12.997222865500703],
      [-45.47873654856133, -12.997222865500703],
    ],
  ],
  type: "Polygon",
};

const MOC<PERSON>_MULTI_POLYGON_GEOMETRY = {
  coordinates: [
    [
      [
        [-45.47873654856133, -12.997222865500703],
        [-45.47873654856133, -12.998973850770838],
        [-45.47648145785058, -12.998973850770838],
        [-45.47648145785058, -12.997222865500703],
        [-45.47873654856133, -12.997222865500703],
      ],
    ],
    [
      [
        [-45.57873654856133, -12.897222865500703],
        [-45.57873654856133, -12.898973850770838],
        [-45.57648145785058, -12.898973850770838],
        [-45.57648145785058, -12.897222865500703],
        [-45.57873654856133, -12.897222865500703],
      ],
    ],
  ],
  type: "MultiPolygon",
};

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe("GeometryIcon component", () => {
  beforeEach(() => {
    const mockFeatures = [{ getGeometry: () => ({ getExtent: () => [0, 0, 1, 1] }) }];
    (geojsonToFeatures as jest.Mock).mockReturnValue(mockFeatures);
  });

  it("should render the component with Polygon geometry", () => {
    render(
      <GeometryIcon
        geometry={MOCK_POLYGON_GEOMETRY}
        onClick={() => {}}
        fill="red"
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-icon");
    expect(component).toBeInTheDocument();

    expect(component).toHaveAttribute("viewBox", "0 0 1 1");

    const path = component.querySelector("path");
    expect(path).toBeInTheDocument();
    expect(path).toHaveAttribute("fill", "red");
  });

  it("should render the component with MultiPolygon geometry", () => {
    render(
      <GeometryIcon
        geometry={MOCK_MULTI_POLYGON_GEOMETRY}
        onClick={() => {}}
        fill="blue"
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-icon");
    expect(component).toBeInTheDocument();

    const paths = component.querySelectorAll("path");
    expect(paths.length).toBe(2);

    paths.forEach(path => {
      expect(path).toHaveAttribute("fill", "blue");
    });
  });

  it("should use default fill color when not provided", () => {
    render(
      <GeometryIcon
        geometry={MOCK_POLYGON_GEOMETRY}
        onClick={() => {}}
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-icon");
    const path = component.querySelector("path");
    expect(path).toHaveAttribute("fill", "#eaeaea"); // Default fill color
  });

  it("should call the click callback when clicked", () => {
    const mockClick = jest.fn();
    render(
      <GeometryIcon
        geometry={MOCK_POLYGON_GEOMETRY}
        onClick={mockClick}
        fill="red"
      />
    );
    const component = screen.queryByTestId("pb-t-geometry-icon");
    fireEvent.click(component);
    expect(mockClick).toHaveBeenCalledTimes(1);
    expect(mockClick).toHaveBeenCalledWith(MOCK_POLYGON_GEOMETRY);
  });

  it("should not call onClick when not provided", () => {
    render(
      <GeometryIcon
        geometry={MOCK_POLYGON_GEOMETRY} onClick={undefined} />
    );
    const component = screen.queryByTestId("pb-t-geometry-icon");
    fireEvent.click(component);
    expect(component).toBeInTheDocument();
  });
});
