import { GeoJSONGeometry } from "ol/format/GeoJSON";
import { Svg } from "./GeometryIcon.styles";
import { geojsonToFeatures, getExtent } from "@utils";

function GeometryIcon({ geometry, fill = "#eaeaea", onClick }) {
  function getViewBox(geometry: GeoJSONGeometry) {
    const features = geojsonToFeatures(geometry);
    const extent = getExtent(features);
    // minX minY (maxX - minX) (maxY - minY)
    return `${extent[0]} ${extent[1]} ${extent[2] - extent[0]} ${
      extent[3] - extent[1]
    }`;
  }

  function getSvgPath(coordinates: number[][][]) {
    const svgPath =
      coordinates
        .flat()
        .map(([lon, lat], index) => `${index === 0 ? "M" : "L"}${lon} ${lat}`)
        .join("") + "Z";

    return svgPath;
  }

  const paths =
    geometry.type == "MultiPolygon" ? (
      geometry.coordinates.map((coordinates, index) => (
        <path
          key={index}
          d={getSvgPath(coordinates)}
          stroke="none"
          fill={fill}
        />
      ))
    ) : (
      <path d={getSvgPath(geometry.coordinates)} stroke="none" fill={fill} />
    );

  return (
    <Svg
      viewBox={getViewBox(geometry)}
      xmlns="http://www.w3.org/2000/svg"
      version="1.2"
      onClick={!!onClick ? () => onClick(geometry) : null}
      data-testid="pb-t-geometry-icon"
    >
      <g>{paths}</g>
    </Svg>
  );
}

export default GeometryIcon;
