import { SearchOutlined } from "@ant-design/icons";
import { formatDocument } from "@utils";
import { TooltipPlacement } from "antd/es/tooltip";
import { Select } from "brainui";
import { IconContainer, SelectContainer } from "./CustomSelect.styles";

const { Option } = Select;

export type CustomSelectOptions = {
  value: string;
  label: string;
  className: string;
  disabled: boolean;
  document: string;
  document_type: string;
};

function CustomSelect({
  onChange = (value) => {},
  onClear = () => {},
  value,
  options = [],
  label = "",
  tooltip = "",
  disabled,
  optionTooltipTitle = null,
  optionTooltipPlacement = "left" as TooltipPlacement,
  grow = false,
  ...rest
}) {
  const renderOptions = () => {
    return options.map((option) => {
      const {
        value,
        label,
        className,
        disabled = false,
        document,
        document_type,
      } = option;

      const text = document
        ? `${label} - ${formatDocument(document, document_type)}`
        : label;

      return (
        <Option
          key={document}
          value={JSON.stringify(value)}
          disabled={disabled}
          className={className}
        >
          {text}
        </Option>
      );
    });
  };

  return (
    <SelectContainer>
      <IconContainer>
        <SearchOutlined />
      </IconContainer>
      <Select
        data-testid="select-custom"
        label={label}
        style={{ minWidth: 120 }}
        onChange={onChange}
        value={value}
        showArrow={true}
        disabled={disabled}
        onClear={onClear}
        {...rest}
      >
        {renderOptions()}
      </Select>
    </SelectContainer>
  );
}

export default CustomSelect;
