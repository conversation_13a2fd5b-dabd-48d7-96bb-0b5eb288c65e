import { render, screen } from "@testing-library/react";

// Mock the utils module to avoid circular dependencies
jest.mock("@utils", () => ({
  formatDocument: jest.fn((doc, type) => `${doc}-${type}`),
}));

// Import after mocking dependencies
import CustomSelect, { CustomSelectOptions } from "./CustomSelect";

describe("CustomSelect", () => {
  it("should render custom select", () => {
    const options: CustomSelectOptions[] = [
      {
        document: "15874288000109",
        document_type: "ru",
        className: "",
        disabled: true,
        label: "doc",
        value: "DOC",
      },
    ];
    render(
      <CustomSelect
        disabled={false}
        value={"123456"}
        options={options as any}
      />
    );

    expect(screen.getAllByText("123456")).toHaveLength(1);
  });

  it("should render custom select with no options", () => {
    render(<CustomSelect disabled value={"123456"} />);

    expect(screen.getAllByText("123456")).toHaveLength(1);
  });

  it("should handle with fields required", () => {
    const options = [
      {
        document: "15874288000109",
        document_type: "ru",
        className: "",
        label: "doc",
        value: "DOC",
      },
    ] as CustomSelectOptions[];
    render(
      <CustomSelect
        disabled={false}
        value={"123456"}
        options={options as any}
      />
    );

    expect(screen.getAllByText("123456")).toHaveLength(1);
  });
});
