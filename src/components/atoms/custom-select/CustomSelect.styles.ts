import styled from "styled-components";

export const IconContainer = styled.div`
  position: absolute;
  top: 28%;
  left: 11px;
  z-index: 10;
  color: #b6b6b6;
`;

export const SelectContainer = styled.div`
  position: relative;
  overflow: hidden;

  .ant-select-selection-search,
  .ant-select-selector .ant-select-selection-item,
  label {
    padding-left: 18px !important;
  }

  .ant-select-selection-item {
    overflow: hidden;
    display: block;
  }

  .ant-select-selector .ant-select-selection-item {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
  }

  .ant-select-single:not(.ant-select-customize-input)
    .ant-select-selector
    .ant-select-selection-search-input {
    height: 37px;
  }
`;
