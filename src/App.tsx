import { FormWrapper } from "@components";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import { TabsWrapper } from "./App.styles";
import {
  HeaderNavTabs,
  Home,
  ReportRequestsTable,
} from "./components/organisms";
import { AvailableReport, REPORT_CARD_METADATA } from "./constants/reports";
import { useAnalytics } from "./contexts/AnalyticsDataLayerContext";
import { RequestFormContext } from "./contexts/RequestFormContext";
import { ReportType } from "./types/report-type";
import { useCardsList } from "./hooks/use-cards-list";

const PAGES = {
  home: {
    key: 1,
    name: "AG:LG:AgroReport:Home",
  },
  reports: {
    key: 2,
    name: "AG:LG:AgroReport:MinhasSolicitacoes",
  },
};

function App() {
  const { t } = useTranslation();
  const { sendPageViewEvent } = useAnalytics();
  const [isRequestFormOpen, setIsRequestFormOpen] = useState(false);
  const [reportMetadata, setReportMetadata] = useState<AvailableReport | null>(
    null
  );
  const [selectedTab, setSelectedTab] = useState(PAGES.home.key);

  const [searchParams, setSearchParams] = useSearchParams();
  const page = searchParams.get("page");
  const { cardsList } = useCardsList();

  useEffect(() => {
    setSelectedTab(PAGES[page]?.key || PAGES.home.key);
    sendPageViewEvent({
      pageName: PAGES[page]?.name || PAGES.home.name,
      ambiente: "LG",
      siteSection: "Agro Report",
      url: location.href,
    });
  }, [page]);

  const navbar = (
    <HeaderNavTabs
      tabs={[
        {
          tabKey: 1,
          label: t("DISCOVER_REPORTS"),
          selected: selectedTab == 1,
        },
        { tabKey: 2, label: t("MY_REQUESTS"), selected: selectedTab == 2 },
      ]}
      onChange={(tabKey: number) => {
        setSearchParams((params) => {
          params.set(
            "page",
            Object.keys(PAGES).find((x) => PAGES[x].key == tabKey)
          );
          return params;
        });
      }}
    />
  );

  async function openRequestForm(initialReportType: ReportType) {
    let _reportMetadata =
      cardsList.find((r) => r.type == initialReportType) ||
      ({} as AvailableReport);

    _reportMetadata = {
      ..._reportMetadata,
      ...REPORT_CARD_METADATA[initialReportType],
    };
    setReportMetadata(_reportMetadata);
    setIsRequestFormOpen(true);
    sendPageViewEvent({
      pageName: "AG:LG:AgroReport:SolicitarNovoRelatorio",
      ambiente: "LG",
      siteSection: "Agro Report",
      url: location.href,
    });
  }

  function closeRequestForm() {
    setIsRequestFormOpen(false);
  }

  return (
    <div style={{ overflow: "hidden" }}>
      <RequestFormContext.Provider
        value={{
          isRequestFormOpen,
          reportMetadata,
          openRequestForm,
          closeRequestForm,
        }}
      >
        <FormWrapper />
        {document.querySelector("#main-nav")
          ? createPortal(
              <TabsWrapper>{navbar}</TabsWrapper>,
              document.querySelector("#main-nav")
            )
          : navbar}

        {selectedTab == 1 && <Home />}
        {selectedTab == 2 && <ReportRequestsTable />}
      </RequestFormContext.Provider>
    </div>
  );
}

export default App;
