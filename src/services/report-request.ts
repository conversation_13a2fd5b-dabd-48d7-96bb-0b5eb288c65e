import { api } from ".";
import { ReportRequest, ReportRequestExport } from "../types/report-request";
import { ReportStatus } from "../types/report-status";
import { ReportType } from "../types/report-type";

export type ListReportRequestResult = {
  items: ReportRequest[];
  reports_count: number;
  count: number;
};

export async function createReportRequest(params) {
  const { data } = await api.post(`/reports-requests`, params);
  return data;
}

export async function createReportExternalAPI(file: File, reportType: string) {
  const formData = new FormData();
  formData.append("file", file);
  const { data } = await api.post(
    `/reports-requests/${reportType.toLowerCase()}`,
    formData,
    {
      headers: { "Content-Type": "multipart/form-data" },
    }
  );
  return data;
}

export async function createBatchReportRequest(
  file,
  report_type: ReportType,
  signature: boolean
) {
  const formData = new FormData();
  formData.append("file", file);
  if (signature == undefined) {
    signature = false;
  }
  const { data } = await api.post(`/reports-requests/batch_upload`, formData, {
    headers: { "Content-Type": "multipart/form-data" },
    params: { report_type, signature },
  });
  return data;
}

export async function createFileBatchReportRequest(
  formData: FormData,
  report_type: ReportType,
  signature: boolean
): Promise<any> {
  const { data } = await api.post(`/reports-requests/batch_upload`, formData, {
    headers: { "Content-Type": "multipart/form-data" },
    params: { report_type, signature },
  });
  return data;
}

export async function createFileReportRequest(payload: {
  formData: FormData;
  report_type?: ReportType;
  signature?: boolean;
  url?: string;
}): Promise<any> {
  const { formData, report_type, signature, url } = payload;
  let params: Record<string, string | boolean> = { report_type };
  if (signature !== undefined) {
    params = { ...params, signature };
  }

  if (report_type !== undefined) {
    params = { ...params, report_type };
  }
  const { data } = await api.post(url ?? `/reports-requests`, formData, {
    headers: { "Content-Type": "multipart/form-data" },
    params: { ...params },
  });
  return data;
}

export async function listReportRequests(
  params
): Promise<ListReportRequestResult> {
  const { data } = await api.get(`/reports-requests`, {
    params,
    paramsSerializer: {
      indexes: null,
    },
  });
  return data;
}

export async function cloneReportRequest(
  requestId: string | number
): Promise<ReportRequest> {
  const { data } = await api.post(`/reports-requests/${requestId}/clone`);
  return data;
}

export function getReportRequestStatus(request: ReportRequest): ReportStatus {
  let status = ReportStatus.DONE;
  if (request.reports_error == request.reports_count) {
    status = ReportStatus.ERROR;
  } else if (
    request.reports_error > 0 &&
    request.reports_error != request.reports_count
  ) {
    status = ReportStatus.WARNING;
  } else if (request.reports_pending > 0 && request.reports_done == 0) {
    status = ReportStatus.PENDING;
  } else if (request.reports_done == 0) {
    status = ReportStatus.PROCESSING;
  }
  return status;
}

export async function hasCredits(
  report_type: ReportType,
  report_count: number
): Promise<{ has_credit: boolean }> {
  const { data } = await api.get<{ has_credit: boolean }>(
    `/reports-requests/billing`,
    {
      params: { report_type, reports: report_count },
    }
  );
  return data;
}

export async function downloadRequestProperties(
  reportRequestId: string | number
) {
  const { data } = await api.get(
    `reports-requests/${reportRequestId}/properties`,
    { responseType: "blob" }
  );
  return data;
}

export async function listRequestExports(
  requestId: string | number
): Promise<ReportRequestExport[]> {
  const { data } = await api.get<ReportRequestExport[]>(
    `/reports-requests/${requestId}/exports`
  );
  return data;
}

export async function downloadRequest(requestId: number, exportId: number) {
  const { data } = await api.get(
    `/report-requests/${requestId}/exports/${exportId}/data`,
    {
      responseType: "blob",
    }
  );
  return data;
}
