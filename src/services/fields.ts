import { farmC<PERSON>ck<PERSON>pi } from ".";
import { AutomaticFieldResponse } from "../types/fields";
import { getArea } from "../utils/geo";

export async function getPropertyFields(
  contactId: string | number,
  propertyIds: (string | number)[]
) {
  const { data } = await farmCheckApi.get(
    `/contacts/${contactId}/fields?properties=[${propertyIds}]`
  );
  return data;
}

export async function getAutomaticFields(
  propertyId: number | string
): Promise<AutomaticFieldResponse[]> {
  const {
    data: { subareas },
  } = await farmCheckApi.get(
    `/remote_sensings/automatic_fields?properties=[${propertyId}]`
  );
  return subareas;
}

export async function createFields(propertyId: number | string, fields: any[]) {
  const { data } = await farmCheckApi.post(`/remote_sensings/fields`, {
    fields: fields.map((field) => ({
      color: "#ffe814",
      origin: "MANUAL",
      matricula: "",
      cultures: [],
      crop_date: null,
      property_id: propertyId,
      name: field.name,
      coordinates: {
        geometry: field.geometry,
      },
      area: getArea(field.geometry) * 0.0001,
    })),
  });
  return data;
}
