import axios from "axios";

export const farmCheckApi = axios.create({
  baseURL: process.env.FARMCHECK_API_URL,
});

farmCheckApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if ([401, 403].includes(error?.response?.status)) {
      let url = process.env.MONOLITH_APP_URL;

      if (error?.response?.status === 401) url += `/login?logout=success`;

      if (error?.response?.status === 403) url += `/403`;

      location.replace(url);
    }

    return Promise.reject(error);
  }
);

farmCheckApi.interceptors.request.use(
  (config: any) => {
    const token = localStorage.getItem("auth-token");
    const refreshToken = localStorage.getItem("refresh-token");

    config.headers = {
      ...config.headers,
      Authorization: `bearer ${token}`,
      "refresh-token": refreshToken,
    };

    return config;
  },
  (error) => Promise.reject(error)
);
