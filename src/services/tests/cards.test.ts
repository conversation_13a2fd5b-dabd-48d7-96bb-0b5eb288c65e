import { listCards, getFileModel } from '../cards';
import { api } from '..';
import { ReportType, TagCard } from '../../types';

jest.mock('..', () => ({
  api: {
    get: jest.fn()
  }
}));

document.createElement = jest.fn().mockImplementation(() => ({
  href: '',
  download: '',
  dispatchEvent: jest.fn(),
  remove: jest.fn()
}));

URL.createObjectURL = jest.fn().mockReturnValue('blob:url');

describe('Cards Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('listCards', () => {
    it('should fetch cards and transform the data', async () => {
      const mockCards = [
        {
          report_type: ReportType.INSPECTION,
          title: 'Inspection Card',
          description: 'Inspection description',
          files_format: [{ file_format: 'PDF' }],
          models: [],
          tag: TagCard.NEW,
          has_metadata: true,
          has_signature: false,
          signature_resource: null,
          no_signature_resource: null,
          has_permission: true,
          value: 'inspection'
        },
        {
          report_type: ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE,
          title: 'CECAFE Card',
          description: 'CECAFE description',
          files_format: [{ file_format: 'PDF' }],
          models: [],
          tag: TagCard.SOON,
          has_metadata: true,
          has_signature: false,
          signature_resource: null,
          no_signature_resource: null,
          has_permission: true,
          value: 'cecafe'
        }
      ];

      (api.get as jest.Mock).mockResolvedValue({
        data: mockCards
      });

      const result = await listCards();

      expect(api.get).toHaveBeenCalledWith('/cards');
      
      expect(result.length).toBe(2);
      
      expect(result[0].type).toBe(ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE);
      expect(result[1].type).toBe(ReportType.INSPECTION);
    });
  });

  describe('getFileModel', () => {
    it('should fetch and download a file model', async () => {
      const url = '/model/url';
      const format = 'PDF';
      const mockData = new ArrayBuffer(8);

      (api.get as jest.Mock).mockResolvedValue({
        data: mockData
      });

      jest.useFakeTimers();

      await getFileModel(url, format);

      expect(api.get).toHaveBeenCalledWith(url, { responseType: 'arraybuffer' });
      
      expect(document.createElement).toHaveBeenCalledWith('a');
      
      expect(URL.createObjectURL).toHaveBeenCalled();
      
      jest.runAllTimers();
    });
  });
});
