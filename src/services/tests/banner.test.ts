import { listBanners, getBannerImage } from '../banner';
import { api } from '..';

jest.mock('..', () => ({
  api: {
    get: jest.fn()
  }
}));

describe('Banner Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('listBanners', () => {
    it('should fetch banners and their images', async () => {
      const mockBanners = {
        items: [
          { id: 1, title: 'Banner 1', image: 'image1.jpg' },
          { id: 2, title: 'Banner 2', image: 'image2.jpg' },
          { id: 3, title: 'Banner 3', image: null }
        ],
        total: 3,
        page: 1,
        size: 10
      };

      const mockImage1 = 'data:image/jpeg;base64,image1base64';
      const mockImage2 = 'data:image/jpeg;base64,image2base64';

      (api.get as jest.Mock).mockResolvedValueOnce({
        data: mockBanners
      });

      (api.get as jest.Mock).mockResolvedValueOnce({
        data: new ArrayBuffer(8),
        headers: { 'Content-Type': 'image/jpeg' }
      });

      (api.get as jest.Mock).mockResolvedValueOnce({
        data: new ArrayBuffer(8),
        headers: { 'Content-Type': 'image/jpeg' }
      });

      global.btoa = jest.fn().mockImplementation(() => {
        return 'mockBase64String';
      });

      const result = await listBanners();

      expect(api.get).toHaveBeenCalledWith('/banners');
      
      expect(api.get).toHaveBeenCalledWith('/banners/1/image', { responseType: 'arraybuffer' });
      expect(api.get).toHaveBeenCalledWith('/banners/2/image', { responseType: 'arraybuffer' });
      
      expect(result).toEqual({
        ...mockBanners,
        items: expect.any(Array)
      });
      
      expect(result.items.length).toBe(3);
    });
  });

  describe('getBannerImage', () => {
    it('should fetch and convert a banner image to base64', async () => {
      const bannerId = 1;
      const mockArrayBuffer = new ArrayBuffer(8);
      const mockContentType = 'image/jpeg';

      (api.get as jest.Mock).mockResolvedValue({
        data: mockArrayBuffer,
        headers: { 'Content-Type': mockContentType }
      });

      global.btoa = jest.fn().mockImplementation(() => {
        return 'mockBase64String';
      });

      const result = await getBannerImage(bannerId);

      expect(api.get).toHaveBeenCalledWith('/banners/1/image', { responseType: 'arraybuffer' });

      expect(result).toBe(`data:${mockContentType};base64,mockBase64String`);
    });
  });
});
