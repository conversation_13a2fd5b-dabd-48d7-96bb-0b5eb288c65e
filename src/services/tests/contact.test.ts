import { searchContacts, createContact } from '../contact';
import { farmCheckApi } from '..';

jest.mock('..', () => ({
  farmCheckApi: {
    get: jest.fn(),
    post: jest.fn()
  }
}));

describe('Contact Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('searchContacts', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const mockParams = { query: 'test', page: 1 };
      const mockResponse = {
        data: {
          items: [
            { id: 1, name: 'Contact 1' },
            { id: 2, name: 'Contact 2' }
          ],
          total: 2
        }
      };

      (farmCheckApi.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await searchContacts(mockParams);

      expect(farmCheckApi.get).toHaveBeenCalledWith('/search/contacts', {
        params: mockParams
      });

      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('createContact', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const mockParams = {
        values: [
          {
            id: 'uuid',
            type: 'cpf',
            contactName: 'Test Contact',
            document: '12345678901'
          }
        ]
      };
      const mockResponse = {
        data: { success: true }
      };

      (farmCheckApi.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await createContact(mockParams);

      expect(farmCheckApi.post).toHaveBeenCalledWith('/contact', mockParams);

      expect(result).toEqual(mockResponse.data);
    });
  });
});
