import { createTags, listTags, addTagToRequest } from '../tags';
import { api } from '..';

jest.mock('..', () => ({
  api: {
    post: jest.fn(),
    get: jest.fn()
  }
}));

describe('Tags Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createTags', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const tagList = ['tag1', 'tag2', 'tag3'];
      const mockResponse = {
        data: [
          { id: 1, name: 'tag1' },
          { id: 2, name: 'tag2' },
          { id: 3, name: 'tag3' }
        ]
      };

      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await createTags(tagList);

      expect(api.post).toHaveBeenCalledWith(
        '/tags',
        [{ name: 'tag1' }, { name: 'tag2' }, { name: 'tag3' }]
      );
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('listTags', () => {
    it('should call the API and return the data', async () => {
      const mockResponse = {
        data: [
          { id: 1, name: 'tag1' },
          { id: 2, name: 'tag2' }
        ]
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await listTags();

      expect(api.get).toHaveBeenCalledWith('/tags');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('addTagToRequest', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const requestId = 123;
      const tagId = 456;
      const mockResponse = {
        data: { success: true }
      };

      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await addTagToRequest(requestId, tagId);

      expect(api.post).toHaveBeenCalledWith(
        `/reports-requests/${requestId}/tags/${tagId}/add`
      );
      expect(result).toEqual(mockResponse.data);
    });
  });
});
