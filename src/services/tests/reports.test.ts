import { listReports, downloadReport, cloneReport, downloadReportProperties } from '../reports';
import { api } from '..';
import { ReportType } from '@types';

jest.mock('..', () => ({
  api: {
    get: jest.fn(),
    post: jest.fn()
  }
}));

describe('Reports Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('listReports', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const mockParams = { limit: 10, offset: 0 };
      const mockResponse = {
        data: {
          items: [
            { id: 1, name: 'Report 1' },
            { id: 2, name: 'Report 2' }
          ],
          count: 2
        }
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await listReports(mockParams);

      expect(api.get).toHaveBeenCalledWith('/reports', { params: mockParams });

      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('downloadReport', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const reportId = 123;
      const exportId = 456;
      const mockResponse = {
        data: new Blob(['test data'], { type: 'application/pdf' })
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await downloadReport(reportId, exportId);

      expect(api.get).toHaveBeenCalledWith(
        `/reports/${reportId}/exports/${exportId}/data`,
        { responseType: 'blob' }
      );

      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('cloneReport', () => {
    it('should call the API with the correct parameters and return the data', async () => {
      const reportId = 123;
      const mockResponse = {
        data: { id: 456, name: 'Cloned Report' }
      };

      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await cloneReport(reportId);

      expect(api.post).toHaveBeenCalledWith(`/reports/${reportId}/clone`);

      expect(result).toEqual(mockResponse.data);
    });

    it('should include the new report type in the URL if provided', async () => {
      const reportId = 123;
      const reportType = ReportType.INSPECTION;
      const mockResponse = {
        data: { id: 456, name: 'Cloned Report', type: ReportType.INSPECTION }
      };

      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await cloneReport(reportId, reportType);

      expect(api.post).toHaveBeenCalledWith(`/reports/${reportId}/clone?new_type=${reportType}`);

      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('downloadReportProperties', () => {
    it('should call the API with the correct parameters and return the data and filename', async () => {
      const reportId = 123;
      const mockResponse = {
        data: new Blob(['test data'], { type: 'application/csv' }),
        headers: {
          get: jest.fn().mockReturnValue('attachment; filename="properties.csv"')
        }
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await downloadReportProperties(reportId);

      expect(api.get).toHaveBeenCalledWith(`reports/${reportId}/property`, {
        responseType: 'blob'
      });

      expect(result).toEqual({
        data: mockResponse.data,
        filename: 'properties.csv'
      });
    });
  });
});
