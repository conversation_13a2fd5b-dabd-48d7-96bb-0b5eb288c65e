import { api } from ".";
import { Tag } from "../types/tag";

export async function createTags(tagList: string[]): Promise<Tag[]> {
  const { data } = await api.post<Tag[]>(
    `/tags`,
    tagList.map((t) => ({ name: t }))
  );
  return data;
}

export async function listTags(): Promise<Tag[]> {
  const { data } = await api.get<Tag[]>(`/tags`);
  return data;
}

export async function addTagToRequest(request_id: number, tag_id: number) {
  const { data } = await api.post(
    `/reports-requests/${request_id}/tags/${tag_id}/add`
  );
  return data;
}
