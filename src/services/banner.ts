import { api } from ".";
import { BannerData, PaginatedResponse } from "../types";

export async function listBanners(): Promise<PaginatedResponse<BannerData>> {
  const { data } = await api.get<PaginatedResponse<BannerData>>("/banners");
  const items = await Promise.all(
    data.items.map((banner) => {
      if (banner.image) {
        return getBannerImage(banner.id);
      }
      return Promise.resolve(null);
    })
  ).then((images: (string | null)[]) => {
    return images.map((imageSource: string | null, index: number) => ({
      ...data.items[index],
      image: imageSource,
    }));
  });
  return {
    ...data,
    items,
  };
}

export async function getBannerImage(
  bannerId: string | number
): Promise<string> {
  const { data, headers } = await api.get(`/banners/${bannerId}/image`, {
    responseType: "arraybuffer",
  });
  const base64 = btoa(
    new Uint8Array(data).reduce(
      (data, byte) => data + String.fromCharCode(byte),
      ""
    )
  );
  return `data:${headers["Content-Type"]};base64,${base64}`;
}
