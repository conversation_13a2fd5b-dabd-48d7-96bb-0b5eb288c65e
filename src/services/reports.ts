import { ListReportsParams, ReportType } from "@types";
import { api } from ".";
import { Report } from "../types/report";

export async function listReports(params: ListReportsParams) {
  const { data } = await api.get(`/reports`, { params });
  return data;
}

export async function downloadReport(reportId: number, exportId: number) {
  const { data } = await api.get(
    `/reports/${reportId}/exports/${exportId}/data`,
    {
      responseType: "blob",
    }
  );
  return data;
}

export async function cloneReport(
  reportId: string | number,
  reportType?: ReportType
): Promise<Report> {
  let url = `/reports/${reportId}/clone`;
  if (reportType) {
    url = `${url}?new_type=${reportType}`;
  }
  const { data } = await api.post(url);
  return data;
}

export async function downloadReportProperties(reportId: string | number) {
  const response = await api.get(`reports/${reportId}/property`, {
    responseType: "blob",
  });
  const data = response.data;
  const filename = (response.headers as any)
    .get("content-disposition")
    ?.split('"')[1];
  return { data, filename };
}
