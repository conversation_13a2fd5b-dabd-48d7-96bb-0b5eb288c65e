export async function downloadFile(fileData, filename, fileType) {
  const blob = new Blob([fileData], {
    type: fileType,
  });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.dispatchEvent(
    new MouseEvent(`click`, {
      bubbles: true,
      cancelable: true,
      view: window,
    })
  );
  setTimeout(function () {
    link.remove();
  }, 0);
}
