import { Coordinate } from "ol/coordinate";
import { api, farm<PERSON>heck<PERSON><PERSON> } from ".";
import { ContactProperties } from "@/types/property";
import { AxiosResponse } from "axios";

export async function getProperties(
  contactId: string | number
): Promise<ContactProperties> {
  const { data }: AxiosResponse<ContactProperties> = await farmCheckApi.get(
    `/contacts/${contactId}/properties`
  );
  return data;
}

export async function searchCARByClick(coords: Coordinate) {
  const { data } = await farmCheckApi.post(`/property/click`, coords);
  return data;
}

export async function getByCar(car: string) {
  const { data } = await farmCheckApi.post(`/property/getByCar`, {
    car,
  });
  return data;
}

export async function getByCode(code: string, query: string) {
  const { data } = await api.post(`/agrowatch/search`, {
    query,
    value: code,
  });
  return data;
}

export async function createProperty(
  contactId: number,
  groupId: number,
  propertyName: string,
  geometry,
  car: string,
  origin: "MANUAL" | "CAR" = "MANUAL"
) {
  const { data } = await farmCheckApi.post(`/property`, {
    area_code: "",
    area_cultures: [],
    area_name: propertyName,
    car,
    contact: contactId,
    coordinates: {
      geometry: geometry,
    },
    group_id: groupId,
    manually_created: true,
    notes: "",
    relation: "OWNER",
    origin,
    tags: [],
    type: "1",
  });
  return data;
}
