import { AvailableReport } from "@constants";
import { api } from ".";
import { CardData, ReportFormat, ReportType, TagCard } from "../types";

export async function listCards(): Promise<AvailableReport[]> {
  const { data } = await api.get<CardData[]>("/cards");
  const response = data.map((card) => {
    return {
      type: card.report_type,
      title: card.title,
      description: card.description,
      formats: card.files_format.map(
        (format) => ReportFormat[format.file_format]
      ),
      models: card.models,
      isNew: card.tag == TagCard.NEW,
      commingSoon: card.tag == TagCard.SOON,
      hasMetadata: card.has_metadata,
      hasSignature: card.has_signature,
      hasSignatureResource: card.signature_resource,
      noSignatureResource: card.no_signature_resource,
      hasPermission: card.has_permission,
      value: card.value,
    } as AvailableReport;
  });
  let cecafeIdx = response.findIndex(
    (x) => x.type == ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE
  );
  if (cecafeIdx > 0) {
    const cecafe = response.splice(cecafeIdx, 1)[0];
    response.unshift(cecafe);
  }
  return response;
}

export async function getFileModel(url: string, format): Promise<any> {
  const { data } = await api.get(url, {
    responseType: "arraybuffer",
  });
  return downloadModel(data, new Date().toISOString(), format);
}

function downloadModel(data, filename, format: string) {
  const blob = new Blob([data]);
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `${filename}.${format.toLowerCase()}`;
  link.dispatchEvent(
    new MouseEvent(`click`, {
      bubbles: true,
      cancelable: true,
      view: window,
    })
  );
  setTimeout(function () {
    link.remove();
  }, 0);
}
