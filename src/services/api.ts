import axios from "axios";

export const api = axios.create({
  baseURL: process.env.AGREPORT_API_URL,
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if ([401, 403].includes(error?.response?.status)) {
      let url = process.env.MONOLITH_APP_URL;

      if (error?.response?.status === 401) url += `/login?logout=success`;

      if (error?.response?.status === 403) url += `/403`;

      location.replace(url);
    }

    return Promise.reject(error);
  }
);

api.interceptors.request.use(
  (config: any) => {
    const token = localStorage.getItem("auth-token");

    config.headers = {
      ...config.headers,
      Authorization: `Bearer ${token}`,
    };

    return config;
  },
  (error) => Promise.reject(error)
);
