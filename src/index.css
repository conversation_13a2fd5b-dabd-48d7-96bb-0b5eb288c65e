@import 'brainui/dist/brainui.css';

:root {
    --surface-color-0: #f5f5f5;
    --surface-color-1: #FFF;
    --surface-color-2: #E2E4E7;
    --surface-color-3: #C8CCD1;
    --surface-color-4: #F8F8F8;
    --color-primary: #00b277;
}

html, body {
    margin: 0;
    color: #262626;
}

.floating-panel {
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 9px 28px 8px rgba(0, 0, 0, 0.05)
}

.divider-h-small {
    margin: 10px 0;
}

.label-input input {
    padding: 20px 12px 4px 11px !important;
}

.ol-zoom {
    display: flex;
    flex-direction: column;
    gap: 7px;
    margin: 10px;
}

.ol-zoom-in, .ol-zoom-out {
    color: #fff;
    background: rgb(0, 178, 119);
    border-radius: 8px;
    cursor: pointer;
    height: 24px;
    width: 24px;
    line-height: 1;
    font-weight: bold;
    border: none;
}

.filter-range-picker .ant-picker.ant-picker-range {
    padding: 20px 12px 4px 11px !important;
}

.date-picker .ant-picker {
    padding: 20px 12px 4px 11px !important;
}

.pb-multi-select input {
    height: 17.5px;
}

.pb-multi-select .ant-select-selection-item {
    height: 18px !important;
    line-height: 16px !important;
    margin: 0 !important;
  }

.ar-checkbox-multiline {
    align-items: center;
    
    p {
        margin-bottom: 0;
    }
}

.ar-banner-skeleton {
    height: 204px !important;
    width: 100% !important;
    border-radius: 4px;
}

.ar-btn {
    border-radius: 4px !important;
    padding: 4px 16px !important;
    font-weight: 400 !important;
}

.ar-btn.ant-btn-link {
    padding: 0 !important;
}

.ar-btn:not(:disabled):not(.ant-btn-disabled) {
    color: #118385 !important;
}


.ar-select-placeholder .ant-select-selection-placeholder {
    color: #118385 !important;
}

.ar-btn-primary {
    padding: 4px 16px !important;
    font-weight: 400 !important;
    border-radius: 4px !important;
    border: none !important;
}

.ar-btn-primary:not(:disabled) {
    background: #118385 !important;
    color: #fff;
}

.ar-alert-message .ant-alert-message {
    color: #d63a47 !important;
    font-weight: 500;
}

.ar-tooltip-overlay {
    pointer-events: none;
}

.empty-title-steps .ant-steps-item-title:after {
    top: 16px;
}

.empty-title-steps .ant-steps-item-title {
    padding-right: 5px;
}

.ar-reports-table table {
    border-collapse: collapse !important;
}
