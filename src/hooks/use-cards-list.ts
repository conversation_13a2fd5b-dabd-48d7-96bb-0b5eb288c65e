import { useEffect } from "react";
import { useAppDispatch } from "./use-app-dispatch";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { thunkListCard } from "@/store/modules/card/list/thunks";

export const useCardsList = () => {
  const dispatch = useAppDispatch();
  const { data: cardsList, loading: isLoadingCards } = useSelector(
    (state: RootState) => state.cardList
  );

  function handleListCards() {
    dispatch(thunkListCard());
  }

  useEffect(() => {
    if (!isLoadingCards && !cardsList.length) {
      handleListCards();
    }
  }, [cardsList, isLoadingCards]);

  return {
    cardsList,
    handleListCards,
    isLoadingCards,
  };
};
