import { renderHook } from "@testing-library/react";
import { useSelector } from "react-redux";
import { useAppDispatch } from "../use-app-dispatch";
import { thunkListCard } from "../../store/modules/card/list/thunks";
import { act } from "react";

jest.unmock("../use-cards-list");

import { useCardsList } from "../use-cards-list";

jest.mock("react-redux", () => ({
  useSelector: jest.fn(),
}));

jest.mock("../use-app-dispatch", () => ({
  useAppDispatch: jest.fn(),
}));

jest.mock("../../store/modules/card/list/thunks", () => ({
  thunkListCard: jest.fn(),
}));

describe("useCardsList", () => {
  const mockDispatch = jest.fn();
  const mockCardsList = [
    { type: "VALUATION", title: "Valuation Report" },
    { type: "INSPECTION", title: "Inspection Report" },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useAppDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useSelector as jest.Mock).mockImplementation((selector) =>
      selector({
        cardList: {
          data: mockCardsList,
          loading: false,
        },
      })
    );
    (thunkListCard as unknown as jest.Mock).mockReturnValue({ type: "MOCK_THUNK_ACTION" });
  });

  it("should return cardsList from state", () => {
    const { result } = renderHook(() => useCardsList());

    expect(result.current.cardsList).toEqual(mockCardsList);
    expect(result.current.isLoadingCards).toBe(false);
  });

  it("should provide a function to fetch cards", () => {
    const { result } = renderHook(() => useCardsList());

    act(() => {
      result.current.handleListCards();
    });

    expect(mockDispatch).toHaveBeenCalledWith({ type: "MOCK_THUNK_ACTION" });
  });

  it("should fetch cards on mount if cardsList is empty", () => {
    (useSelector as jest.Mock).mockImplementation((selector) =>
      selector({
        cardList: {
          data: [],
          loading: false,
        },
      })
    );

    renderHook(() => useCardsList());

    expect(mockDispatch).toHaveBeenCalledWith({ type: "MOCK_THUNK_ACTION" });
  });

  it("should not fetch cards on mount if cardsList is not empty", () => {
    renderHook(() => useCardsList());

    expect(mockDispatch).not.toHaveBeenCalled();
  });

  it("should not fetch cards if already loading", () => {
    (useSelector as jest.Mock).mockImplementation((selector) =>
      selector({
        cardList: {
          data: [],
          loading: true,
        },
      })
    );

    renderHook(() => useCardsList());

    expect(mockDispatch).not.toHaveBeenCalled();
  });
});
