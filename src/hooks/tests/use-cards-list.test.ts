import { useCardsList } from '../use-cards-list';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(() => ({ data: [], loading: false }))
}));

jest.mock('../use-app-dispatch', () => ({
  useAppDispatch: jest.fn(() => jest.fn())
}));

jest.mock('@/store/modules/card/list/thunks', () => ({
  thunkListCard: jest.fn()
}));

describe('useCardsList', () => {
  it('should be defined', () => {
    expect(useCardsList).toBeDefined();
    expect(typeof useCardsList).toBe('function');
  });
});
