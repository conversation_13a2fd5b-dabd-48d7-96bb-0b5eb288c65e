import { renderHook } from "@testing-library/react";
import { RequestFormContext } from "@contexts";
import { ReportType } from "@types";
import { Form } from "antd";
import React from "react";

jest.unmock("../use-request-form");

import { useRequestForm } from "../use-request-form";

jest.mock("antd", () => ({
  Form: {
    useForm: jest.fn(),
  },
}));

jest.mock("@contexts", () => ({
  RequestFormContext: {
    Consumer: jest.fn(),
    Provider: jest.fn(),
  },
}));

describe("useRequestForm", () => {
  const mockOpenRequestForm = jest.fn();
  const mockCloseRequestForm = jest.fn();
  const mockForm = { validateFields: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    (Form.useForm as jest.Mock).mockReturnValue([mockForm]);

    jest.spyOn(React, "useContext").mockReturnValue({
      openRequestForm: mockOpenRequestForm,
      closeRequestForm: mockCloseRequestForm,
    });
  });

  it("should return form and handler functions", () => {
    const { result } = renderHook(() => useRequestForm());

    expect(result.current.form).toBe(mockForm);
    expect(typeof result.current.handleOpenRequestForm).toBe("function");
    expect(typeof result.current.handleCloseRequestForm).toBe("function");
  });

  it("should call openRequestForm when handleOpenRequestForm is called", () => {
    const { result } = renderHook(() => useRequestForm());

    result.current.handleOpenRequestForm(ReportType.VALUATION);

    expect(mockOpenRequestForm).toHaveBeenCalledWith(ReportType.VALUATION);
  });

  it("should call closeRequestForm when handleCloseRequestForm is called", () => {
    const { result } = renderHook(() => useRequestForm());

    result.current.handleCloseRequestForm();

    expect(mockCloseRequestForm).toHaveBeenCalled();
  });
});
