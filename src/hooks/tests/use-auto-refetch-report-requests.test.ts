import { renderHook } from '@testing-library/react';
import { useAutoRefetchReportRequests } from '../use-auto-refetch-report-requests';
import { ReportType } from '../../types/report-type';

jest.useFakeTimers();

describe('useAutoRefetchReportRequests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  it('should set up interval for auto-generated reports that are pending', () => {
    const mockFetchFunction = jest.fn();
    const mockRequests = [
      {
        id: 1,
        reports_pending: 2,
        reports_processing: 0,
        reports_types: [ReportType.SOCIOENVIRONMENT_PROTOCOL]
      }
    ];
    
    const setIntervalSpy = jest.spyOn(global, 'setInterval');
    
    renderHook(() => useAutoRefetchReportRequests(mockFetchFunction, mockRequests, 10));
    
    expect(setIntervalSpy).toHaveBeenCalledTimes(1);
    expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 10000);
    
    jest.advanceTimersByTime(10000);
    
    expect(mockFetchFunction).toHaveBeenCalledTimes(1);
  });

  it('should not set up interval when no auto-generated reports are pending', () => {
    const mockFetchFunction = jest.fn();
    const mockRequests = [
      {
        id: 1,
        reports_pending: 2,
        reports_processing: 0,
        reports_types: [ReportType.INSPECTION] // Not an auto-generated report
      }
    ];
    
    const setIntervalSpy = jest.spyOn(global, 'setInterval');
    
    renderHook(() => useAutoRefetchReportRequests(mockFetchFunction, mockRequests, 10));
    
    expect(setIntervalSpy).not.toHaveBeenCalled();
  });
});
