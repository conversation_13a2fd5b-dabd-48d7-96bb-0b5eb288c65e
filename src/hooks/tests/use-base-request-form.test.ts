import { renderHook, act } from '@testing-library/react';
import { useBaseRequestForm } from '../use-base-request-form';

jest.mock('@/utils', () => ({
  getExtent: jest.fn(() => [0, 0, 100, 100]),
  geojsonToFeatures: jest.fn(() => [{ getGeometry: () => ({ getExtent: () => [0, 0, 100, 100] }) }])
}));

jest.mock('ol/source/Vector', () => {
  return jest.fn().mockImplementation(() => ({
    clear: jest.fn(),
    addFeatures: jest.fn()
  }));
});

jest.mock('@services', () => ({
  getProperties: jest.fn(() => Promise.resolve({ properties: [] })),
  getPropertyFields: jest.fn(() => Promise.resolve({ properties: [] })),
  searchContacts: jest.fn(() => Promise.resolve([]))
}));

describe('useBaseRequestForm', () => {
  const mockProps = {
    onPropertiesLoad: jest.fn(),
    selectedSubareas: [],
    onSubareasLoad: jest.fn(),
    properties: [],
    selectedProperty: null,
    data: {}
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useBaseRequestForm(mockProps));
    
    expect(result.current.showMap).toBe(true);
    expect(result.current.isLoadingContacts).toBe(false);
    expect(result.current.contacts).toEqual([]);
    expect(result.current.isContactFormOpen).toBe(false);
    expect(result.current.isPropertyDrawerOpen).toBe(false);
    expect(result.current.isFieldsDrawerOpen).toBe(false);
  });

  it('should expose the expected functions and state setters', () => {
    const { result } = renderHook(() => useBaseRequestForm(mockProps));
    
    expect(result.current.onCreateNewArea).toBeDefined();
    expect(result.current.debouncedText).toBeDefined();
    expect(result.current.setMap).toBeDefined();
    expect(result.current.setShowMap).toBeDefined();
    expect(result.current.setIsContactFormOpen).toBeDefined();
    expect(result.current.setIsPropertyDrawerOpen).toBeDefined();
    expect(result.current.setIsFieldsDrawerOpen).toBeDefined();
    expect(result.current.setIsDrawingSubareas).toBeDefined();
    expect(result.current.fetchProperties).toBeDefined();
    expect(result.current.fetchSubareas).toBeDefined();
  });

  it('should toggle state values correctly', () => {
    const { result } = renderHook(() => useBaseRequestForm(mockProps));
    
    act(() => {
      result.current.setShowMap(false);
    });
    expect(result.current.showMap).toBe(false);
    
    act(() => {
      result.current.setIsContactFormOpen(true);
    });
    expect(result.current.isContactFormOpen).toBe(true);
    
    act(() => {
      result.current.setIsPropertyDrawerOpen(true);
    });
    expect(result.current.isPropertyDrawerOpen).toBe(true);
    
    act(() => {
      result.current.setIsFieldsDrawerOpen(true);
    });
    expect(result.current.isFieldsDrawerOpen).toBe(true);
  });
});
