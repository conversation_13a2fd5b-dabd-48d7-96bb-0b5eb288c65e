import { renderHook, act } from '@testing-library/react';
import { useContactForm } from '../use-contact-form';
import { createContact } from '@services';
import { notification } from 'antd';
import { IdentificationType } from '@types';

jest.mock('@services', () => ({
  createContact: jest.fn()
}));

jest.mock('antd', () => ({
  notification: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));

jest.mock('uuid', () => ({
  v4: () => 'mock-uuid'
}));

describe('useContactForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const mockOnSuccess = jest.fn();
    
    const { result } = renderHook(() => useContactForm({ onSuccess: mockOnSuccess }));
    
    expect(result.current.isSaving).toBe(false);
    expect(result.current.value).toEqual([
      {
        key: 0,
        document: '',
        name: '',
        cities: [],
        CAR: [],
        type: IdentificationType.DOCUMENT
      }
    ]);
  });

  it('should add a new item', () => {
    const mockOnSuccess = jest.fn();
    
    const { result } = renderHook(() => useContactForm({ onSuccess: mockOnSuccess }));
    
    act(() => {
      result.current.onAddItem();
    });
    
    expect(result.current.value.length).toBe(2);
    expect(result.current.value[1]).toEqual({
      key: 1,
      document: '',
      name: '',
      cities: [],
      CAR: [],
      type: IdentificationType.DOCUMENT
    });
  });

  it('should remove an item', () => {
    const mockOnSuccess = jest.fn();
    
    const { result } = renderHook(() => useContactForm({ onSuccess: mockOnSuccess }));
    
    act(() => {
      result.current.onAddItem();
    });
    
    act(() => {
      result.current.onRemoveItem({ key: 0 });
    });
    
    expect(result.current.value.length).toBe(1);
    expect(result.current.value[0].key).toBe(1);
  });

  it('should update an item', () => {
    const mockOnSuccess = jest.fn();
    
    const { result } = renderHook(() => useContactForm({ onSuccess: mockOnSuccess }));
    
    act(() => {
      result.current.onItemChange(0, {
        document: '12345678901',
        name: 'Test Name',
        identificationType: IdentificationType.DOCUMENT,
        cities: ['City 1'],
        CAR: ['CAR 1']
      });
    });
    
    expect(result.current.value[0]).toEqual({
      key: 0,
      document: '12345678901',
      name: 'Test Name',
      type: IdentificationType.DOCUMENT,
      cities: ['City 1'],
      CAR: ['CAR 1']
    });
  });

  it('should create contacts successfully', async () => {
    (createContact as jest.Mock).mockResolvedValue({});
    
    const mockOnSuccess = jest.fn();
    
    const { result } = renderHook(() => useContactForm({ onSuccess: mockOnSuccess }));
    
    act(() => {
      result.current.onItemChange(0, {
        document: '12345678901',
        name: 'Test Name',
        identificationType: IdentificationType.DOCUMENT,
        cities: [],
        CAR: []
      });
    });
    
    await act(async () => {
      await result.current.createContacts();
    });
    
    expect(createContact).toHaveBeenCalledWith({
      values: [
        {
          id: 'mock-uuid',
          type: 'cpf',
          typeAcronym: 'cpf',
          contactName: 'Test Name',
          nameLabel: '',
          document: '12345678901',
          cars: [],
          cities: [],
          inputType: 'manual',
          search_property: false
        }
      ],
      crop: '2024',
      consults: JSON.stringify([1]),
      tags: [],
      product_origin: 'AGRO_REPORT',
      no_consult: true
    });
    
    expect(notification.success).toHaveBeenCalled();
    
    expect(mockOnSuccess).toHaveBeenCalled();
    
    expect(result.current.value).toEqual([
      {
        key: 0,
        document: '',
        name: '',
        type: IdentificationType.DOCUMENT,
        cities: [],
        CAR: []
      }
    ]);
  });
});
