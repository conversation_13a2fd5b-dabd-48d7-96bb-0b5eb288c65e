import { renderHook } from '@testing-library/react';
import { useAutoRefetchReports } from '../use-auto-refetch-reports';
import { Report, ReportStatus } from '@types';

jest.useFakeTimers();

describe('useAutoRefetchReports', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  });

  it('should set up an interval when reports have status to watch', () => {
    const mockFetchFunction = jest.fn();
    const mockReports = [
      { id: 1, status: ReportStatus.PROCESSING },
      { id: 2, status: ReportStatus.DONE }
    ] as Report[];
    
    const setIntervalSpy = jest.spyOn(global, 'setInterval');
    
    renderHook(() => useAutoRefetchReports(mockFetchFunction, mockReports, 10));
    
    expect(setIntervalSpy).toHaveBeenCalledTimes(1);
    expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 10000);
    
    jest.advanceTimersByTime(10000);
    
    expect(mockFetchFunction).toHaveBeenCalledTimes(1);
    
    jest.advanceTimersByTime(10000);
    
    expect(mockFetchFunction).toHaveBeenCalledTimes(2);
  });

  it('should not set up an interval when no reports have status to watch', () => {
    const mockFetchFunction = jest.fn();
    const mockReports = [
      { id: 1, status: ReportStatus.DONE },
      { id: 2, status: ReportStatus.ERROR }
    ] as Report[];
    
    const setIntervalSpy = jest.spyOn(global, 'setInterval');
    
    renderHook(() => useAutoRefetchReports(mockFetchFunction, mockReports, 10));
    
    expect(setIntervalSpy).not.toHaveBeenCalled();
    
    jest.advanceTimersByTime(10000);
    
    expect(mockFetchFunction).not.toHaveBeenCalled();
  });

  it('should clear the interval when unmounted', () => {
    const mockFetchFunction = jest.fn();
    const mockReports = [
      { id: 1, status: ReportStatus.PENDING },
      { id: 2, status: ReportStatus.DONE }
    ] as Report[];
    
    const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
    
    const { unmount } = renderHook(() => useAutoRefetchReports(mockFetchFunction, mockReports, 10));
    
    unmount();
    
    expect(clearIntervalSpy).toHaveBeenCalledTimes(1);
  });

  it('should clear the interval when reports change to not need watching', () => {
    const mockFetchFunction = jest.fn();
    const initialReports = [
      { id: 1, status: ReportStatus.PROCESSING },
      { id: 2, status: ReportStatus.DONE }
    ] as Report[];
    
    const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
    
    const { rerender } = renderHook(
      ({ reports }) => useAutoRefetchReports(mockFetchFunction, reports, 10),
      { initialProps: { reports: initialReports } }
    );
    
    const updatedReports = [
      { id: 1, status: ReportStatus.DONE },
      { id: 2, status: ReportStatus.DONE }
    ] as Report[];
    
    rerender({ reports: updatedReports });
    
    expect(clearIntervalSpy).toHaveBeenCalled();
    
    jest.advanceTimersByTime(10000);
    
    expect(mockFetchFunction).not.toHaveBeenCalled();
  });
});
