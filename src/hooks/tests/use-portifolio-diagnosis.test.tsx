
import { RequestMethod } from "@types";
import { renderHook } from "@testing-library/react";
import { act } from "react";

jest.mock("../use-portifolio-diagnosis", () => ({
    usePortifolioDiagnosis: jest.fn(),
}));

import { usePortifolioDiagnosis } from "../use-portifolio-diagnosis";

describe("usePortifolioDiagnosis", () => {
    const mockData = {
        manual: {
            municipaliciesSelected: [],
            statesSelected: [],
            municipalities: [],
            states: [],
            subModulesSelected: [],
            subModules: [],
            selectType: null,
        },
        batch: {
            documentType: null,
            fileDetails: { keyName: "", name: "", type: "" },
            subModulesAvailable: [],
            documentsList: [],
            invalidDocuments: [],
        },
        requestMethod: null,
    };

    const mockHandleSetRequestMethod = jest.fn();
    const mockHandleUploadBatchFile = jest.fn();
    const mockHandleChangeManualSubModules = jest.fn();
    const mockHandleClickCancel = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();

        (usePortifolioDiagnosis as jest.Mock).mockReturnValue({
            data: mockData,
            isLoading: false,
            isManualFormEnabled: false,
            handleSetRequestMethod: mockHandleSetRequestMethod,
            handleUploadBatchFile: mockHandleUploadBatchFile,
            handleChangeManualSubModules: mockHandleChangeManualSubModules,
            handleClickCancel: mockHandleClickCancel,
        });
    });

    it("should initialize with default values", () => {
        const { result } = renderHook(() => usePortifolioDiagnosis());
        expect(result.current.data).toEqual(mockData);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.isManualFormEnabled).toBe(false);
    });

    it("should dispatch action to set request method", () => {
        const { result } = renderHook(() => usePortifolioDiagnosis());

        act(() => {
            result.current.handleSetRequestMethod(RequestMethod.BATCH);
        });

        expect(mockHandleSetRequestMethod).toHaveBeenCalledWith(RequestMethod.BATCH);
    });

    it("should handle batch file upload with invalid CAR documents", async () => {
        const mockFile = new File(["car"], "test.csv", { type: "text/csv" });

        const { result } = renderHook(() => usePortifolioDiagnosis());

        await act(async () => {
            await result.current.handleUploadBatchFile(mockFile);
        });

        expect(mockHandleUploadBatchFile).toHaveBeenCalledWith(mockFile);
    });

    it("should handle manual sub-modules change", () => {
        const { result } = renderHook(() => usePortifolioDiagnosis());

        act(() => {
            result.current.handleChangeManualSubModules(["subModule1"]);
        });

        expect(mockHandleChangeManualSubModules).toHaveBeenCalledWith(["subModule1"]);
    });

    it("should reset state on cancel", () => {
        const { result } = renderHook(() => usePortifolioDiagnosis());

        act(() => {
            result.current.handleClickCancel();
        });

        expect(mockHandleClickCancel).toHaveBeenCalled();
    });
});