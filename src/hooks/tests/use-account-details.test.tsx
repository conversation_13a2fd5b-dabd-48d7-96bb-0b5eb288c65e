import { useSelector } from "react-redux";
import { useAppDispatch } from "../use-app-dispatch";
import { Resource } from "../../constants/resources";
import { renderHook } from "@testing-library/react";
import { useAccountDetails } from "../use-account-details";
import { act } from "react";
import { thunkGetDetailsAccount } from "../../store/modules/account/get-details/thunks";
import { ReportType } from "@types";

jest.unmock("../use-account-details");

jest.mock("react-redux", () => ({
  useSelector: jest.fn(),
}));

jest.mock("../use-app-dispatch", () => ({
  useAppDispatch: jest.fn(),
}));

jest.mock("../../store/modules/account/get-details/thunks", () => ({
  thunkGetDetailsAccount: jest.fn(),
}));

jest.mock("../use-cards-list", () => ({
  useCardsList: jest.fn(),
}));

import { useCardsList } from "../use-cards-list";

describe("useAccountDetails", () => {
  const mockDispatch = jest.fn();
  const mockData = {
    resources: [Resource.InspectionReportSigned],
    credit_available: {
      [ReportType.VALUATION]: 100,
      [ReportType.PORTFOLIO_DIAGNOSIS]: 50,
      [ReportType.AGRO_CREDIT]: 0
    }
  };
  const mockCardsList = [
    { type: ReportType.VALUATION, value: 10 },
    { type: ReportType.PORTFOLIO_DIAGNOSIS, value: 20 },
    { type: ReportType.AGRO_CREDIT, value: 30 }
  ];

  beforeEach(() => {
    (useAppDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useSelector as jest.Mock).mockImplementation((selectorFn) =>
      selectorFn({ getDetailsAccount: { data: mockData } })
    );
    (useCardsList as jest.Mock).mockReturnValue({ cardsList: mockCardsList });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return account data", () => {
    const { result } = renderHook(() => useAccountDetails());
    expect(result.current.account).toEqual(mockData);
  });

  it("should call thunkGetDetailsAccount when handleGetDetailsAccount is called", () => {
    const { result } = renderHook(() => useAccountDetails());

    act(() => {
      result.current.handleGetDetailsAccount();
    });

    expect(mockDispatch).toHaveBeenCalledWith(thunkGetDetailsAccount());
  });

  it("should return true when checking for a resource that exists", () => {
    const { result } = renderHook(() => useAccountDetails());

    expect(result.current.hasResource(Resource.InspectionReportSigned)).toBe(true);
  });

  it("should return false when checking for a resource that doesn't exist", () => {
    const { result } = renderHook(() => useAccountDetails());

    expect(result.current.hasResource(Resource.ValuationReportSigned)).toBe(false);
  });

  describe("isAbleToRequestReportByReportType", () => {
    it("should return false for both single and batch reports when account is undefined", () => {
      (useSelector as jest.Mock).mockImplementation((selectorFn) =>
        selectorFn({ getDetailsAccount: { data: undefined } })
      );

      const { result } = renderHook(() => useAccountDetails());

      const ability = result.current.isAbleToRequestReportByReportType(ReportType.VALUATION);

      expect(ability).toEqual({
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false
      });
    });

    it("should return false for both single and batch reports when cardsList is empty", () => {
      (useCardsList as jest.Mock).mockReturnValue({ cardsList: [] });

      const { result } = renderHook(() => useAccountDetails());

      const ability = result.current.isAbleToRequestReportByReportType(ReportType.VALUATION);

      expect(ability).toEqual({
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false
      });
    });

    it("should return false for both single and batch reports when report type doesn't exist in cardsList", () => {
      const { result } = renderHook(() => useAccountDetails());

      const ability = result.current.isAbleToRequestReportByReportType(ReportType.SINISTER);

      expect(ability).toEqual({
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false
      });
    });

    it("should return true for both single and batch reports when credit is sufficient", () => {
      const { result } = renderHook(() => useAccountDetails());

      const ability = result.current.isAbleToRequestReportByReportType(ReportType.VALUATION);

      expect(ability).toEqual({
        isAbleToRequestSingleReport: true,
        isAbleToRequestBatchReports: true
      });
    });

    it("should return false for both single and batch reports when credit is insufficient", () => {
      const { result } = renderHook(() => useAccountDetails());

      const ability = result.current.isAbleToRequestReportByReportType(ReportType.AGRO_CREDIT);

      expect(ability).toEqual({
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false
      });
    });

    it("should handle undefined credit_available for a report type", () => {
      const { result } = renderHook(() => useAccountDetails());

      const ability = result.current.isAbleToRequestReportByReportType(ReportType.SINISTER);

      expect(ability).toEqual({
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false
      });
    });
  });
});
