import { renderHook } from "@testing-library/react";
import { useAgroCreditFormReview } from "../use-agro-credit-form-review";
import ExcelJS from "exceljs";

jest.mock("exceljs", () => {
    const mockWorkbook = {
        addWorksheet: jest.fn().mockReturnValue({
            columns: [],
            addRow: jest.fn(),
        }),
        xlsx: {
            writeBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(8)),
        },
    };
    return {
        Workbook: jest.fn(() => mockWorkbook),
    };
});

global.URL.createObjectURL = jest.fn(() => "blob:mock-url");

describe("useAgroCreditFormReview", () => {
    it("should generate and download an Excel file with invalid documents", async () => {
        jest.useFakeTimers();

        const { result } = renderHook(() => useAgroCreditFormReview());
        const mockData = {
            filename: "test-file",
            invalidRows: [
                { document: "doc1", debt: "100" },
                { document: "doc2", debt: "200" },
            ],
        };

        const createElementSpy = jest.spyOn(document, "createElement");
        const linkMock = {
            href: "",
            download: "",
            dispatchEvent: jest.fn(),
            remove: jest.fn(),
        };
        createElementSpy.mockReturnValue(linkMock as unknown as HTMLAnchorElement);

        await result.current.downloadInvalidDocuments(mockData);

        jest.runAllTimers();

        expect(ExcelJS.Workbook).toHaveBeenCalledTimes(1);
        const mockWorkbook = (ExcelJS.Workbook as jest.Mock).mock.results[0].value;
        expect(mockWorkbook.addWorksheet).toHaveBeenCalledWith("Linhas Inválidas");
        const mockWorksheet = mockWorkbook.addWorksheet.mock.results[0].value;
        expect(mockWorksheet.columns).toEqual([
            { header: "document", key: "document" },
            { header: "debt", key: "debt" },
        ]);
        expect(mockWorksheet.addRow).toHaveBeenCalledTimes(2);
        expect(mockWorksheet.addRow).toHaveBeenCalledWith({
            document: "doc1",
            debt: "100",
        });
        expect(mockWorksheet.addRow).toHaveBeenCalledWith({
            document: "doc2",
            debt: "200",
        });

        expect(mockWorkbook.xlsx.writeBuffer).toHaveBeenCalledTimes(1);
        expect(linkMock.href).toContain("blob:");
        expect(linkMock.download).toBe("test-file-invalid.xlsx");
        expect(linkMock.dispatchEvent).toHaveBeenCalledWith(
            expect.any(MouseEvent)
        );
        expect(linkMock.remove).toHaveBeenCalledTimes(1);

        createElementSpy.mockRestore();
        jest.useRealTimers();
    });
});