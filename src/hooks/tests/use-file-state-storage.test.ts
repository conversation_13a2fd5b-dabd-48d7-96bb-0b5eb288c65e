import { renderHook } from '@testing-library/react';
import { useFileStateStorage } from '../use-file-state-storage';
import { fileToBase64, base64ToFile } from '@/utils/file';

jest.mock('@/utils/file', () => ({
  fileToBase64: jest.fn(),
  base64ToFile: jest.fn()
}));

const localStorageMock = (() => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value;
    }),
    clear: () => {
      store = {};
    }
  };
})();
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('useFileStateStorage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.clear();
  });

  describe('setFile', () => {
    it('should convert file to base64 and store in localStorage', async () => {
      const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
      const key = 'testKey';
      const type = 'text/plain';
      const mockBase64 = 'base64encodedstring';

      (fileToBase64 as jest.Mock).mockResolvedValue(mockBase64);

      const { result } = renderHook(() => useFileStateStorage());

      await result.current.setFile({ file, key, type });

      expect(fileToBase64).toHaveBeenCalledWith(file);

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        key,
        JSON.stringify({
          fileBase64: mockBase64,
          type,
          fileName: file.name
        })
      );
    });
  });

  describe('getFile', () => {
    it('should retrieve file from localStorage and convert from base64', async () => {
      const key = 'testKey';
      const mockBase64 = 'base64encodedstring';
      const fileName = 'test.txt';
      const type = 'text/plain';
      const mockFile = new File(['test content'], fileName, { type });

      const mockStorage = JSON.stringify({
        fileBase64: mockBase64,
        type,
        fileName
      });
      localStorageMock.getItem.mockReturnValue(mockStorage);

      (base64ToFile as jest.Mock).mockResolvedValue(mockFile);

      const { result } = renderHook(() => useFileStateStorage());

      const retrievedFile = await result.current.getFile(key);

      expect(localStorageMock.getItem).toHaveBeenCalledWith(key);

      expect(base64ToFile).toHaveBeenCalledWith(mockBase64, fileName, type);

      expect(retrievedFile).toBe(mockFile);
    });

    it('should return null if no item is found in localStorage', async () => {
      const key = 'nonExistentKey';

      localStorageMock.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useFileStateStorage());

      const retrievedFile = await result.current.getFile(key);

      expect(localStorageMock.getItem).toHaveBeenCalledWith(key);

      expect(retrievedFile).toBeNull();

      expect(base64ToFile).not.toHaveBeenCalled();
    });
  });
});
