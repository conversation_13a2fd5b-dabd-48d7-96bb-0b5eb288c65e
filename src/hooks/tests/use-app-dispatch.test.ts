import { renderHook } from '@testing-library/react';
import { useAppDispatch } from '../use-app-dispatch';
import { useDispatch } from 'react-redux';

jest.mock('react-redux', () => ({
  useDispatch: jest.fn()
}));

describe('useAppDispatch', () => {
  it('should call useDispatch from react-redux', () => {
    const mockDispatch = jest.fn();

    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);

    const { result } = renderHook(() => useAppDispatch());

    expect(useDispatch).toHaveBeenCalled();

    expect(result.current).toBe(mockDispatch);
  });
});
