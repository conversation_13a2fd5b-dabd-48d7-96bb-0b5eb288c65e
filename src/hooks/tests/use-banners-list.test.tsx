import { renderHook, act } from "@testing-library/react";
import { listBanners } from "@services";

jest.unmock("../use-banners-list");

import { useBannersList } from "../use-banners-list";

jest.mock("@services", () => ({
  listBanners: jest.fn(),
}));

describe("useBannersList", () => {
  const mockBanners = [
    { id: 1, title: "Banner 1", image: "image1.jpg" },
    { id: 2, title: "Banner 2", image: "image2.jpg" },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (listBanners as jest.Mock).mockResolvedValue({ items: mockBanners });
  });

  it("should initialize with empty banners list and loading state", () => {
    const { result } = renderHook(() => useBannersList());

    expect(result.current.bannersList).toEqual([]);
    expect(result.current.isLoadingBanners).toBe(true);
  });

  it("should fetch banners on mount", async () => {
    (listBanners as jest.Mock).mockImplementation(() => {
      return new Promise((resolve) => {
        setTimeout(() => resolve({ items: mockBanners }), 10);
      });
    });

    const { result } = renderHook(() => useBannersList());

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20));
    });

    expect(listBanners).toHaveBeenCalledTimes(1);
    expect(result.current.bannersList).toEqual(mockBanners);
    expect(result.current.isLoadingBanners).toBe(false);
  });

  it("should provide a function to manually fetch banners", async () => {
    const { result } = renderHook(() => useBannersList());

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20));
    });

    (listBanners as jest.Mock).mockClear();

    await act(async () => {
      await result.current.handleListBanners();
    });

    expect(listBanners).toHaveBeenCalledTimes(1);
    expect(result.current.bannersList).toEqual(mockBanners);
    expect(result.current.isLoadingBanners).toBe(false);
  });

  it("should set loading state during fetch", async () => {
    let resolvePromise;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    (listBanners as jest.Mock).mockReturnValue(promise);

    const { result } = renderHook(() => useBannersList());

    expect(result.current.isLoadingBanners).toBe(true);

    await act(async () => {
      resolvePromise({ items: mockBanners });
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.isLoadingBanners).toBe(false);
    expect(result.current.bannersList).toEqual(mockBanners);
  });
});
