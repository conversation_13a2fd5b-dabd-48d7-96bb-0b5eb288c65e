import { renderHook, act } from "@testing-library/react";
import { listTags } from "@services";

jest.unmock("../use-tags-list");

import { useTagsList } from "../use-tags-list";

jest.mock("@services", () => ({
  listTags: jest.fn(),
}));

describe("useTagsList", () => {
  const mockTags = [
    { id: 1, name: "Tag 1" },
    { id: 2, name: "Tag 2" },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (listTags as jest.Mock).mockResolvedValue(mockTags);
  });

  it("should initialize with empty tags list", () => {
    const { result } = renderHook(() => useTagsList());
    
    expect(result.current.tagsList).toEqual([]);
  });

  it("should fetch tags on mount", async () => {
    (listTags as jest.Mock).mockImplementation(() => {
      return new Promise((resolve) => {
        setTimeout(() => resolve(mockTags), 10);
      });
    });
    
    const { result } = renderHook(() => useTagsList());
    
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20));
    });
    
    expect(listTags).toHaveBeenCalledTimes(1);
    expect(result.current.tagsList).toEqual(mockTags);
  });

  it("should provide a function to manually fetch tags", async () => {
    const { result } = renderHook(() => useTagsList());
    
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 20));
    });
    
    (listTags as jest.Mock).mockClear();
    
    await act(async () => {
      await result.current.handleListTags();
    });
    
    expect(listTags).toHaveBeenCalledTimes(1);
    expect(result.current.tagsList).toEqual(mockTags);
  });
});
