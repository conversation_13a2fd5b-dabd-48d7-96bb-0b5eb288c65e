import { useAccountDetails } from '../use-account-details';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(() => ({ data: { resources: [] } }))
}));

jest.mock('../use-app-dispatch', () => ({
  useAppDispatch: jest.fn(() => jest.fn())
}));

jest.mock('../use-cards-list', () => ({
  useCardsList: jest.fn(() => ({ cardsList: [] }))
}));

jest.mock('../../store/modules/account/get-details/thunks', () => ({
  thunkGetDetailsAccount: jest.fn()
}));

describe('useAccountDetails', () => {
  it('should be defined', () => {
    expect(useAccountDetails).toBeDefined();
    expect(typeof useAccountDetails).toBe('function');
  });
});
