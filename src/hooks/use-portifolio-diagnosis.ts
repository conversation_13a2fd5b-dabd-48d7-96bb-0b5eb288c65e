import { useSelector } from "react-redux";
import { useAppDispatch } from "./use-app-dispatch";
import { RootState } from "@/store";
import {
  actionSearchPortifolioDiagnosisMunicipality,
  actionSearchPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisRequestMethod,
  actionSetPortifolioDiagnosisMunicipalitiesSelected,
  actionSetPortifolioDiagnosisStatesSelected,
  actionSetPortifolioDiagnosisBatchDocumentType,
  actionSetPortifolioDiagnosisBatchDocumentList,
  actionSetPortifolioDiagnosisBatchInvalidDocuments,
  actionResetPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisManualSelectType,
  actionSetPortifolioDiagnosisBatchFileDetails,
  actionSetPortifolioDiagnosisManualSubModulesSelected,
  actionSetPortifolioDiagnosisManualSubModules,
} from "@/store/modules/portifolio-diagnosis/actions";

import { ReportType, RequestMethod, SubModule } from "@types";
import { useRequestForm } from "./use-request-form";
import { thunkCreatePortifolioDiagnosisReportRequest } from "@/store/modules/portifolio-diagnosis/thunks";
import {
  ReportPortifolioDiagnosisBatchDocumentType,
  ReportPortifolioDiagnosisManualSelectType,
} from "@/store/modules/portifolio-diagnosis/types";
import { readCSV } from "@/utils/csv";
import { isCarValid, isCNPJValid, isCPFValid, maskCAR } from "@utils";
import { notification } from "antd";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { useAccountDetails } from "./use-account-details";
import { Resource } from "@constants";
import { useFileStateStorage } from "./use-file-state-storage";

export const usePortifolioDiagnosis = () => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { handleCloseRequestForm } = useRequestForm();
  const { getFile, setFile } = useFileStateStorage();
  const { account } = useAccountDetails();
  const [isManualFormEnabled, setIsManualFormEnabled] = useState(false);

  const { data, loading } = useSelector(
    (state: RootState) => state.portifolioDiagnosis
  );

  const handleClickRemoveMunicipality = (ibgeCode: number) => {
    dispatch(
      actionSetPortifolioDiagnosisMunicipalitiesSelected(
        data.manual.municipaliciesSelected.filter(
          (item) => item.ibgeCode !== ibgeCode
        )
      )
    );
  };

  const handleSetSelectType = (
    selectType: ReportPortifolioDiagnosisManualSelectType
  ) => {
    dispatch(actionSetPortifolioDiagnosisManualSelectType(selectType));

    if (selectType === ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY) {
      dispatch(actionSetPortifolioDiagnosisStatesSelected([]));
      dispatch(actionSearchPortifolioDiagnosisState(""));
      return;
    }
    if (selectType === ReportPortifolioDiagnosisManualSelectType.STATE) {
      dispatch(actionSetPortifolioDiagnosisMunicipalitiesSelected([]));
      dispatch(actionSearchPortifolioDiagnosisMunicipality(""));
      return;
    }
  };

  const handleChangeMunicipality = (payload: { ibgeCode: number }) => {
    const municipality = data.manual.municipalities.find(
      (item) => item.ibge_code === Number(payload.ibgeCode)
    );

    if (!municipality) return;
    const alreadyExists = data.manual.municipaliciesSelected.find(
      ({ ibgeCode }) => ibgeCode === municipality.ibge_code
    );
    if (alreadyExists) return;
    dispatch(actionSearchPortifolioDiagnosisMunicipality(""));

    dispatch(
      actionSetPortifolioDiagnosisMunicipalitiesSelected([
        ...data.manual.municipaliciesSelected,
        {
          ibgeCode: municipality.ibge_code,
          name: municipality.name,
          uf: municipality.uf,
        },
      ])
    );
  };

  const handleSetRequestMethod = (requestMethod: RequestMethod) => {
    dispatch(actionSetPortifolioDiagnosisRequestMethod(requestMethod));
  };

  const handleSearchMunicipality = (search: string) => {
    dispatch(actionSearchPortifolioDiagnosisMunicipality(search));
  };
  const handleSearchState = (search: string) => {
    dispatch(actionSearchPortifolioDiagnosisState(search));
  };

  const handleClickRemoveState = (uf: string) => {
    dispatch(
      actionSetPortifolioDiagnosisStatesSelected(
        data.manual.statesSelected.filter((item) => item.uf !== uf)
      )
    );
  };

  const handleSetBatchDocumentType = (
    batchDocumentType: ReportPortifolioDiagnosisBatchDocumentType
  ) => {
    dispatch(actionSetPortifolioDiagnosisBatchDocumentType(batchDocumentType));
    dispatch(actionSetPortifolioDiagnosisBatchInvalidDocuments([]));
    dispatch(actionSetPortifolioDiagnosisBatchDocumentList([]));
  };

  const handleChangeSelectState = (uf: string) => {
    const state = data.manual.states.find((item) => item.uf === uf);
    if (!state) return;
    const alreadyExists = data.manual.statesSelected.find(
      ({ uf }) => uf == state.uf
    );
    if (alreadyExists) return;
    dispatch(actionSetPortifolioDiagnosisStatesSelected([state]));
  };

  const handleUploadBatchFile = async (file: File) => {
    const keyName = "batch_file";
    await setFile({ file, key: keyName, type: file.type });
    dispatch(
      actionSetPortifolioDiagnosisBatchFileDetails({
        name: file.name,
        type: file.type,
        keyName,
      })
    );
    if (
      data.batch.documentType === ReportPortifolioDiagnosisBatchDocumentType.CAR
    ) {
      const result = await readCSV<{ car: string }>(file);

      if (!result.find((item) => item.car)) {
        notification.error({
          message: t("ERR_INVALID_DOCUMENT"),
          description: t("ERR_INVALID_DOCUMENT_DESC"),
        });
        return;
      }
      const invalidDocuments = result
        .filter(({ car }) => !isCarValid(maskCAR(car)))
        .map(({ car }) => car);
      const validDocuments = result
        .filter(({ car }) => isCarValid(maskCAR(car)))
        .map(({ car }) => car);

      dispatch(actionSetPortifolioDiagnosisBatchDocumentList(validDocuments));
      dispatch(
        actionSetPortifolioDiagnosisBatchInvalidDocuments(invalidDocuments)
      );
    }
    if (
      data.batch.documentType ===
      ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ
    ) {
      const result = await readCSV<{ documento: string }>(file);

      if (!result.find((item) => item.documento)) {
        notification.error({
          message: t("ERR_INVALID_DOCUMENT"),
          description: t("ERR_INVALID_DOCUMENT_DESC"),
        });
        return;
      }

      const mapped = result.map(({ documento }) => ({ document: documento }));

      const invalidDocuments = mapped
        .filter(
          ({ document }) => !isCNPJValid(document) && !isCPFValid(document)
        )
        .map(({ document }) => document);

      const validDocuments = mapped
        .filter(({ document }) => isCNPJValid(document) || isCPFValid(document))
        .map(({ document }) => document);

      dispatch(actionSetPortifolioDiagnosisBatchDocumentList(validDocuments));
      dispatch(
        actionSetPortifolioDiagnosisBatchInvalidDocuments(invalidDocuments)
      );
    }
  };

  const handleChangeManualSubModules = (subModulesTypes: Array<string>) => {
    const subModulesParsed = Array.isArray(subModulesTypes)
      ? subModulesTypes
      : [subModulesTypes];

    const mapped = account.sub_modules.filter((item) =>
      subModulesParsed.find((subModule) => item.report_type === subModule)
    );

    dispatch(actionSetPortifolioDiagnosisManualSubModulesSelected(mapped));
  };

  const handleClickRequestReport = async (
    reportType: ReportType.PORTFOLIO_DIAGNOSIS
  ) => {
    const { requestMethod } = data;

    if (requestMethod === RequestMethod.BATCH) {
      const formData = new FormData();
      const subModules = data.batch.subModulesAvailable
        .map((item) => item.report_type)
        .join(",");

      const file = await getFile(data.batch.fileDetails.keyName);

      formData.append("file", file);
      formData.append("sub_modules", subModules);

      await dispatch(
        thunkCreatePortifolioDiagnosisReportRequest({
          reportType,
          form: formData,
        })
      );
      notification.success({
        message: t("REPORT_REQUESTED_SUCCESSFULLY"),
      });
      handleResetState();
      handleCloseRequestForm();
      return;
    }

    if (requestMethod === RequestMethod.MANUAL) {
      const { municipaliciesSelected, statesSelected, selectType } =
        data.manual;

      const dict: Record<ReportPortifolioDiagnosisManualSelectType, string> = {
        [ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY]:
          municipaliciesSelected
            .map(({ ibgeCode }) => `${ibgeCode}`)
            .join("\n"),

        [ReportPortifolioDiagnosisManualSelectType.STATE]: statesSelected
          .map(({ uf }) => uf)
          .join("\n"),
      };
      const csvColumnDict = {
        [ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY]:
          "municipalities",
        [ReportPortifolioDiagnosisManualSelectType.STATE]: "state",
      };

      const csvData = dict[selectType];

      const file = new File(
        [`${csvColumnDict[selectType]}\n${csvData}`],
        "portifolio_diagnosis.csv",
        {
          type: "text/csv",
        }
      );

      const formData = new FormData();
      formData.append("file", file);

      if (!data.manual.subModulesSelected.length) {
        notification.error({
          message: t("ADD_ONE_MODULE_IS_MANDATORY"),
        });
        return;
      }

      const subModules = data.manual.subModulesSelected
        .map((item) => item.report_type)
        .join(",");

      formData.append("sub_modules", subModules);

      await dispatch(
        thunkCreatePortifolioDiagnosisReportRequest({
          reportType,
          form: formData,
        })
      );

      notification.success({
        message: t("REPORT_REQUESTED_SUCCESSFULLY"),
      });
      handleResetState();
      handleCloseRequestForm();
      return;
    }
  };

  const handleResetState = () => {
    dispatch(actionResetPortifolioDiagnosisState());
  };

  const handleClickRemoveBatchFile = () => {
    dispatch(
      actionSetPortifolioDiagnosisBatchFileDetails({
        name: "",
        type: "",
        keyName: "",
      })
    );
    dispatch(actionSetPortifolioDiagnosisBatchDocumentList([]));
    dispatch(actionSetPortifolioDiagnosisBatchInvalidDocuments([]));
  };

  const handleClickCancel = () => {
    handleResetState();
    handleCloseRequestForm();
  };

  useEffect(() => {
    dispatch(actionSetPortifolioDiagnosisManualSubModules(account.sub_modules));
  }, [account.sub_modules]);

  useEffect(() => {
    if (
      account.resources.includes(
        Resource.PortfolioDiagnosisCreditEnableFormManual
      )
    ) {
      setIsManualFormEnabled(true);
    }
  }, [account.resources]);

  return {
    data,
    isLoading: loading,
    handleSetSelectType,
    handleSetBatchDocumentType,
    handleChangeMunicipality,
    handleSetRequestMethod,
    handleSearchMunicipality,
    handleClickRemoveMunicipality,
    handleClickRemoveState,
    handleChangeSelectState,
    handleSearchState,
    handleClickRequestReport,
    handleClickCancel,
    handleUploadBatchFile,
    handleResetState,
    handleClickRemoveBatchFile,
    handleChangeManualSubModules,
    isManualFormEnabled,
  };
};
