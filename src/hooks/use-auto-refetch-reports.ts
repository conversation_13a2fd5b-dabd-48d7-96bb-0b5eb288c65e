import { useEffect } from "react";
import { Report, ReportStatus } from "@types";

const STATUS_TO_WATCH = [ReportStatus.PROCESSING, ReportStatus.PENDING];

export const useAutoRefetchReports = (
  fetchFunction: (...any: any) => any,
  reports: Report[],
  intervalInSeconds: number = 30
) => {
  const INTERVAL_IN_MILISECONDS = intervalInSeconds * 1000;

  useEffect(() => {
    let interval: NodeJS.Timeout;
    const shouldAutoRefetch = reports.some(({ status }) => {
      return STATUS_TO_WATCH.includes(status);
    });

    if (shouldAutoRefetch) {
      interval = setInterval(() => {
        fetchFunction();
      }, INTERVAL_IN_MILISECONDS);
    }

    if (!shouldAutoRefetch && interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [reports]);
};
