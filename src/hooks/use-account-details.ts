import { useSelector } from "react-redux";
import { RootState } from "../store";
import { thunkGetDetailsAccount } from "../store/modules/account/get-details/thunks";
import { useAppDispatch } from "./use-app-dispatch";
import { Resource } from "@constants";
import { ReportType } from "@types";
import { useCardsList } from "./use-cards-list";

export const useAccountDetails = () => {
  const dispatch = useAppDispatch();
  const { data: account } = useSelector(
    (state: RootState) => state.getDetailsAccount
  );
  const { cardsList } = useCardsList();

  const handleGetDetailsAccount = () => {
    dispatch(thunkGetDetailsAccount());
  };

  const hasResource = (resource: Resource): boolean => {
    return account.resources.includes(resource);
  };

  const isAbleToRequestReportByReportType = (reportType: ReportType) => {
    if (!account || !cardsList.length) {
      return {
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false,
      };
    }
    const currentCard = cardsList.find((item) => item.type === reportType);

    if (!currentCard) {
      return {
        isAbleToRequestSingleReport: false,
        isAbleToRequestBatchReports: false,
      };
    }

    const credit = account.credit_available[reportType] ?? 0;
    const isAbleToRequestSingleReport = credit >= currentCard.value;
    const isAbleToRequestBatchReports = credit >= currentCard.value;

    return {
      isAbleToRequestSingleReport,
      isAbleToRequestBatchReports,
    };
  };

  return {
    account,
    handleGetDetailsAccount,
    isAbleToRequestReportByReportType,
    hasResource,
  };
};
