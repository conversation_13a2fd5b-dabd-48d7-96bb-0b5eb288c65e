import { getDescriptionGenericError } from "@/components/atoms/notification-description-generic-error";
import { createContact } from "@services";
import { IdentificationType } from "@types";
import { notification } from "antd";
import { ReactNode, useState } from "react";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";

export const useContactForm = ({ onSuccess }) => {
  const { t } = useTranslation();
  const [keySeeder, setKeySeeder] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [value, setValue] = useState([
    {
      key: keySeeder,
      document: "",
      name: "",
      cities: [],
      CAR: [],
      type: IdentificationType.DOCUMENT,
    },
  ]);

  const onAddItem = () => {
    setValue([
      ...value,
      {
        key: keySeeder + 1,
        document: "",
        name: "",
        cities: [],
        CAR: [],
        type: IdentificationType.DOCUMENT,
      },
    ]);
    setKeySeeder(keySeeder + 1);
  };

  const onRemoveItem = (d) => {
    setValue(value.filter((v) => v.key !== d.key));
  };

  const onItemChange = (key: number, data) => {
    setValue(
      value.map((v) => {
        if (v.key == key)
          return {
            key,
            document: data.document,
            name: data.name,
            type: data.identificationType,
            cities: data.cities,
            CAR: data.CAR,
          };
        return v;
      })
    );
  };

  const createContacts = async () => {
    setIsSaving(true);

    const newDocuments = value.map((item) => {
      const document = item.document.replace(/[^0-9]/gi, "");
      let type: string = item.type;

      if (item.type === IdentificationType.DOCUMENT && document.length === 14) {
        type = "cnpj";
      } else if (
        item.type === IdentificationType.DOCUMENT &&
        document.length === 11
      ) {
        type = "cpf";
      }

      return {
        id: uuidv4(),
        type: type,
        typeAcronym: type,
        contactName: item.name,
        nameLabel: "",
        document:
          item.type === IdentificationType.DOCUMENT ? document : item.document,
        cars: item.CAR,
        cities: item.cities,
        inputType: "manual",
        search_property: false,
      };
    });

    const newValues = {
      values: newDocuments,
      crop: "2024",
      consults: JSON.stringify([1]),
      tags: [],
      product_origin: "AGRO_REPORT",
      no_consult: true,
    };

    try {
      await createContact(newValues);
      notification.success({
        message: t("NOTIFICATION_NEW_CONTACT_SUCCESS"),
        description: t("NOTIFICATION_NEW_CONTACT_SUCCESS_DESC"),
      });
      setValue([
        {
          key: keySeeder,
          document: "",
          name: "",
          type: IdentificationType.DOCUMENT,
          cities: [],
          CAR: [],
        },
      ]);
      onSuccess && onSuccess();
    } catch ({
      response: {
        status,
        data: { content, remaining, type },
      },
    }) {
      let message = t("QUERY_NOT_MADE");
      let description: ReactNode = "";

      switch (type) {
        case "NoCreditException": {
          description = "ERR_INSUFFICIENT_CREDITS_QUERY";
          break;
        }
        case "ContactExistsException": {
          description = t("ERR_CONTACT_EXISTS");
          if (remaining > 0) {
            description += `\n ${t("ALERT_IMPORTED_DOCS", { remaining })}`;
            message = t("ALERT_QUERY_MADE_PARTIALLY");
          }

          break;
        }
        case "ContactsExistsException": {
          description = t("ALERT_SOME_CONTACTS_ALREADY_EXISTS");
          for (const [index, existingContact] of content.entries()) {
            description += `${existingContact}${
              content.length - 1 === index ? "." : ", "
            }`;
          }

          if (remaining > 0) {
            description += ` ${t("ALERT_IMPORTED_DOCS", { remaining })}`;
            message = t("QUERY_PARTIALLY_MADE");
          }

          break;
        }
        case "ContactRestrictionException": {
          description = t("ERROR_CONTACTS_BLOCKED");
          if (remaining > 0) {
            description += `\n ${t("ALERT_IMPORTED_DOCS", { remaining })}`;
            message = t("QUERY_PARTIALLY_MADE");
          }
          break;
        }
        case "ContactsRestrictionException": {
          description = t("ERROR_CONTACTS_BLOCKED");
          for (const [index, existingContact] of content.entries()) {
            description += `${existingContact}${
              content.length - 1 === index ? "." : ", "
            }`;
          }
          if (remaining > 0) {
            description += `\n ${t("ALERT_IMPORTED_DOCS", { remaining })}`;
            message = t("QUERY_PARTIALLY_MADE");
          }
          break;
        }
        case "TypeError": {
          message = t("ERR_INVALID_DOCUMENT");
          description = "";
        }
        case "ContactsInvalidDocumentsException": {
          message = t("ERR_INVALID_DOCUMENTS");
          description = t("ERR_INVALID_DOCUMENTS_DESC");
        }
        default: {
          description = getDescriptionGenericError(t);
        }
      }

      notification.error({
        message,
        description,
      });
    }

    setIsSaving(false);
  };

  return {
    isSaving,
    onRemoveItem,
    createContacts,
    onAddItem,
    onItemChange,
    value,
  };
};
