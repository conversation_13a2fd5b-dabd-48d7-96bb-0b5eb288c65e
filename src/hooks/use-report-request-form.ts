import {
  addTagToRequest,
  createBatchReportRequest,
  createReportRequest,
  createTags,
} from "@/services";
import {
  CharInput,
  FileRequest,
  ReportType,
  RequestMethod,
  Tag,
} from "@/types";
import { notification } from "antd";
import { useEffect, useMemo, useState } from "react";
import { useRequestFormContext } from "./use-request-form-context";
import { useCardsList } from "./use-cards-list";
import { useAccountDetails } from "./use-account-details";
import { ReportRequestValue } from "@/components/organisms/report-request-form/ReportRequestForm";
import { useAnalytics } from "@/contexts/AnalyticsDataLayerContext";
import { useSocioEnvironmentCriterias } from "./use-socio-environment-criterias";
import { useTagsList } from "./use-tags-list";
import { useReportRequestsList } from "./use-report-requests-list";
import {
  FIELD_TO_CHARINPUT,
  FIELD_VALIDATORS,
  REPORT_CARD_METADATA,
  SPLIT_FIELDS,
} from "@constants";
import { getDescriptionGenericError } from "@/components/atoms/notification-description-generic-error";
import { useTranslation } from "react-i18next";
import { PLANNER_QUERIES } from "@constants/socio-environment-criteria";
import { Property } from "@/types/property";

export const useReportRequestForm = () => {
  const { t } = useTranslation();
  const [selectedTags, setSelectedTags] = useState([]);
  const [requestMethod, setRequestMethod] = useState(RequestMethod.MANUAL);
  const [batchRequestFiles, setBatchRequestFiles] = useState<FileRequest[]>([]);
  const [reportType, setReportType] = useState<ReportType>(undefined);
  const [subareas, setSubareas] = useState<
    { coordinates: any; name: string; area_name: string }[]
  >([]);
  const [properties, setProperties] = useState<Property[]>([]);
  const [alertCredits, setAlertCredits] = useState(false);
  const [signature, setSignature] = useState<boolean>(false);
  const [isSignatureModalOpen, setIsSignatureModalOpen] =
    useState<boolean>(false);
  const [isBacenGeometryModalOpen, setIsBacenGeometryModalOpen] =
    useState<boolean>(false);
  const [isSavingBacenGeometry, setIsSavingBacenGeometry] =
    useState<boolean>(false);

  const { handleGetDetailsAccount, hasResource } = useAccountDetails();
  const { isRequestFormOpen, closeRequestForm, reportMetadata } =
    useRequestFormContext();
  const { cardsList } = useCardsList();
  const [keySeeder, setKeySeeder] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [value, setValue] = useState<ReportRequestValue[]>([
    {
      key: keySeeder,
      reportType: ReportType.VALUATION,
      contact: undefined,
      observation: "",
      property: undefined,
    },
  ]);

  const { handleListTags, tagsList } = useTagsList();
  const { handleListReportsRequests, pagination } = useReportRequestsList();
  const { deforestationCriterias, socioEnvironmentalCriterias } =
    useSocioEnvironmentCriterias();

  const hasSignatureResource = hasResource(
    reportMetadata?.hasSignatureResource
  );
  const noSignatureResource = hasResource(reportMetadata?.noSignatureResource);
  const { sendPageViewEvent } = useAnalytics();
  const { isAbleToRequestReportByReportType, account } = useAccountDetails();

  const REQUEST_METHODS = [
    { label: t("MANUALLY"), value: RequestMethod.MANUAL },
    { label: t("BATCH"), value: RequestMethod.BATCH },
  ];

  const isDisabledButtonRequestReport: boolean = useMemo(() => {
    if (requestMethod !== RequestMethod.BATCH) return false;
    const { isAbleToRequestBatchReports } =
      isAbleToRequestReportByReportType(reportType);

    const disableRequestBatchButton = isAbleToRequestBatchReports === false;
    return disableRequestBatchButton;
  }, [requestMethod, account, cardsList]);

  function isItemInList(item, list): boolean {
    const itemStr = JSON.stringify({ ...item, key: 0 });
    return list.some((x) => JSON.stringify({ ...x, key: 0 }) == itemStr);
  }

  const onAddItem = () => {
    if (!value[value.length - 1].contact || !value[value.length - 1].property) {
      notification.error({
        message: t("ITEM_NOT_ADDED"),
        description: t("SELECT_FARMER_AND_PROPERTY"),
      });
      setIsSaving(false);
      return;
    }
    if (!isReportValid(value[0])) {
      notification.error({
        message: t("ITEM_NOT_ADDED"),
        description: t("FILL_EVERY_FIELD_CORRECTLY"),
      });
      setIsSaving(false);
      return;
    }
    if (isItemInList(value[0], value.slice(1))) {
      notification.error({
        message: t("ITEM_NOT_ADDED"),
        description: t("ITEM_ALREADY_EXISTS"),
      });
      setIsSaving(false);
      return;
    }
    // validateCredits();
    setValue([
      ...value,
      {
        key: keySeeder + 1,
        reportType: reportType,
        contact: undefined,
        observation: "",
        property: undefined,
      },
    ]);
    setKeySeeder(keySeeder + 1);
  };

  const onRemoveItem = (d) => {
    setValue(value.filter((v) => v.key !== d.key));
  };

  const onFormChange = (
    key: ReportRequestValue["key"],
    data: ReportRequestValue
  ) => {
    const mapped = value.map((reportRequest) => {
      if (reportRequest.key == key)
        return {
          key,
          ...data,
        };
      return reportRequest;
    });

    setValue(mapped);
  };

  const requestReports = async () => {
    setIsSaving(true);

    let reportRequest;
    if (requestMethod == RequestMethod.MANUAL) {
      reportRequest = await requestManual();
    } else {
      reportRequest = await requestBatch();
    }

    if (!reportRequest) {
      setIsSaving(false);
      return;
    }

    // Add tags to the report request:
    if (reportRequest && selectedTags.length) {
      const existingSelectedTags = tagsList.filter((t) =>
        selectedTags.includes(t.name)
      );
      const newTags = selectedTags.filter(
        (t) => !tagsList.find((tag) => tag.name == t)
      );
      let currentTags: Tag[] = existingSelectedTags;
      if (newTags.length) {
        const createdTags = await createTags(newTags);
        currentTags = [...existingSelectedTags, ...createdTags];
        handleListTags();
      }
      let addTagRequests;
      if (Array.isArray(reportRequest)) {
        addTagRequests = reportRequest
          .map((request) => {
            return currentTags.map((tag) =>
              addTagToRequest(request.id, tag.id)
            );
          })
          .flat();
      } else {
        addTagRequests = currentTags.map((tag) =>
          addTagToRequest(reportRequest.id, tag.id)
        );
      }
      await Promise.all(addTagRequests);
    }

    setIsSaving(false);
    handleClose();
    handleListReportsRequests({
      offset: pagination.offset,
      limit: pagination.limit,
    });

    sendPageViewEvent({
      pageName: `AG:LG:AgroReport:Relatorio:${reportMetadata.type}`,
      ambiente: "LG",
      siteSection: "Agro Report",
      url: location.href,
    });
  };

  async function requestBatch() {
    if (!batchRequestFiles.length) {
      notification.error({
        message: t("ATTACH_AT_LEAST_ONE_FILE"),
      });
      return;
    }

    try {
      const response = await Promise.all(
        batchRequestFiles.map((fileRequest) =>
          createBatchReportRequest(fileRequest.file, reportType, signature)
        )
      );
      handleGetDetailsAccount();
      notification.success({
        message:
          batchRequestFiles.length > 1
            ? t("REPORTS_REQUESTED_SUCCESSFULLY")
            : t("REPORT_REQUESTED_SUCCESSFULLY"),
      });
      return response;
    } catch ({ code, response }) {
      let description = getDescriptionGenericError(t);
      if (code == "ERR_BAD_REQUEST") {
        description = response.data["detail"];
      }
      notification.error({
        message: t("REPORTS_NOT_REQUESTED"),
        description,
      });
    }
  }

  function getDocumentCharInputs(d): CharInput[] {
    if (!d.contact) return [];
    return [
      {
        label: d.contact.label.toUpperCase(),
        value: d.contact.value,
        is_parent: true,
      },
      {
        label: "CONTACT_NAME",
        value: d.contact.name,
      },
    ];
  }

  // Campos adicionais que nao sao comuns entre a maioria dos relatorios:
  function getDataCharInputs(d): CharInput[] {
    if (!d.data || !reportMetadata.hasMetadata) return [];

    return Object.keys(d.data)
      .filter((key) => d.data[key])
      .map((key) => {
        // Campos como "municipality" são divididos em
        // varios itens com a mesma chave:
        if (SPLIT_FIELDS.includes(key)) {
          const isListofList: boolean = d.data?.queries?.some(
            (item: Record<string, any> | Record<string, any>[]) =>
              Array.isArray(item)
          );

          if (isListofList) {
            return d.data[key];
          }
          return d.data[key].map((x) => {
            return [
              {
                label: FIELD_TO_CHARINPUT[key],
                value: String(x),
                is_parent: true,
              },
            ];
          });
        }
        return [
          [
            {
              label: FIELD_TO_CHARINPUT[key],
              value: String(d.data[key]),
              is_parent: true,
            },
          ],
        ];
      })
      .flat();
  }

  async function requestManual() {
    const validReports = getValidReports();
    const validForms = validReports
      .map((d) => {
        const propertyName = d.property.area_name || `${d.property.id}`;
        const dataCharInput = getDataCharInputs(d);

        const report = {
          report_type: d.reportType,
          observation: d.observation,
          signature: signature,
          charinputs: [
            getDocumentCharInputs(d),
            [
              {
                label: "PROPERTY_NAME",
                value: propertyName,
                is_parent: true,
              },
            ],
            // Campos adicionais alem de cpf/cnpj e propriedade
            // ficam no campo "data"
            ...dataCharInput,
          ],
          geominputs: [
            {
              label: "PROPERTY",
              name: propertyName,
              value: {
                type: "Feature",
                geometry: d.property?.area_coordinates,
                properties: {},
              },
            },
            ...(d.subareas || []).map((subarea) => ({
              label: "FIELD",
              name: subarea.name,
              value: {
                type: "Feature",
                geometry: subarea.coordinates,
                properties: {},
              },
            })),
          ],
        };
        if (
          d.reportType == ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER
        ) {
          report.charinputs = addQueries(report.charinputs as CharInput[][]);
        }
        if (d.property.origin == "CAR") {
          report.charinputs.push([
            {
              label: "CAR",
              value: d.property.external_code,
              is_parent: true,
            },
          ]);
        }
        return report;
      })
      .reduce((prev, curr) => {
        // Evita duplicados
        if (!isItemInList(curr, prev)) {
          prev = [...prev, curr];
        }
        return prev;
      }, []);

    if (!validForms.length) {
      notification.error({
        message: t("REPORTS_NOT_REQUESTED"),
        description: t("FILL_FIELDS_TO_REQUEST"),
      });
      return;
    }

    const socioEnvironmentQueryValues = socioEnvironmentalCriterias
      .map((item) => item.value)
      .concat(deforestationCriterias.map((item) => item.value));

    const shouldValidateList = [
      ReportType.SOCIOENVIRONMENT_COMPLIANCE,
      ReportType.DETAILED_ANALYSIS_DEFORESTATION,
    ];

    for (const report of validForms) {
      if (shouldValidateList.includes(report.report_type)) {
        const charInputs = report.charinputs as Array<
          Array<{ value: string; label: string }>
        >;

        let includeSomeCriteria: boolean = false;

        charInputs.forEach((item) => {
          item.forEach(({ value }) => {
            if (socioEnvironmentQueryValues.includes(value)) {
              includeSomeCriteria = true;
            }
          });
        });

        if (!includeSomeCriteria) {
          notification.error({
            message: t("MUST_SELECT_SOME_CRITERIA"),
          });

          return;
        }
      }
    }

    let reportRequest;
    try {
      reportRequest = await createReportRequest({ reports: validForms });
      handleGetDetailsAccount();
      setValue([
        {
          key: keySeeder,
          reportType: ReportType.VALUATION,
          contact: undefined,
          observation: "",
          property: undefined,
        },
      ]);
      notification.success({
        message:
          validForms.length > 1
            ? t("REPORTS_REQUESTED_SUCCESSFULLY")
            : t("REPORT_REQUESTED_SUCCESSFULLY"),
      });
    } catch {
      notification.error({
        message:
          validForms.length > 1
            ? t("REPORTS_NOT_REQUESTED")
            : t("REPORT_NOT_REQUESTED"),
        description: getDescriptionGenericError(t),
      });
    }
    return reportRequest;
  }

  function addQueries(charInputs: CharInput[][]): CharInput[][] {
    for (const query of PLANNER_QUERIES) {
      charInputs.push([
        {
          label: "QUERY",
          value: query,
          is_parent: true,
        },
      ]);
    }
    return charInputs;
  }

  function handleRequestReports() {
    setIsSubmitted(true);
    const validForms = getValidReports();
    if (requestMethod == RequestMethod.MANUAL) {
      const errorMessage = value
        .map(
          (data) => reportMetadata.onValidate && reportMetadata.onValidate(data)
        )
        .filter(Boolean);
      if (errorMessage.length) {
        notification.error({
          message: t(errorMessage[0].message),
          description: t(errorMessage[0].description),
        });
        return;
      }

      if (!validForms.length) {
        notification.error({
          message: t("REPORTS_NOT_REQUESTED"),
          description: t("FILL_FIELDS_TO_REQUEST"),
        });
        return;
      }
    }

    if (value.some((x) => x.property?.isInternal)) {
      setIsBacenGeometryModalOpen(true);
      return;
    }

    if (
      reportMetadata.hasSignature &&
      hasSignatureResource &&
      noSignatureResource
    ) {
      setIsSignatureModalOpen(true);
      return;
    }
    requestReports();
  }

  function isFieldValid(field: string, fieldValue): boolean {
    return (
      !FIELD_VALIDATORS[field] ||
      FIELD_VALIDATORS[field].every((v) => {
        return Object.keys(v || {}).every((key) => {
          if (key == "min") return (fieldValue || "").length >= v[key];
          if (key == "eq") return (fieldValue || "").length == v[key];
          return true;
        });
      })
    );
  }

  function isReportValid(reportData: ReportRequestValue): boolean {
    if (!reportData) return false;
    if (!reportData.reportType) return false;
    if (!REPORT_CARD_METADATA[reportData.reportType]) return false;

    const metadata = REPORT_CARD_METADATA[reportData.reportType];

    if (metadata.isDocumentRequired && !reportData.contact) {
      return false;
    }

    const fields = Object.keys(reportData.data || {});
    let requiredFields = metadata.requiredFields || [];
    requiredFields = requiredFields.filter((field) => field != "contact");
    if (
      requiredFields?.length &&
      requiredFields.some(
        (field) =>
          !fields.includes(field) ||
          reportData.data[field] === undefined ||
          reportData.data[field] === null
      )
    ) {
      return false;
    }

    const fieldsAreValid = fields.every((field) => {
      return (
        !FIELD_VALIDATORS[field] ||
        (reportData.data[field] && isFieldValid(field, reportData.data[field]))
      );
    });
    const hasMetadataAndData = reportMetadata.hasMetadata && reportData.data;
    const isValidMetadata =
      (hasMetadataAndData && fieldsAreValid) || !reportMetadata.hasMetadata;
    const isValid = reportData.property && isValidMetadata;

    return isValid;
  }

  function getValidReports() {
    return value.filter((v) => isReportValid(v));
  }

  function handleRefBacenGeometry(newProperty) {
    setProperties([...properties, newProperty]);
  }

  function handleRefBacenSubareas(newSubareas) {
    setSubareas(newSubareas);
  }

  function handleClose() {
    setIsSaving(false);
    setIsSubmitted(false);
    setValue([]);
    setSelectedTags([]);
    setBatchRequestFiles([]);
    setProperties([]);
    setSubareas([]);
    setRequestMethod(RequestMethod.MANUAL);
    closeRequestForm();
  }

  useEffect(() => {
    // Limpa o formulario ao abrir novamente
    if (!isRequestFormOpen) return;
    // validateCredits();
    setValue([
      {
        key: keySeeder,
        reportType: ReportType.VALUATION,
        contact: undefined,
        observation: "",
        property: undefined,
      },
    ]);
    setBatchRequestFiles([]);
    setSignature(hasSignatureResource && !noSignatureResource);
  }, [isRequestFormOpen]);

  useEffect(() => {
    if (reportMetadata.batchRequestOnly) {
      setRequestMethod(RequestMethod.BATCH);
    }
    setReportType(reportMetadata.type);
  }, [reportMetadata]);

  return {
    selectedTags,
    requestMethod,
    batchRequestFiles,
    subareas,
    properties,
    alertCredits,
    signature,
    isSaving,
    isSubmitted,
    isBacenGeometryModalOpen,
    handleRefBacenGeometry,
    handleRefBacenSubareas,
    handleRequestReports,
    reportMetadata,
    REQUEST_METHODS,
    isSavingBacenGeometry,
    isDisabledButtonRequestReport,
    isSignatureModalOpen,

    setAlertCredits,
    setRequestMethod,
    setBatchRequestFiles,
    setSignature,
    setIsSignatureModalOpen,
    setIsBacenGeometryModalOpen,
    setIsSavingBacenGeometry,
    onAddItem,
    onFormChange,
    onRemoveItem,
    value,
    setProperties,
    setSubareas,
    setSelectedTags,
    tagsList,
    handleClose,
    getValidReports,
    requestReports,
    hasSignatureResource,
    noSignatureResource,
    setReportType,
  };
};
