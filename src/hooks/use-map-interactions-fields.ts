import { useState, useEffect, useRef } from "react";
import { notification } from "antd";
import { useTranslation } from "react-i18next";
import { Map, MapBrowserEvent } from "ol";
import Draw from "ol/interaction/Draw";
import Modify from "ol/interaction/Modify";
import VectorSource from "ol/source/Vector";
import { Feature } from "ol";
import { searchCARByClick } from "@services";
import { 
  geojsonToFeatures, 
  geometryToGeojson, 
  getExtent,
  geojsonToGeometry
} from "@utils";
import { v4 as uuidv4 } from "uuid";
import geojsonArea from "@mapbox/geojson-area";
import { toHectares } from "@utils";
import intersect from "@turf/intersect";
import { Polygon } from "geojson";
import { SUBAREA_STYLE, UNSELECTED_SUBAREA_STYLE } from "@constants";

export enum MapInteraction {
  CLICK = "CLICK",
  DRAW = "DRAW",
}

export interface Field {
  uid: string;
  area: number;
  selected: boolean;
  geometry: any;
  features: Feature[];
  car: string;
  origin: "MANUAL" | "CAR";
}

interface UseMapInteractionsProps {
  source: VectorSource;
  initialGeometrySource: VectorSource;
  initialProperty: any;
  toIntersect: boolean;
  setFields: (fields: Field[]) => void;
  fields: Field[];
}

export function useMapInteractions({ 
  source,
  initialGeometrySource,
  initialProperty,
  toIntersect,
  setFields,
  fields
}: UseMapInteractionsProps) {
  const { t } = useTranslation();
  const [map, setMap] = useState<Map | undefined>(undefined);
  const [interaction, setInteraction] = useState<MapInteraction>(
    MapInteraction.CLICK
  );
  const [drawInteraction, setDrawInteraction] = useState<Draw | undefined>(
    undefined
  );
  const [modifyInteraction, setModifyInteraction] = useState<
    Modify | undefined
  >(undefined);
  
  const fieldsRef = useRef<Field[]>();
  fieldsRef.current = fields;
  
  const selectedPropertyRef = useRef<
    any & { area_coordinates: any }
  >();
  selectedPropertyRef.current = initialProperty;

  useEffect(() => {
    return () => {
      if (map) {
        if (drawInteraction) map.removeInteraction(drawInteraction);
        if (modifyInteraction) map.removeInteraction(modifyInteraction);
      }
    };
  }, [map, drawInteraction, modifyInteraction]);

  useEffect(() => {
    initialGeometrySource.clear();
    if (initialProperty && map && initialProperty.area_coordinates) {
      const features = geojsonToFeatures(initialProperty.area_coordinates);
      const extent = getExtent(features);
      initialGeometrySource.addFeatures(features);
      map.getView().fit(extent, { padding: [10, 10, 10, 10] });
    }
  }, [map, initialProperty, initialGeometrySource]);

  async function handleClickCAR(
    event: MapBrowserEvent<MouseEvent>,
    mapRef: Map
  ) {
    if (interaction !== MapInteraction.CLICK) return;
    
    try {
      const response = await searchCARByClick(event.coordinate);
      if (!response.length || !response[0]["geom"]) {
        notification.error({
          message: t("PROPERTY_NOT_FOUND"),
          description: t("PROPERTY_NOT_FOUND_DESC"),
        });
        return;
      }
      let geometry = JSON.parse(response[0].geom);
      const area = toHectares(geojsonArea.geometry(geometry));
      const features = geojsonToFeatures(geometry);
      features.forEach((feature) => {
        feature.set("highlight", false);
        feature.set("selected", false);
      });

      if (initialProperty) {
        const intersection = intersect(
          geometry,
          selectedPropertyRef.current.area_coordinates
        );
        if (!intersection || getArea(intersection.geometry) * 0.0001 < 1) {
          notification.error({
            message: t("ALERT_SUBAREA_NOT_IN_PROPERTY"),
          });
          source.clear();
          return;
        }

        if (toIntersect) {
          geometry = intersection.geometry;
        }
      }

      setFields([
        {
          uid: uuidv4(),
          area,
          features,
          selected: true,
          car: response[0].cod_imovel,
          geometry,
          origin: "CAR",
        },
      ]);
      source.clear();
      source.addFeatures(features);
      const extent = getExtent(features);
      mapRef.getView().fit(extent, { padding: [25, 25, 25, 25] });
    } catch {
      notification.warn({ message: t("CAR_NOT_FOUND") });
    }
  }

  async function handleClickSelectField(
    event: MapBrowserEvent<MouseEvent>,
    mapRef: Map
  ) {
    mapRef.forEachFeatureAtPixel(event.pixel, function (feature: Feature) {
      if (feature.get("TYPE") !== "FIELD") return;
      const fieldIndex = fieldsRef.current.findIndex(
        (p) => p.uid === feature.get("UID")
      );
      if (fieldIndex < 0) return;

      if (!fieldsRef.current[fieldIndex].selected) {
        feature.setStyle(SUBAREA_STYLE(feature));
      } else {
        feature.setStyle(UNSELECTED_SUBAREA_STYLE);
      }

      const updatedFields = [...fieldsRef.current];
      updatedFields.splice(fieldIndex, 1, {
        ...fieldsRef.current[fieldIndex],
        selected: !fieldsRef.current[fieldIndex].selected,
      });

      setFields(updatedFields);
    });
  }

  function addDrawInteraction() {
    if (!map) return;
    const draw = new Draw({
      source,
      type: "Polygon",
      stopClick: true,
    });
    map.addInteraction(draw);
    setDrawInteraction(draw);
    addModifyInteraction();
    draw.on("drawend", ({ feature }: { feature: Feature }) => {
      const geom = feature.getGeometry();
      let geometry = geometryToGeojson(geom);
      const area = toHectares(geojsonArea.geometry(geometry));

      const intersection = intersect(
        geometry as Polygon,
        selectedPropertyRef.current.area_coordinates
      );
      if (!intersection) {
        notification.error({
          message: t("ALERT_SUBAREA_NOT_IN_PROPERTY"),
        });
        source.clear();
        return;
      }

      if (toIntersect) geometry = intersection.geometry;

      feature.setGeometry(geojsonToGeometry(geometry));
      feature.set("highlight", false);
      feature.set("selected", false);

      setFields([
        {
          uid: uuidv4(),
          area,
          features: [feature],
          geometry: geometry as Polygon,
          selected: true,
          car: "",
          origin: "MANUAL",
        },
      ]);
      map.removeInteraction(draw);
    });
  }

  function addModifyInteraction() {
    if (!map) return;
    const newModifyInteraction = new Modify({ source, pixelTolerance: 30 });
    map.addInteraction(newModifyInteraction);
    setModifyInteraction(newModifyInteraction);
  }

  function changeInteraction(newInteraction: MapInteraction) {
    setInteraction(newInteraction);
    setFields([]);
    source.clear();

    if (newInteraction === MapInteraction.DRAW) {
      addDrawInteraction();
    } else {
      if (map) {
        if (drawInteraction) map.removeInteraction(drawInteraction);
        if (modifyInteraction) map.removeInteraction(modifyInteraction);
      }
    }
  }

  function updateFieldsOnMap(newFields: Field[]) {
    const features = newFields.map((field) => field.features).flat();
    source.clear();
    if (!features.length) return;
    source.addFeatures(features);
    if (map) {
      const extent = getExtent(features);
      map.getView().fit(extent, { padding: [25, 25, 25, 25] });
    }
  }

  function setActiveFields(
    newFields: Field[],
    fitToView: boolean = false
  ) {
    const features = newFields.map((p) => p.features).flat();
    source.clear();
    if (!features.length) return;
    source.addFeatures(features);
    const extent = getExtent(features);
    fitToView && map && map.getView().fit(extent, { padding: [25, 25, 25, 25] });
  }

  function handleMapClick(
    event: MapBrowserEvent<MouseEvent>,
    mapRef: Map,
    currentTab: number
  ) {
    if (currentTab === 0) { // AUTO_FIELDS
      return handleClickSelectField(event, mapRef);
    }
    if (
      interaction === MapInteraction.CLICK &&
      currentTab === 1 // CLICK_DRAW
    )
      return handleClickCAR(event, mapRef);
  }

  function handleToggleAllAutomaticFields(checked: boolean) {
    fields.forEach((p) => {
      p.features.forEach((feature) =>
        feature.setStyle(
          checked ? SUBAREA_STYLE(feature) : UNSELECTED_SUBAREA_STYLE
        )
      );
    });
    setFields(fields.map((p) => ({ ...p, selected: checked })));
  }

  return {
    map,
    interaction,
    setMap,
    handleMapClick,
    changeInteraction,
    updateFieldsOnMap,
    setActiveFields,
    handleToggleAllAutomaticFields,
  };
}

function getArea(geometry: any): number {
  return geojsonArea.geometry(geometry);
}
