import { base64ToFile, fileToBase64 } from "@/utils/file";

export const useFileStateStorage = () => {
  async function setFile(data: { file: File; type: string; key: string }) {
    const { file, key, type } = data;
    const fileBase64 = await fileToBase64(file);
    const storage = JSON.stringify({ fileBase64, type, fileName: file.name });
    localStorage.setItem(key, storage);
  }

  async function getFile(key: string): Promise<File> {
    const item = localStorage.getItem(key);
    if (!item) return null;
    const { fileBase64, type, fileName } = JSON.parse(item);

    const result = await base64ToFile(fileBase64, fileName, type);
    return result;
  }

  return {
    getFile,
    setFile,
  };
};
