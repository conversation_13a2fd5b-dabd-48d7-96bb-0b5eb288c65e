import ExcelJS from "exceljs";

export const useAgroCreditFormReview = () => {
  async function downloadInvalidDocuments(data: {
    filename: string;
    invalidRows: Array<{ document: string; debt: string }>;
  }) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Linhas Inválidas");
    worksheet.columns = Object.keys(data.invalidRows[0]).map((key) => ({
      header: key,
      key,
    }));
    data.invalidRows.forEach((row) => {
      worksheet.addRow(row);
    });
    const excelBuffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([excelBuffer], {
      type: "application/octet-stream",
    });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `${data.filename}-invalid.xlsx`;
    link.dispatchEvent(
      new MouseEvent(`click`, {
        bubbles: true,
        cancelable: true,
        view: window,
      })
    );
    setTimeout(function () {
      link.remove();
    }, 0);
    return link; // Return the link for testing purposes
  }
  return {
    downloadInvalidDocuments,
  };
};
