import { listTags } from "@services";
import { Tag } from "@types";
import { useEffect, useState } from "react";

export const useTagsList = () => {
  const [tagsList, setTagsList] = useState<Tag[]>([]);

  const handleListTags = async () => {
    const tags = await listTags();
    setTagsList(tags);
  };

  useEffect(() => {
    handleListTags();
  }, []);

  return {
    tagsList,
    handleListTags,
  };
};
