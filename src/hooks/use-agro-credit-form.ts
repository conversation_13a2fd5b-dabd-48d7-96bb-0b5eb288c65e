import { useAnalytics } from "@/contexts/AnalyticsDataLayerContext";
import {
  addTagToRequest,
  createReportExternalAPI,
  createTags,
} from "@services";
import { ReportType, Tag } from "@types";
import { useContext, useState } from "react";
import { useReportRequestsList } from "./use-report-requests-list";
import { useTagsList } from "./use-tags-list";
import { RequestFormContext } from "@contexts";
import { AgroCreditFormState } from "@/components/organisms/agro-credit-form/AgroCreditForm";
import { notification } from "antd";
import { useTranslation } from "react-i18next";
import { getDescriptionGenericError } from "@/components/atoms/notification-description-generic-error";

export const useAgroCreditForm = () => {
  const { t } = useTranslation();
  const { sendPageViewEvent } = useAnalytics();
  const { closeRequestForm } = useContext(RequestFormContext);
  const { handleListReportsRequests } = useReportRequestsList();
  const { tagsList, handleListTags } = useTagsList();

  const [step, setStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [state, setState] = useState({
    filename: "",
    tags: [],
    documents: [],
    invalidRows: [],
    scores: ["CREDIT_RISK"],
  } as AgroCreditFormState);

  function resetState() {
    setState({
      filename: "",
      file: null,
      tags: [],
      documents: [],
      invalidRows: [],
      scores: ["CREDIT_RISK"],
    });
  }

  async function handleSubmit() {
    setIsSubmitting(true);

    try {
      const reportRequest = await createReportExternalAPI(
        state.file,
        ReportType.AGRO_CREDIT
      );

      // Add tags to the report request:
      if (reportRequest && state.tags.length) {
        const existingSelectedTags = tagsList.filter((t) =>
          state.tags.includes(t.name)
        );
        const newTags = state.tags.filter(
          (t) => !tagsList.find((tag) => tag.name == t)
        );
        let currentTags: Tag[] = existingSelectedTags;
        if (newTags.length) {
          const createdTags = await createTags(newTags);
          currentTags = [...existingSelectedTags, ...createdTags];
          handleListTags();
        }
        const addTagRequests = currentTags.map((tag) =>
          addTagToRequest(reportRequest.id, tag.id)
        );
        await Promise.all(addTagRequests);
      }

      notification.success({
        message: t("REPORT_REQUESTED_SUCCESSFULLY"),
      });

      handleListReportsRequests({});

      sendPageViewEvent({
        pageName: `AG:LG:AgroReport:Relatorio:${ReportType.AGRO_CREDIT}`,
        ambiente: "LG",
        siteSection: "Agro Report",
        url: location.href,
      });
      resetState();
      setStep(0);
      closeRequestForm();
    } catch {
      notification.error({
        message: t("REPORT_NOT_REQUESTED"),
        description: getDescriptionGenericError(t),
      });
    }
    setIsSubmitting(false);
  }

  return {
    step,
    setStep,
    handleSubmit,
    resetState,
    setState,
    state,
    isSubmitting,
  };
};
