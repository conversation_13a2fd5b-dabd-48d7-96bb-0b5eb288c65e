import { thunkListReport } from "@/store/modules/report/list/thunks";
import { useAppDispatch } from "./use-app-dispatch";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { thunkListReportRequest } from "@/store/modules/report-requests/list/thunks";

export const useListReports = () => {
  const dispatch = useAppDispatch();

  const {
    data: { items: reports, count: total },
  } = useSelector((state: RootState) => state.listReport);

  async function handleListReports(data: {
    page: number;
    pageSize: number;
    requestId: number;
  }) {
    const { page, pageSize } = data;

    await dispatch(
      thunkListReport({
        limit: pageSize,
        offset: (page - 1) * pageSize,
        report_request_id: data.requestId,
      })
    );

    await dispatch(thunkListReportRequest({}));
  }
  return {
    reports,
    handleListReports,
    total,
  };
};
