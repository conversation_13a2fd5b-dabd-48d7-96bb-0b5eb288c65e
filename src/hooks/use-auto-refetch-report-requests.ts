import { useEffect } from "react";
import { ReportType } from "../types/report-type";

const AutoGeneratedReports = [
  ReportType.SOCIOENVIRONMENT_COMPLIANCE,
  ReportType.SOCIOENVIRONMENT_PROTOCOL,
  ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG,
  ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER,
  ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR,
  ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE,
];

export const useAutoRefetchReportRequests = (
  fetchFunction: (...any: any) => any,
  requests: Record<string, any>[],
  intervalInSeconds: number = 30
) => {
  const INTERVAL_IN_MILISECONDS = intervalInSeconds * 1000;

  useEffect(() => {
    let hasAtLeastOneAutoGeneratedReportPending = false;
    let interval: NodeJS.Timeout;
    requests
      .filter((item) => item.reports_pending > 0 || item.reports_processing > 0)
      .some((report) => {
        const types = report.reports_types;

        if (!types) return;

        if (!Array.isArray(types)) return;

        if (!types.length) return;

        return (hasAtLeastOneAutoGeneratedReportPending = types.some((type) =>
          AutoGeneratedReports.includes(type)
        ));
      });

    if (hasAtLeastOneAutoGeneratedReportPending) {
      interval = setInterval(() => {
        fetchFunction();
      }, INTERVAL_IN_MILISECONDS);
    }

    if (!hasAtLeastOneAutoGeneratedReportPending && interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [requests]);
};
