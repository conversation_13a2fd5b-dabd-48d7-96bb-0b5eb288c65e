import { useState, useMemo } from "react";
import { notification } from "antd";
import { v4 as uuidv4 } from "uuid";
import { createFields } from "@services";
import { useTranslation } from "react-i18next";
import { Field } from "./use-map-interactions-fields";

export enum FieldsTabs {
  AUTO_FIELDS = 0,
  CLICK_DRAW = 1,
  FILE_UPLOAD = 2,
}

interface UseFieldsManagementProps {
  initialProperty: any;
  onSuccess?: () => void;
  onClose?: () => void;
  registedFields?: any[];
}

export function useFieldsManagement({
  initialProperty,
  onSuccess,
  onClose,
  registedFields = [],
}: UseFieldsManagementProps) {
  const { t } = useTranslation();
  const [fields, setFields] = useState<Field[]>([]);
  const [fieldName, setFieldName] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [currentTab, setCurrentTab] = useState<FieldsTabs>(FieldsTabs.AUTO_FIELDS);

  const selectedFields = useMemo(() => {
    return fields.filter((field) => field.selected);
  }, [fields]);

  const totalAreaAutomatedFields = useMemo((): number => {
    return fields.reduce((acc, field) => acc + field.area, 0);
  }, [fields]);

  const selectedAreaAutomatedFields = useMemo((): number => {
    return fields
      .filter((p) => p.selected === true)
      .reduce((acc, field) => acc + field.area || 0, 0);
  }, [fields]);

  function validate(): boolean {
    if (!selectedFields.length) {
      notification.error({
        message: t("SUBAREA_NOT_ADDED"),
        description: t("NO_SUBAREAS_FOUND"),
      });
      return false;
    }

    if (!fieldName.length) {
      notification.error({
        message: t("SUBAREA_NOT_ADDED"),
        description: t("FILL_EVERY_FIELD"),
      });
      return false;
    }

    const fieldNameExists = registedFields.some(
      (field) => field.name === fieldName
    );
    if (fieldNameExists) {
      notification.error({
        message: t("ALERT_SUBAREA_SAME_NAME"),
      });
      return false;
    }

    return true;
  }

  async function saveFields() {
    setIsSubmitted(true);
    const isValid = validate();
    if (!isValid) return;
    setIsSaving(true);

    try {
      await Promise.all(
        selectedFields.map((field, index) =>
          createFields(initialProperty.id, [
            {
              name: [FieldsTabs.AUTO_FIELDS, FieldsTabs.FILE_UPLOAD].includes(
                currentTab
              )
                ? `${fieldName}${index + 1}`
                : fieldName,
              geometry: field.geometry,
            },
          ])
        )
      );
      onSuccess && onSuccess();
      notification.success({
        message: t("SUBAREA_ADDED_SUCCESSFULLY"),
      });
      handleClose();
    } catch {
      notification.error({
        message: t("PROPERTY_NOT_ADDED"),
        description: t("PROPERTY_NOT_ADDED_DESC"),
      });
    }

    setIsSaving(false);
  }

  function handleClose() {
    setFields([]);
    setFieldName("");
    setIsSubmitted(false);
    onClose && onClose();
  }

  function handleAutomaticFields(newFields: any[]) {
    if (!newFields.length) {
      return notification.warn({ message: t("NO_SUBAREAS_IN_PROPERTY") });
    }

    const newFieldsData = newFields.map((field) => {
      const features = field.features || [];
      features.forEach((feature) => {
        feature.set("highlight", false);
        feature.set("selected", false);
        feature.set("UID", String(field.id));
        feature.set("TYPE", "FIELD");
      });

      return {
        uid: String(field.id),
        area: field.area_ha,
        features,
        geometry: field.geometry,
        selected: true,
        car: "",
        origin: "MANUAL",
      };
    }) as Field[];
    
    setFields(newFieldsData);
    return newFieldsData;
  }

  function setNextTab(tabIndex: number) {
    if (tabIndex !== currentTab) {
      setFields([]);
      setCurrentTab(tabIndex);
    }
  }

  return {
    fields,
    fieldName,
    isSubmitted,
    isSaving,
    currentTab,
    selectedFields,
    totalAreaAutomatedFields,
    selectedAreaAutomatedFields,
    setFields,
    setFieldName,
    saveFields,
    handleClose,
    handleAutomaticFields,
    setNextTab,
  };
}
