import { RequestMethod } from "@types";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export const useReportAnalysisDeforestationEUDR = () => {
  const { t } = useTranslation();

  const [languageSelected, setLanguageSelected] = useState("pt_BR");

  const selectLanguagePlaceholder = t("LANGUAGE");

  const REQUEST_METHODS = [
    { label: t("MANUALLY"), value: RequestMethod.MANUAL },
    { label: t("BATCH"), value: RequestMethod.BATCH },
  ];

  const selectLanguageOptions = [
    {
      value: "en",
      label: t("ENGLISH"),
    },
    {
      value: "pt_BR",
      label: t("PORTUGUESE"),
    },
  ];
  const translate = (word: string) => t(word);

  return {
    REQUEST_METHODS,
    selectLanguageOptions,
    selectLanguagePlaceholder,
    languageSelected,
    setLanguageSelected,
    translate,
  };
};
