import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { useAppDispatch } from "./use-app-dispatch";
import {
  actionRenovaBioCertificationResetFileMetadata,
  actionRenovaBioCertificationResetState,
  actionSetCertificationRenovaBioDocumentsData,
  actionSetCertificationRenovaBioFormStep,
  actionSetRenovaBioReportType,
} from "@/store/modules/certification-renova-bio-form/actions";
import { useTranslation } from "react-i18next";
import { isCarValid, maskCAR } from "@/utils/car";
import { useState } from "react";
import { notification } from "antd";
import { ReportType } from "@types";
import Papa from "papaparse";
import { thunkCreateRenovaBioReportRequest } from "@/store/modules/certification-renova-bio-form/thunks";
import { useRequestForm } from "./use-request-form";
import { useTagsList } from "./use-tags-list";
import ExcelJS from "exceljs";

export type HandleDownloadInvalidDocumentsPayload = {
  invalidRows: Array<{ document: string; debt: string }>;
  filename: string;
};

const detectDelimiter = (data: string): string => {
  const delimiters = [",", ";", "\t", "|"];
  let maxCount = 0;
  let detectedDelimiter = ",";

  delimiters.forEach((delimiter) => {
    const count = data.split(delimiter).length;
    if (count > maxCount) {
      maxCount = count;
      detectedDelimiter = delimiter;
    }
  });

  return detectedDelimiter;
};

const readCSV = (data: string) => {
  const delimiter = detectDelimiter(data);
  const result = Papa.parse(data, {
    header: true,
    skipEmptyLines: true,
    delimiter,
  }).data;

  return result;
};

export const useCertificationRenovaBioForm = () => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { handleCloseRequestForm } = useRequestForm();
  const [selectedTags, setSelectedTags] = useState([]);
  const { tagsList } = useTagsList();
  const [isInvalidDocumentsExpanded, setIsInvalidDocumentsExpanded] =
    useState(true);

  const {
    data: { formStep, reportType, ...documentsData },
    loading,
  } = useSelector((state: RootState) => state.certificationRenovaBio);

  const handleToggleInvalidDocumentsExpanded = () => {
    setIsInvalidDocumentsExpanded(!isInvalidDocumentsExpanded);
  };

  const handleDownloadInvalidDocumentsXLS = () => {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Linhas Inválidas");
    worksheet.columns = Object.keys(documentsData.invalidRows[0]).map((key) => ({
      header: key,
      key,
    }));
    documentsData.invalidRows.forEach((row) => {
      worksheet.addRow(row);
    });
    workbook.xlsx.writeBuffer().then((excelBuffer) => {
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `${documentsData.fileMetadata.name}-invalid.xlsx`;
      link.dispatchEvent(
        new MouseEvent(`click`, {
          bubbles: true,
          cancelable: true,
          view: window,
        })
      );
      setTimeout(function () {
        link.remove();
      }, 0);
    });
  };

  function handleUploadFile(file: File) {
    const reader = new FileReader();
    const formData = new FormData();
    formData.append("file", file);

    if (file.size / (1024 * 1000) > 100) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 100 }),
      });
      return;
    }

    reader.onload = ({ target }) => {
      const result: Array<{
        car: string;
        ano_elegibilidade: number;
        cultura: string;
      }> = readCSV(target.result as string) as any;

      const invalidRows = result.filter(({ car }) => !isCarValid(maskCAR(car)));
      const carCodes = result.filter(
        ({ car }) => !invalidRows.find((item) => item.car === car)
      );

      if (!result) {
        notification.error({
          message: t("INVALID_FILE"),
        });
        return;
      }
      if (!carCodes.length) {
        notification.error({
          message: t("NO_VALID_DOCUMENTS_IN_FILE"),
        });
        return;
      }

      dispatch(
        actionSetCertificationRenovaBioDocumentsData({
          formData,
          carCodes,
          fileMetadata: {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
          },
          formStep,
          invalidRows,
          reportType,
        })
      );
    };

    reader.readAsText(file);
  }

  const handleSetFormStep = (step: number) => {
    dispatch(actionSetCertificationRenovaBioFormStep(step));
  };

  const handleSetReportType = (reportType: ReportType) => {
    dispatch(actionSetRenovaBioReportType(reportType));
  };

  const handleClickButtonNext = () => {
    dispatch(actionSetCertificationRenovaBioFormStep(formStep + 1));
  };
  const handleClickButtonCancel = () => {
    dispatch(actionRenovaBioCertificationResetState());
    handleCloseRequestForm();
  };

  const handleSubmit = async () => {
    await dispatch(
      thunkCreateRenovaBioReportRequest({
        formData: documentsData.formData,
        reportType,
        selectedTags,
        tagsList,
      })
    ).then((thunk) => {
      if (thunk.type.includes("fulfilled")) {
        notification.success({ message: t("REPORT_REQUESTED_SUCCESSFULLY") });
        return;
      }
      if (thunk.type.includes("rejected")) {
        notification.error({ message: t("REPORTS_NOT_REQUESTED") });
      }
    });

    handleCloseRequestForm();
    dispatch(actionRenovaBioCertificationResetState());
  };

  const handleRemoveFile = () => {
    dispatch(actionRenovaBioCertificationResetFileMetadata());
  };

  return {
    formStep,
    reportType,
    handleSubmit,
    documentsData,
    handleRemoveFile,
    handleSetFormStep,
    handleUploadFile,
    handleSetReportType,
    handleClickButtonNext,
    handleClickButtonCancel,
    isInvalidDocumentsExpanded,
    handleToggleInvalidDocumentsExpanded,
    handleDownloadInvalidDocumentsXLS,
    isLoading: loading,
    tagsList,
    selectedTags,
    setSelectedTags,
  };
};
