import { useState } from "react";
import { notification } from "antd";
import { useTranslation } from "react-i18next";
import { RcFile } from "antd/lib/upload";
import { UploadProps } from "antd";
import { v4 as uuidv4 } from "uuid";
import { 
  convertKMLToJSON, 
  convertSHPToJSON, 
  geojsonToFeatures, 
  toHectares 
} from "@utils";
import { GeoJSONPolygon } from "ol/format/GeoJSON";
import geojsonArea from "@mapbox/geojson-area";
import { Property } from "./use-property-management";

interface UseFileProcessingProps {
  properties: Property[];
  setProperties: (properties: Property[]) => void;
  updatePropertiesOnMap: (properties: Property[]) => void;
}

export function useFileProcessing({
  properties,
  setProperties,
  updatePropertiesOnMap,
}: UseFileProcessingProps) {
  const { t } = useTranslation();
  const [propertyOptions, setPropertyOptions] = useState<Property[]>([]);

  const uploadProps: UploadProps = {
    name: "file",
    multiple: true,
    showUploadList: false,
    accept: ".kml,.shp",
    beforeUpload: (file) => {
      handleFileUpload(file);
      return false;
    },
  };

  function handleFileUpload(file: RcFile) {
    const reader = new FileReader();
    if (file.size / (1024 * 1000) > 10) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 10 }),
      });
      return;
    }
    
    const extension = file.name.substring(file.name.lastIndexOf(".") + 1);
    reader.onload = async ({ target }) => {
      try {
        if (extension === "shp") {
          return handleShpFile(target);
        }

        if (extension === "kml") {
          return handleKmlFile(target);
        }
      } catch {
        notification.error({
          message: "Upload",
          description: t("ERR_FILE_CORRUPTED"),
        });
      }
    };

    if (extension === "shp") {
      return reader.readAsArrayBuffer(file);
    }
    if (extension === "kml") {
      return reader.readAsText(file);
    }
  }

  async function handleShpFile(target: FileReader) {
    const geometry = await convertSHPToJSON(target.result);
    return handleFileList(geometry as any);
  }

  function handleKmlFile(target: FileReader) {
    const collection = convertKMLToJSON(target.result);
    let polygons: GeoJSONPolygon[];

    if (collection.type === "GeometryCollection") {
      polygons = [
        {
          type: "MultiPolygon" as any,
          coordinates: collection.geometries.map((geo: any) => geo.coordinates),
        },
      ];
    } else {
      polygons = [collection];
    }
    return handleFileList(polygons);
  }

  function handleFileList(polygons: GeoJSONPolygon[]): any[] {
    const newProperties: Property[] = polygons.map((geometry) => {
      const uid = uuidv4();
      const area = toHectares(geojsonArea.geometry(geometry));
      const features = geojsonToFeatures(geometry);
      features.forEach((feature) => {
        feature.set("highlight", false);
        feature.set("selected", false);
      });

      return {
        uid,
        area,
        features,
        geometry,
        car: "",
        origin: "MANUAL",
      };
    });
    
    const updatedPropertyOptions = [...propertyOptions, ...newProperties];
    setPropertyOptions(updatedPropertyOptions);
    
    setProperties([newProperties[0]]);
    newProperties[0].features.forEach((feature) => {
      feature.set("selected", true);
    });
    
    updatePropertiesOnMap([...properties, ...newProperties]);
    return newProperties;
  }

  return {
    propertyOptions,
    setPropertyOptions,
    uploadProps,
    handleFileUpload,
  };
}
