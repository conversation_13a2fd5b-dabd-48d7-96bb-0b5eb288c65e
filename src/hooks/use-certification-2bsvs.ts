import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  Certification2BSvsFormStep,
  Certification2BSvsLanguages,
} from "@/store/modules/certification-2bsvs/types";
import ExcelJS from "exceljs";

import {
  actionResetCertification2BSvsFileMetadata,
  actionResetCertification2BSvsState,
  actionSetCertification2BSvsFileMetadata,
  actionSetCertification2BSvsFileObjectUrl,
  actionSetCertification2BSvsFormStep,
  actionSetCertification2BSvsInvalidCars,
  actionSetCertification2BSvsLanguage,
  actionSetCertification2BSvsValidCars,
} from "@/store/modules/certification-2bsvs/actions";

import { useAppDispatch } from "./use-app-dispatch";
import { useTranslation } from "react-i18next";
import { readCSV } from "@/utils/csv";
import { isCarValid, maskCAR } from "@utils";
import { notification } from "antd";
import { useRequestForm } from "./use-request-form";
import { useState } from "react";
import { thunkCreateCertification2BSvsReportRequest } from "@/store/modules/certification-2bsvs/thunks";

export const useCertification2BSvs = () => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { handleCloseRequestForm } = useRequestForm();
  const { data, error, loading } = useSelector(
    (state: RootState) => state.certification2BSvs
  );

  const [isToggleInvalidDocumentsOpen, setIsToggleInvalidDocumentsOpen] =
    useState(true);

  const CERTIFICATION_2BSVS_LANGUAGES = Object.entries(
    Certification2BSvsLanguages
  ).map(([key, value]) => ({ label: t(key), value }));

  const handleDownloadInvalidDocumentsXLS = () => {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Linhas Inválidas");
    worksheet.columns = [
      {
        header: "CAR Inválido",
        key: "invalidCAR",
      },
    ];
    data.invalidCars.forEach((row) => {
      worksheet.addRow({invalidCAR: row});
    });
    workbook.xlsx.writeBuffer().then((excelBuffer) => {
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `${data.fileMetadata.name}-invalid.xlsx`;
      link.dispatchEvent(
        new MouseEvent(`click`, {
          bubbles: true,
          cancelable: true,
          view: window,
        })
      );
      setTimeout(function () {
        link.remove();
      }, 0);
    });
  };

  function handleUploadFile(fileData: File) {
    const reader = new FileReader();

    const fileUrl = URL.createObjectURL(fileData);

    dispatch(actionSetCertification2BSvsFileObjectUrl(fileUrl));
    if (fileData.size / (1024 * 1000) > 100) {
      notification.warning({
        message: t("FILE_NOT_SENT"),
        description: t("ALERT_FILE_SIZE", { sizeMb: 100 }),
      });
      return;
    }

    reader.onload = async ({}) => {
      const result = await readCSV<{ car: string }>(fileData);

      if (!result.find((item) => item.car)) {
        notification.error({
          message: t("ERR_INVALID_DOCUMENT"),
          description: t("ERR_INVALID_DOCUMENT_DESC"),
        });
        return;
      }
      const invalidDocuments = result
        .map(({ car }) => maskCAR(car))
        .filter((car) => !isCarValid(car));

      const validDocuments = result
        .map(({ car }) => maskCAR(car))
        .filter((car) => isCarValid(car));

      if (!result) {
        notification.error({
          message: t("INVALID_FILE"),
        });
        return;
      }
      if (!validDocuments.length) {
        notification.error({
          message: t("NO_VALID_DOCUMENTS_IN_FILE"),
        });
        return;
      }

      dispatch(actionSetCertification2BSvsValidCars(validDocuments));
      dispatch(actionSetCertification2BSvsInvalidCars(invalidDocuments));

      dispatch(
        actionSetCertification2BSvsFileMetadata({
          name: fileData.name,
          type: fileData.type,
        })
      );
    };

    reader.readAsText(fileData);
  }
  const handleSetFormStep = (formStep: Certification2BSvsFormStep) => {
    dispatch(actionSetCertification2BSvsFormStep(formStep));
  };

  const handleRemoveFile = () => {
    dispatch(actionResetCertification2BSvsFileMetadata());
  };

  const handleSetLanguage = (language: Certification2BSvsLanguages) => {
    dispatch(actionSetCertification2BSvsLanguage(language));
  };

  const handleClickCancel = () => {
    dispatch(
      actionSetCertification2BSvsFormStep(
        Certification2BSvsFormStep.UPLOAD_FILE
      )
    );
    dispatch(actionResetCertification2BSvsFileMetadata());
    dispatch(actionResetCertification2BSvsState());
    handleCloseRequestForm();
  };

  const handleClickRequestReport = async () => {
    const formData = new FormData();
    const blobResponse = await fetch(data.fileObjectUrl);
    const blob = await blobResponse.blob();
    const file = new File([blob], data.fileMetadata.name, {
      type: blob.type,
    });

    formData.append("language", data.language);
    formData.append("file", file);

    await dispatch(
      thunkCreateCertification2BSvsReportRequest({
        formData,
      })
    ).then((thunk) => {
      if (thunk.type.includes("fulfilled")) {
        notification.success({ message: t("REPORT_REQUESTED_SUCCESSFULLY") });
        return;
      }
      if (thunk.type.includes("rejected")) {
        notification.error({ message: t("REPORTS_NOT_REQUESTED") });
      }
    });
    URL.revokeObjectURL(data.fileObjectUrl);

    handleCloseRequestForm();
    dispatch(actionResetCertification2BSvsState());
  };

  return {
    data,
    handleSetFormStep,
    loading,
    handleSetLanguage,
    CERTIFICATION_2BSVS_LANGUAGES,
    handleUploadFile,
    handleRemoveFile,
    handleClickCancel,
    handleClickRequestReport,
    setIsToggleInvalidDocumentsOpen,
    isToggleInvalidDocumentsOpen,
    handleDownloadInvalidDocumentsXLS,
  };
};
