import { useContext } from "react";
import { RequestFormContext } from "@contexts";
import { ReportType } from "@types";
import { Form } from "antd";

export const useRequestForm = () => {
  const { openRequestForm, closeRequestForm } = useContext(RequestFormContext);
  const [form] = Form.useForm();

  const handleOpenRequestForm = (reportType: ReportType) => {
    return openRequestForm(reportType);
  };

  const handleCloseRequestForm = () => {
    closeRequestForm();
  };

  return {
    handleOpenRequestForm,
    handleCloseRequestForm,
    form,
  };
};
