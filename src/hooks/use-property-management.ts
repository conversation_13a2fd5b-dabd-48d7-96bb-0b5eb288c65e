import { useState } from "react";
import { notification } from "antd";
import { v4 as uuidv4 } from "uuid";
import { createProperty } from "@services";
import { toHectares, removeMaskCAR } from "@utils";
import { useTranslation } from "react-i18next";
import { GeoJSONPolygon } from "ol/format/GeoJSON";
import { Feature } from "ol";
import { Polygon } from "geojson";
import geojsonArea from "@mapbox/geojson-area";
import { CAR_CODE_LENGTH } from "@constants/properties";

export interface Property {
  uid: string;
  area: number;
  geometry: GeoJSONPolygon;
  features: Feature[];
  car: string;
  origin: "MANUAL" | "CAR";
}

interface UsePropertyManagementProps {
  contact?: {
    id: number;
    group_id: number;
  };
  onSuccess?: (newProperty: any) => void;
  onClose?: () => void;
}

export function usePropertyManagement({
  contact,
  onSuccess,
  onClose,
}: UsePropertyManagementProps) {
  const { t } = useTranslation();
  const [properties, setProperties] = useState<Property[]>([]);
  const [propertyOptions, setPropertyOptions] = useState<Property[]>([]);
  const [propertyName, setPropertyName] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  function addProperty(property: Property): Property[] {
    let newProperties = [property];
    setProperties(newProperties);
    return newProperties;
  }

  function validate(): boolean {
    if (!properties.length) {
      notification.error({
        message: t("PROPERTY_NOT_ADDED"),
        description: t("NO_PROPERTIES_FOUND"),
      });
      return false;
    }
    return propertyName.length > 0;
  }

  async function saveProperty() {
    if (!propertyName.length) {
      notification.error({
        message: t("PROPERTY_NOT_ADDED"),
        description: t("FILL_EVERY_FIELD"),
      });
      return;
    }

    const newProperty = {
      id: uuidv4(),
      area_name: propertyName,
      area_coordinates: properties[0]?.geometry,
      origin: properties[0]?.origin,
      external_code: properties[0]?.car,
    };
    setIsSubmitted(true);

    if (!contact) {
      onSuccess && onSuccess(newProperty);
      notification.success({
        message: t("PROPERTY_ADDED_SUCCESSFULLY"),
      });
      handleClose();
      return;
    }

    const isValid = validate();
    if (!isValid) return;
    setIsSaving(true);

    const hasCarCode = properties[0]?.car?.length > 0;
    if (
      hasCarCode &&
      removeMaskCAR(properties[0].car).length !== CAR_CODE_LENGTH
    ) {
      notification.error({
        message: t("INVALID_CAR_CODE"),
      });
      setIsSaving(false);
      return;
    }

    try {
      await createProperty(
        contact.id,
        contact.group_id,
        propertyName,
        properties[0].geometry,
        properties[0].car,
        properties[0].origin
      );

      onSuccess && onSuccess(newProperty);
      notification.success({
        message: t("PROPERTY_ADDED_SUCCESSFULLY"),
      });
      handleClose();
    } catch {
      notification.error({
        message: t("PROPERTY_NOT_ADDED"),
        description: t("PROPERTY_NOT_ADDED_DESC"),
      });
    }

    setIsSaving(false);
  }

  function handleClose() {
    setProperties([]);
    setPropertyOptions([]);
    setPropertyName("");
    setIsSubmitted(false);
    onClose && onClose();
  }

  function createPropertyFromGeometry(
    geometry: GeoJSONPolygon | Polygon,
    car: string = "",
    origin: "MANUAL" | "CAR" = "MANUAL"
  ): Property {
    const area = toHectares(geojsonArea.geometry(geometry as any));
    
    return {
      uid: uuidv4(),
      area,
      geometry: geometry as GeoJSONPolygon,
      features: [], // Será preenchido pelo componente que usa este hook
      car,
      origin,
    };
  }

  function updatePropertyOptions(newProperties: Property[]) {
    setPropertyOptions([...propertyOptions, ...newProperties]);
  }

  return {
    properties,
    propertyOptions,
    propertyName,
    isSubmitted,
    isSaving,
    setProperties,
    setPropertyOptions,
    setPropertyName,
    addProperty,
    saveProperty,
    handleClose,
    createPropertyFromGeometry,
    updatePropertyOptions,
  };
}
