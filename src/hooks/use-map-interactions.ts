import { useState, useEffect } from "react";
import { notification } from "antd";
import { useTranslation } from "react-i18next";
import { Map, MapBrowserEvent } from "ol";
import Draw from "ol/interaction/Draw";
import Modify from "ol/interaction/Modify";
import VectorSource from "ol/source/Vector";
import { Feature } from "ol";
import { searchCARByClick } from "@services";
import { 
  geojsonToFeatures, 
  geometryToGeojson, 
  getExtent 
} from "@utils";
import { v4 as uuidv4 } from "uuid";
import { Property } from "./use-property-management";
import geojsonArea from "@mapbox/geojson-area";
import { toHectares } from "@utils";

export enum MapInteraction {
  CLICK = "CLICK",
  DRAW = "DRAW",
}

interface UseMapInteractionsProps {
  addProperty: (property: Property) => Property[];
  source: VectorSource;
}

export function useMapInteractions({ 
  addProperty,
  source
}: UseMapInteractionsProps) {
  const { t } = useTranslation();
  const [map, setMap] = useState<Map | undefined>(undefined);
  const [interaction, setInteraction] = useState<MapInteraction>(
    MapInteraction.CLICK
  );
  const [drawInteraction, setDrawInteraction] = useState<Draw | undefined>(
    undefined
  );
  const [modifyInteraction, setModifyInteraction] = useState<
    Modify | undefined
  >(undefined);

  useEffect(() => {
    return () => {
      if (map) {
        if (drawInteraction) map.removeInteraction(drawInteraction);
        if (modifyInteraction) map.removeInteraction(modifyInteraction);
      }
    };
  }, [map, drawInteraction, modifyInteraction]);

  async function handleMapClick(
    event: MapBrowserEvent<MouseEvent>,
    mapRef: Map
  ) {
    if (interaction !== MapInteraction.CLICK) return;
    
    try {
      const response = await searchCARByClick(event.coordinate);
      if (!response.length || !response[0]["geom"]) {
        notification.error({
          message: t("PROPERTY_NOT_FOUND"),
          description: t("PROPERTY_NOT_FOUND_DESC"),
        });
        return;
      }

      let geometry = JSON.parse(response[0].geom);
      const area = toHectares(geojsonArea.geometry(geometry));
      const features = geojsonToFeatures(geometry);

      addProperty({
        uid: uuidv4(),
        area,
        features,
        geometry,
        car: response[0].cod_imovel,
        origin: "CAR",
      });
      
      source.clear();
      source.addFeatures(features);
      const extent = getExtent(features);
      mapRef.getView().fit(extent, { padding: [25, 25, 25, 25] });
    } catch {
      notification.warn({ message: t("CAR_NOT_FOUND") });
    }
  }

  function addDrawInteraction() {
    if (!map) return;
    
    const draw = new Draw({
      source,
      type: "Polygon",
      stopClick: true,
    });
    
    map.addInteraction(draw);
    setDrawInteraction(draw);
    addModifyInteraction();
    
    draw.on("drawend", ({ feature }: { feature: Feature }) => {
      const geom = feature.getGeometry();
      const geometry = geometryToGeojson(geom);
      const area = toHectares(geojsonArea.geometry(geometry));

      addProperty({
        uid: uuidv4(),
        area,
        features: [feature],
        geometry: geometry as any,
        car: "",
        origin: "MANUAL",
      });
      
      map.removeInteraction(draw);
    });
  }

  function addModifyInteraction() {
    if (!map) return;
    
    const newModifyInteraction = new Modify({ source, pixelTolerance: 30 });
    map.addInteraction(newModifyInteraction);
    setModifyInteraction(newModifyInteraction);
  }

  function changeInteraction(newInteraction: MapInteraction) {
    setInteraction(newInteraction);
    source.clear();

    if (newInteraction === MapInteraction.DRAW) {
      addDrawInteraction();
    } else {
      if (map) {
        if (drawInteraction) map.removeInteraction(drawInteraction);
        if (modifyInteraction) map.removeInteraction(modifyInteraction);
      }
    }
  }

  function updatePropertiesOnMap(newProperties: Property[]) {
    const features = newProperties.map((property) => property.features).flat();
    source.clear();
    if (!features.length) return;
    source.addFeatures(features);
    if (map) {
      const extent = getExtent(features);
      map.getView().fit(extent, { padding: [25, 25, 25, 25] });
    }
  }

  async function handleGetPropertyByCoordinates(
    longitude: number,
    latitude: number
  ) {
    if (!map) return;
    
    const response = await searchCARByClick([longitude, latitude]);
    if (!response.length || !response[0]["geom"]) {
      notification.error({
        message: t("PROPERTY_NOT_FOUND"),
        description: t("NO_PROPERTY_FOUND_IN_COORDS"),
      });
      return;
    }
    
    const geometry = JSON.parse(response[0].geom);
    const area = toHectares(geojsonArea.geometry(geometry));
    const features = geojsonToFeatures(geometry);

    addProperty({
      uid: uuidv4(),
      area,
      car: response[0].cod_imovel,
      geometry,
      features,
      origin: "CAR",
    });
    
    source.clear();
    source.addFeatures(features);
    const extent = getExtent(features);
    map.getView().fit(extent, { padding: [25, 25, 25, 25] });
  }

  return {
    map,
    interaction,
    setMap,
    handleMapClick,
    changeInteraction,
    updatePropertiesOnMap,
    handleGetPropertyByCoordinates,
  };
}
