import { useEffect } from "react";
import { RootState } from "../store";
import { thunkListReportRequest } from "../store/modules/report-requests/list/thunks";
import { useAppDispatch } from "./use-app-dispatch";
import { useSelector } from "react-redux";
import {
  ActionSetReportRequestListFiltersDataPayload,
  ActionSetReportRequestListPaginationDataPayload,
  ListReportRequestsThunkPayload,
  Pagination,
} from "@/store/modules/report-requests/list/types";
import { parseObject } from "@/utils/parse-object";
import {
  actionResetReportRequestListFiltersData,
  actionSetReportRequestListFiltersData,
  actionSetReportRequestListPaginationData,
} from "@/store/modules/report-requests/list/actions";

export type HandleListReportRequestsPayload = ListReportRequestsThunkPayload;

export const useReportRequestsList = () => {
  const dispatch = useAppDispatch();

  const {
    data: { count, items, reports_count },
    pagination,
    loading,
    filters,
    isApiFetched,
  } = useSelector((state: RootState) => state.listReportRequests);

  const handleListReportsRequests = async (
    params: HandleListReportRequestsPayload
  ) => {
    await dispatch(thunkListReportRequest({ ...params, ...filters }));
  };

  const handleSetPaginationData = (payload: {
    pagination?: Partial<Pagination>;
  }) => {
    const parsed =
      parseObject<ActionSetReportRequestListPaginationDataPayload>(payload);

    dispatch(actionSetReportRequestListPaginationData({ ...parsed }));
  };

  const handleClearFiltersAndRefreshReports = () => {
    dispatch(
      actionSetReportRequestListPaginationData({
        pagination: { offset: 0 },
      })
    );

    dispatch(actionResetReportRequestListFiltersData());
    dispatch(thunkListReportRequest({ limit: pagination.limit, offset: 0 }));
  };

  const handleSetFiltersData = (payload: {
    filters?: Record<string, string | number>;
  }) => {
    const parsed =
      parseObject<ActionSetReportRequestListFiltersDataPayload>(payload);
    dispatch(actionSetReportRequestListFiltersData({ ...parsed }));
  };

  useEffect(() => {
    if (loading || items.length) return;
    if (!items.length && Object.keys(filters).length) return;
    if (isApiFetched) return;

    handleListReportsRequests({
      limit: pagination.limit,
      offset: 0,
    });
  }, [loading, items, filters, pagination.limit]);

  return {
    handleListReportsRequests,
    reportsCount: reports_count,
    count,
    requests: items,
    isLoading: loading,
    pagination,
    filters,
    handleSetPaginationData,
    handleClearFiltersAndRefreshReports,
    handleSetFiltersData,
  };
};
