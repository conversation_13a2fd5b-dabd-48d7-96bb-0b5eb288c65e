import { listBanners } from "@services";
import { BannerData } from "@types";
import { useEffect, useState } from "react";

export const useBannersList = () => {
  const [bannersList, setBannersList] = useState<BannerData[]>([]);
  const [isLoadingBanners, setIsLoadingBanners] = useState(false);

  async function handleListBanners() {
    setIsLoadingBanners(true);
    const { items } = await listBanners();
    setBannersList(items);
    setIsLoadingBanners(false);
  }

  useEffect(() => {
    handleListBanners();
  }, []);

  return {
    isLoadingBanners,
    handleListBanners,
    bannersList,
  };
};
