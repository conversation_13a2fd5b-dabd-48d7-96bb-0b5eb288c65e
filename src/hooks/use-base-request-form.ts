import { Property } from "@/types/property";
import { getExtent, geojsonToFeatures } from "@/utils";
import VectorSource from "ol/source/Vector";
import { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import { Map } from "ol";
import { getProperties, getPropertyFields, searchContacts } from "@services";

export const useBaseRequestForm = ({
  onPropertiesLoad,
  selectedSubareas,
  onSubareasLoad,
  properties,
  selectedProperty,
  data,
}) => {
  const [map, setMap] = useState<Map | undefined>(undefined);
  const [propertySource] = useState(new VectorSource());
  const [subareaSource] = useState(new VectorSource());
  const [isLoadingContacts, setIsLoadingContacts] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [showMap, setShowMap] = useState(true);
  const [isContactFormOpen, setIsContactFormOpen] = useState(false);
  const [isPropertyDrawerOpen, setIsPropertyDrawerOpen] = useState(false);
  const [isFieldsDrawerOpen, setIsFieldsDrawerOpen] = useState(false);
  const [isDrawingSubareas, setIsDrawingSubareas] = useState(false);

  const updatePropertyOnMap = (property: Property) => {
    if (!map || !property) return;
    const features = geojsonToFeatures(property.area_coordinates);
    try {
      const extent = getExtent(features);
      propertySource.addFeatures(features);
      map.getView().fit(extent, { padding: [25, 25, 25, 25] });
    } catch (err) {}
  };

  function updateSubareasOnMap(areas) {
    if (!map || !selectedSubareas?.length) return;

    subareaSource.clear();
    let features = areas.map((x) => geojsonToFeatures(x.coordinates)).flat();
    if (features.length) {
      const extent = getExtent(features);
      subareaSource.addFeatures(features);
      map.getView().fit(extent, { padding: [25, 25, 25, 25] });
    }
  }

  const handleFetchContacts = async (query) => {
    try {
      setIsLoadingContacts(true);
      const response = await searchContacts({
        query: query.replace(/[^\w\s]/gi, ""),
      });
      setContacts(
        response
          .filter((r) => r.document_type)
          .map((r) => ({
            label: r.name,
            document_type: r.document_type,
            document: r.document,
            value: {
              id: String(r.id || ''),
              groupId: String(r.group_id || ''),
              label: String(r.document_type || ''),
              value: String(r.document || ''),
              name: typeof r.name === 'string' ? r.name : String(r.name || ''),
            },
          }))
      );
    } catch (error) {
      setContacts([]);
    } finally {
      setIsLoadingContacts(false);
    }
  };

  const fetchProperties = async (selectedContact) => {
    if (!selectedContact) return;
    const contactProperties = await getProperties(selectedContact.id);
    onPropertiesLoad(contactProperties.properties);
  };

  const fetchSubareas = async (selectedContact, selectedProperty) => {
    if (!selectedProperty || !selectedContact || selectedProperty.isInternal)
      return;
    const contactProperties = await getPropertyFields(selectedContact.id, [
      selectedProperty.id,
    ]);
    onSubareasLoad(
      contactProperties.properties
        .filter((x) => x.fields?.length)
        .map((x) => x.fields)
        .flat()
    );
  };

  function onCreateNewArea(newProperty) {
    if (!data.contact) {
      onPropertiesLoad([...properties, newProperty]);
      return;
    }

    if (!isDrawingSubareas) {
      fetchProperties(data.contact);
      return;
    }

    fetchSubareas(data.contact, data.property);
  }

  const debouncedText = useDebouncedCallback((value) => {
    handleFetchContacts(value);
  }, 1000);

  useEffect(() => {
    updatePropertyOnMap(selectedProperty);
  }, [selectedProperty, map]);

  useEffect(() => {
    updateSubareasOnMap(selectedSubareas);
  }, [selectedSubareas, map]);

  return {
    onCreateNewArea,
    debouncedText,
    propertySource,
    subareaSource,
    showMap,
    isLoadingContacts,
    contacts,
    isContactFormOpen,
    isPropertyDrawerOpen,
    isFieldsDrawerOpen,
    setMap,
    setShowMap,
    setIsContactFormOpen,
    setIsPropertyDrawerOpen,
    setIsFieldsDrawerOpen,
    setIsDrawingSubareas,
    fetchProperties,
    fetchSubareas,
  };
};
