import { useEffect } from "react";
import { useAccountDetails } from "./use-account-details";
import { useAppDispatch } from "./use-app-dispatch";
import {
  actionSetDeforestationSocioEnvironmentCriterias,
  actionSetSocioEnvironmentCriterias,
} from "@/store/modules/account/socio-environment-criterias/actions";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { Criterias } from "@/store/modules/account/socio-environment-criterias/types";

const SOCIO_ENVIRONMENTAL_CRITERIAS_AVAILABLE = [
  "PRODES",
  "ASSENTAMENTOS",
  "UNIDADES_CONSERVACAO",
  "QUILOMBOS",
  "EMBARGO_LDI",
  "EMBARGO_IBAMA",
  "EMBARGO_ICMBIO",
  "EMBARGO_SEMA",
  "TERRAS_INDIGENAS",
  "TRABALHO_ESCRAVO",
  "ALERTA_MAPBIOMAS",
  "FLORESTA_PUBLICA_TIPO_B",
  "SITIOS_ARQUEOLOGICOS",
  "SUSCETIBILIDADE_A_INUNDACAO",
  "MORATORIA_DA_SOJA",
  "PROTOCOLO_VERDE_DOS_GRAOS",
  "DAP",
  "UNIDADES_DE_CONSERVACAO_ZA",
  "TERRAS_INDIGENAS_ZA",
  "SOIL_USE_CAR",
];

export const DEFORESTATION_SOCIO_ENVIRONMENT_CRITERIAS_AVAILABLE = [
  "PRODES",
  "DETER",
  "EMBARGO_LDI",
  "EMBARGO_IBAMA",
  "EMBARGO_ICMBIO",
  "EMBARGO_SEMA",
  "MORATORIA_DA_SOJA",
  "PROTOCOLO_VERDE_DOS_GRAOS",
];

export const DEFORESTATION_CRITERIAS_WITHOUT_CUTOFFDATE = [
  "MORATORIA_DA_SOJA",
  "PROTOCOLO_VERDE_DOS_GRAOS",
];

export const DEFORESTATION_CRITERIAS_WITH_MIN_AREA = [
  "PRODES",
  "DETER",
  "EMBARGO_LDI",
  "EMBARGO_IBAMA",
  "EMBARGO_ICMBIO",
  "EMBARGO_SEMA",
];

export const useSocioEnvironmentCriterias = () => {
  const dispatch = useAppDispatch();
  const { account } = useAccountDetails();
  const {
    data: { deforestationCriterias, socioEnvironmentalCriterias },
  } = useSelector((state: RootState) => state.socioEnvironmentCriterias);

  const handleSetSocioEnvironmentalCriterias = (data: {
    deforestationCriterias?: Criterias;
    socioEnvironmentalCriterias?: Criterias;
  }) => {
    if (data.deforestationCriterias) {
      dispatch(
        actionSetDeforestationSocioEnvironmentCriterias(
          data.deforestationCriterias
        )
      );
    }
    if (data.socioEnvironmentalCriterias) {
      dispatch(
        actionSetSocioEnvironmentCriterias(data.socioEnvironmentalCriterias)
      );
    }
  };

  useEffect(() => {
    handleSetSocioEnvironmentalCriterias({
      deforestationCriterias: account.criterias_list
        .filter((item) =>
          DEFORESTATION_SOCIO_ENVIRONMENT_CRITERIAS_AVAILABLE.includes(
            item.value
          )
        )
        .map((item) => ({ ...item, checked: false })),

      socioEnvironmentalCriterias: account.criterias_list
        .filter((item) =>
          SOCIO_ENVIRONMENTAL_CRITERIAS_AVAILABLE.includes(item.value)
        )
        .map((item) => ({ ...item, checked: false })),
    });
  }, [account.criterias_list]);

  return {
    deforestationCriterias,
    socioEnvironmentalCriterias,
    handleSetSocioEnvironmentalCriterias,
  };
};
