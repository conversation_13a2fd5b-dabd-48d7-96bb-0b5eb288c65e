import i18next from "i18next";
import "../src/i18n/index";

i18next.changeLanguage("pt-BR");

jest.mock("axios", () => {
  return {
    create: jest.fn(() => {
      return {
        get: jest.fn((url) => {
          if (url.includes("cards")) {
            return {
              data: [],
            };
          }
          return Promise.resolve({
            data: {
              count: 0,
              reports_count: 0,
              items: [],
            },
          });
        }),
        post: jest.fn(),
        put: jest.fn(),
        patch: jest.fn(),
        delete: jest.fn(),
        interceptors: {
          request: {
            use: jest.fn(),
          },
          response: {
            use: jest.fn(),
          },
        },
      };
    }),
  };
});

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

jest.mock("./hooks/use-cards-list", () => {
  return {
    useCardsList: () => ({
      cardsList: [],
      handleListCards: jest.fn(),
    }),
  };
});

jest.mock("./hooks/use-list-reports", () => {
  return {
    useListReports: () => ({
      handleListReports: jest.fn(),
      reports: [],
      total: 0,
    }),
  };
});

jest.mock("./hooks/use-banners-list", () => {
  return {
    useBannersList: jest.fn(() => ({
      bannersList: [],
      handleListBanners: jest.fn(),
      isLoadingBanners: true,
    })),
  };
});
jest.mock("./hooks/use-tags-list", () => {
  return {
    useTagsList: () => ({
      tagsList: [],
      handleListTags: jest.fn(),
    }),
  };
});

jest.mock("./hooks/use-request-form", () => {
  return {
    useRequestForm: () => ({
      handleOpenRequestForm: jest.fn(),
    }),
  };
});

jest.mock("./hooks/use-socio-environment-criterias", () => {
  return {
    useSocioEnvironmentCriterias: jest.fn(() => ({
      deforestationCriterias: [
        {
          label: "PRODES",
          value: "PRODES",
        },
      ],
      socioEnvironmentalCriterias: [
        {
          label: "DETER",
          value: "DETER",
        },
      ],
      handleSetSocioEnvironmentalCriterias: jest.fn(),
    })),
  };
});

jest.mock("./hooks/use-account-details", () => {
  return {
    useAccountDetails: jest.fn(() => ({
      handleGetDetailsAccount: jest.fn(),
      account: {
        resources: ["valuation-report-signed"],
        sub_modules: [],
      },
      hasResource: jest.fn(),
    })),
  };
});

jest.mock("./hooks/use-request-form", () => {
  return {
    useRequestForm: () => ({
      handleOpenRequestForm: jest.fn(),
    }),
  };
});

jest.mock("./hooks/use-report-requests-list", () => {
  return {
    useReportRequestsList: () => {
      return {
        handleListReportsRequests: jest.fn(),
        handleSetPaginationData: jest.fn(),
        handleSetFiltersData: jest.fn(),
        reportsCount: 0,
        count: 0,
        requests: [],
        pagination: {
          total: 10,
          limit: 10,
          offset: 0,
          pages: 1,
          totalPages: 1,
        },
      };
    },
  };
});

jest.mock("./hooks/use-request-form-context", () => {
  return {
    useRequestFormContext: () => ({
      isRequestFormOpen: false,
      closeRequestForm: jest.fn(),
      reportMetadata: { type: "VALUATION" },
    }),
  };
});

jest.mock("@/hooks/use-portifolio-diagnosis");
