import { combineReducers, configureStore } from "@reduxjs/toolkit";
import { getDetailsAccount } from "./modules/account/get-details/reducer";
import { listReportRequests } from "./modules/report-requests/list/reducer";
import { soybeanDeforestationForm } from "./modules";
import { listReport } from "./modules/report/list/reducer";
import { socioEnvironmentCriterias } from "./modules/account/socio-environment-criterias/reducer";
import { cardList } from "./modules/card/list/reducer";
import { certificationRenovaBio } from "./modules/certification-renova-bio-form/reducer";
import { portifolioDiagnosis } from "./modules/portifolio-diagnosis/reducer";
import { certification2BSvs } from "./modules/certification-2bsvs/reducer";

const reducers = combineReducers({
  getDetailsAccount,
  listReportRequests,
  listReport,
  soybeanDeforestationForm,
  socioEnvironmentCriterias,
  cardList,
  certificationRenovaBio,
  portifolioDiagnosis,
  certification2BSvs,
});

export const store = configureStore({
  reducer: reducers,
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
