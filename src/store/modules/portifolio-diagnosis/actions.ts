import { createAction } from "@reduxjs/toolkit";
import {
  ACTION_TYPES,
  ActionSetPortifolioDiagnosisBatchFileDetailsPayload,
  ReportPortifolioDiagnosisBatchDocumentType,
  ReportPortifolioDiagnosisManualSelectType,
} from "./types";
import { RequestMethod, SubModule } from "@/types";

export const actionSetPortifolioDiagnosisRequestMethod =
  createAction<RequestMethod>(ACTION_TYPES.SET_REQUEST_METHOD);

export const actionSetPortifolioDiagnosisManualSelectType =
  createAction<ReportPortifolioDiagnosisManualSelectType>(
    ACTION_TYPES.SET_MANUAL_SELECT_TYPE
  );

export const actionSetPortifolioDiagnosisMunicipalitiesSelected = createAction<
  Array<{ uf: string; name: string; ibgeCode: number }>
>(ACTION_TYPES.SET_MUNICIPALITIES);

export const actionSetPortifolioDiagnosisStatesSelected = createAction<
  Array<{ uf: string; name: string }>
>(ACTION_TYPES.SET_STATES);

export const actionSearchPortifolioDiagnosisMunicipality = createAction<string>(
  ACTION_TYPES.SEARCH_MUNICIPALITY
);

export const actionSearchPortifolioDiagnosisState = createAction<string>(
  ACTION_TYPES.SEARCH_STATE
);

export const actionSetPortifolioDiagnosisBatchDocumentType =
  createAction<ReportPortifolioDiagnosisBatchDocumentType>(
    ACTION_TYPES.SET_BATCH_DOCUMENT_TYPE
  );

export const actionSetPortifolioDiagnosisBatchDocumentList = createAction<
  string[]
>(ACTION_TYPES.SET_BATCH_DOCUMENT_LIST);

export const actionSetPortifolioDiagnosisBatchInvalidDocuments = createAction<
  string[]
>(ACTION_TYPES.SET_BATCH_INVALID_DOCUMENTS);

export const actionResetPortifolioDiagnosisState = createAction(
  ACTION_TYPES.RESET_STATE
);

export const actionSetPortifolioDiagnosisBatchFileDetails =
  createAction<ActionSetPortifolioDiagnosisBatchFileDetailsPayload>(
    ACTION_TYPES.SET_BATCH_FILE_DETAILS
  );

export const actionSetPortifolioDiagnosisManualSubModulesSelected =
  createAction<Array<SubModule>>(
    ACTION_TYPES.SET_MANUALLY_SUB_MODULES_SELECTED
  );

export const actionSetPortifolioDiagnosisManualSubModules = createAction<
  SubModule[]
>(ACTION_TYPES.SET_SUB_MODULES);
