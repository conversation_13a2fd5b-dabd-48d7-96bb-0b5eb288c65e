import { createReducer } from "@reduxjs/toolkit";
import {
  actionSearchPortifolioDiagnosisMunicipality,
  actionSearchPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisMunicipalitiesSelected,
  actionSetPortifolioDiagnosisRequestMethod,
  actionSetPortifolioDiagnosisStatesSelected,
  actionSetPortifolioDiagnosisBatchDocumentType,
  actionSetPortifolioDiagnosisBatchDocumentList,
  actionSetPortifolioDiagnosisBatchInvalidDocuments,
  actionResetPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisManualSelectType,
  actionSetPortifolioDiagnosisBatchFileDetails,
  actionSetPortifolioDiagnosisManualSubModulesSelected,
  actionSetPortifolioDiagnosisManualSubModules,
} from "./actions";
import {
  PortifolioDiagnosisState,
  ReportPortifolioDiagnosisBatchDocumentType,
  ReportPortifolioDiagnosisManualSelectType,
} from "./types";
import { ReportType, RequestMethod } from "@types";

import municipalities from "../../../assets/data/cities.json";
import states from "../../../assets/data/states.json";
import { thunkCreatePortifolioDiagnosisReportRequest } from "./thunks";

export const PortifolioDiagnosisInitialState: PortifolioDiagnosisState = {
  data: {
    reportType: ReportType.PORTFOLIO_DIAGNOSIS,
    requestMethod: RequestMethod.BATCH,
    manual: {
      states,
      municipalities,
      municipaliciesSelected: [],
      statesSelected: [],
      searchMunicipality: "",
      searchState: "",
      selectType: ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY,
      subModules: [],
      subModulesSelected: [],
    },
    batch: {
      documentType: ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ,
      documentsList: [],
      invalidDocuments: [],
      subModulesAvailable: [],
      fileDetails: { keyName: "", name: "", type: "" },
    },
  },
  loading: false,
  error: "",
};

export const portifolioDiagnosis = createReducer(
  PortifolioDiagnosisInitialState,
  (builder) => {
    builder.addCase(
      actionSetPortifolioDiagnosisRequestMethod,
      (state, action) => {
        state.data.requestMethod = action.payload;
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisMunicipalitiesSelected,
      (state, action) => {
        state.data.manual.municipaliciesSelected = action.payload;
      }
    );
    builder.addCase(
      actionSetPortifolioDiagnosisStatesSelected,
      (state, action) => {
        state.data.manual.statesSelected = action.payload;
      }
    );

    builder.addCase(
      actionSearchPortifolioDiagnosisMunicipality,
      (state, action) => {
        state.data.manual.searchMunicipality = action.payload;
      }
    );

    builder.addCase(actionSearchPortifolioDiagnosisState, (state, action) => {
      state.data.manual.searchState = action.payload;
    });

    builder.addCase(
      thunkCreatePortifolioDiagnosisReportRequest.pending,
      (state) => {
        state.loading = true;
      }
    );
    builder.addCase(
      thunkCreatePortifolioDiagnosisReportRequest.fulfilled,
      (state) => {
        state.loading = false;
      }
    );
    builder.addCase(
      thunkCreatePortifolioDiagnosisReportRequest.rejected,
      (state, action) => {
        state.loading = false;
        state.error = JSON.stringify(action.payload);
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisBatchDocumentType,
      (state, action) => {
        state.data.batch.documentType = action.payload;
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisBatchDocumentList,
      (state, action) => {
        state.data.batch.documentsList = action.payload;
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisBatchInvalidDocuments,
      (state, action) => {
        state.data.batch.invalidDocuments = action.payload;
      }
    );

    builder.addCase(actionResetPortifolioDiagnosisState, (state) => {
      state.data = PortifolioDiagnosisInitialState.data;
      state.loading = PortifolioDiagnosisInitialState.loading;
      state.error = PortifolioDiagnosisInitialState.error;
    });

    builder.addCase(
      actionSetPortifolioDiagnosisManualSelectType,
      (state, action) => {
        state.data.manual.selectType = action.payload;
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisBatchFileDetails,
      (state, action) => {
        state.data.batch.fileDetails = action.payload;
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisManualSubModulesSelected,
      (state, action) => {
        state.data.manual.subModulesSelected = action.payload;
      }
    );

    builder.addCase(
      actionSetPortifolioDiagnosisManualSubModules,
      (state, action) => {
        if (!action.payload) return;
        const subModules = action.payload.map((item) => ({
          ...item,
          active: Boolean(item.active),
        }));
        state.data.manual.subModules = subModules;
        state.data.batch.subModulesAvailable = subModules;
      }
    );
  }
);
