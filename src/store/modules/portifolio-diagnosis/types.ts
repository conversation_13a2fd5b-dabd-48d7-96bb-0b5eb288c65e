import { ReportType, RequestMethod, SubModule } from "@types";

export enum ACTION_TYPES {
  SET_REQUEST_METHOD = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_REQUEST_METHOD",
  SET_MUNICIPALITIES = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_MUNICIPALITIES",
  SET_STATES = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_STATES",
  SEARCH_MUNICIPALITY = "@ACTION/SEARCH/PORTIFOLIO_DIAGNOSIS_STATES_MUNICIPALITY",
  SEARCH_STATE = "@ACTION/SEARCH/PORTIFOLIO_DIAGNOSIS_STATES_STATE",
  SET_BATCH_DOCUMENT_TYPE = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_DOCUMENT_TYPE",
  SET_BATCH_DOCUMENT_LIST = "@ACTION/SET/PORTIF<PERSON><PERSON>_DIAGNOSIS_BATCH_DOCUMENT_LIST",
  SET_BATCH_INVALID_DOCUMENTS = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_BATCH_INVALID_DOCUMENTS",
  RESET_STATE = "@ACTION/RESET/PORTIFOLIO_DIAGNOSIS_STATE",
  SET_MANUAL_SELECT_TYPE = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_MANUAL_SELECT_TYPE",
  SET_BATCH_FILE_DETAILS = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_BATCH_FILE_DETAILS",
  SET_MANUALLY_SUB_MODULES_SELECTED = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_MANUALLY_SUB_MODULES_SELECTED",
  SET_SUB_MODULES = "@ACTION/SET/PORTIFOLIO_DIAGNOSIS_SUBMODULES",
}

export enum THUNK_TYPES {
  CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST = "@THUNK/CREATE/PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST",
}

export type ThunkCreatePortifolioDiagnosisReportRequestPayload = {
  form: FormData;
  reportType: ReportType;
};

export enum ReportPortifolioDiagnosisBatchDocumentType {
  CAR = "CAR",
  CPF_CNPJ = "CPF/CNPJ",
}

export enum ReportPortifolioDiagnosisManualSelectType {
  MUNICIPALITY = "MUNICIPALITY",
  STATE = "STATE",
}

export type PortifolioDiagnosisState = {
  data: {
    requestMethod: RequestMethod;
    reportType: ReportType;
    manual: {
      municipaliciesSelected: Array<{
        uf: string;
        name: string;
        ibgeCode: number;
      }>;
      statesSelected: Array<{ uf: string; name: string }>;
      searchMunicipality: string;
      searchState: string;
      municipalities: Array<{ uf: string; name: string; ibge_code: number }>;
      states: Array<{ uf: string; name: string }>;
      selectType: ReportPortifolioDiagnosisManualSelectType;
      subModulesSelected: Array<SubModule>;
      subModules: Array<SubModule>;
    };
    batch: {
      documentType: ReportPortifolioDiagnosisBatchDocumentType;
      documentsList: Array<string>;
      invalidDocuments: Array<string>;
      subModulesAvailable: Array<SubModule>;
      fileDetails: {
        name: string;
        type: string;
        keyName: string;
      };
    };
  };
  loading: boolean;
  error: string;
};

export type ActionSetPortifolioDiagnosisBatchFileDetailsPayload =
  PortifolioDiagnosisState["data"]["batch"]["fileDetails"];
