import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  THUNK_TYPES,
  ThunkCreatePortifolioDiagnosisReportRequestPayload,
} from "./types";
import { createFileReportRequest } from "@services";

export const thunkCreatePortifolioDiagnosisReportRequest = createAsyncThunk(
  THUNK_TYPES.CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST,
  async (
    payload: ThunkCreatePortifolioDiagnosisReportRequestPayload,
    thunkApi
  ) => {
    const { form, reportType } = payload;

    return createFileReportRequest({
      formData: form,
      url: `/reports-requests/${reportType.toLowerCase()}`,
    }).catch((err) => thunkApi.rejectWithValue(err));
  }
);
