import { createAsyncThunk } from "@reduxjs/toolkit";
import { listReports } from "@services";
import { THUNK_TYPES } from "./types";
import { ListReportThunkPayload } from "./types";

export const thunkListReport = createAsyncThunk(
  THUNK_TYPES.LIST_REPORTS,
  async (payload: ListReportThunkPayload, thunkApi) => {
    return listReports(payload).catch((err) => thunkApi.rejectWithValue(err));
  }
);
