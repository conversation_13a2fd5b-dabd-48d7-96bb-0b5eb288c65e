import { createReducer } from "@reduxjs/toolkit";
import { thunkListReport } from "./thunks";
import { ListReportState } from "./types";

const ListReportsInitialState: ListReportState = {
  data: {
    count: 0,
    items: [],
  },
  loading: false,
  error: "",
};

export const listReport = createReducer(ListReportsInitialState, (builder) => {
  builder.addCase(thunkListReport.pending, (state) => {
    state.loading = true;
  });

  builder.addCase(thunkListReport.fulfilled, (state, action) => {
    state.data.count = action.payload.count;
    state.data.items = action.payload.items;
    state.loading = false;
  });

  builder.addCase(thunkListReport.rejected, (state, action) => {
    state.error = action.payload as string;
    state.loading = false;
  });
});
