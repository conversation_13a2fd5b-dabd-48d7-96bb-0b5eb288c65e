import { createReducer } from "@reduxjs/toolkit";
import { GetDetailsAccountState } from "./types";
import { thunkGetDetailsAccount } from "./thunks";

export const GetDetailsAccountInitialState: GetDetailsAccountState = {
  data: {
    credit: 0,
    credit_available: {
      PORTFOLIO_DIAGNOSIS: 0,
      AGRO_CREDIT: 0,
      ALL: 0,
      DETAILED_ANALYSIS_DEFORESTATION: 0,
      INSPECTION: 0,
      INSPECTION_FINANCIAL: 0,
      SINISTER: 0,
      SOCIOENVIRONMENT_CERTIFICATION: 0,
      SOCIOENVIRONMENT_COMPLIANCE: 0,
      SOCIOENVIRONMENT_PROTOCOL: 0,
      SOC<PERSON><PERSON>VIRONMENT_PROTOCOL_EUDR: 0,
      SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE: 0,
      SOCIOENVIRONMENT_PROTOCOL_MARFRIG: 0,
      SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER: 0,
      SOY_DEFORESTATION_CAR: 0,
      SOY_DEFORESTATION_MUNICIPALITY: 0,
      VALUATION: 0,
      DETAILED_ANALYSIS_DEFORESTATION_EUDR: 0,
      RENOVABIO_CERTIFICATION: 0,
      RENOVABIO_MONITORING: 0,
      CERTIFICATION_2BSVS: 0,
    },
    document: "",
    sub_modules: [],
    id: 0,
    id_external: 0,
    id_platform: 0,
    name: "",
    plan: "",
    resources: [],
    type: "",
    criterias_list: [],
  },
  error: "",
  loading: false,
};

export const getDetailsAccount = createReducer(
  GetDetailsAccountInitialState,
  (builder) => {
    builder.addCase(thunkGetDetailsAccount.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(thunkGetDetailsAccount.fulfilled, (state, action) => {
      state.loading = false;
      state.error = "";
      state.data = action.payload;
      state.data.credit_available = {
        ...state.data.credit_available,
        ...action.payload.credit_available,
      };
    });
    builder.addCase(thunkGetDetailsAccount.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  }
);
