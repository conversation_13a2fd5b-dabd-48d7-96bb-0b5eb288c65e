export type SocioEnvironmentCriteria = {
  label: string;
  value: string;
};

export enum ACTION_TYPES {
  SET_DEFORESTATION_CRITERIAS = "@ACTION/SET/DEFORESTATION_SOCIO_ENVIRONMENT_CRITERIAS",
  SET_SOCIO_ENVIRONMENT_CRITERIAS = "@ACTION/SET/SOCIO_ENVIRONMENT_CRITERIAS",
}

export type Criterias = Array<{
  label: string;
  value: string;
  checked: boolean;
}>;

export type SetCriteriasActionPayload = Criterias;

export type SocioEnvironmentCriteriasState = {
  data: {
    deforestationCriterias: Criterias;
    socioEnvironmentalCriterias: Criterias;
  };
  error: string;
  loading: boolean;
};
