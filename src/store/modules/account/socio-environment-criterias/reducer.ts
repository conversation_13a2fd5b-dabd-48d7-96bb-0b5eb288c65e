import { createReducer } from "@reduxjs/toolkit";
import {
  actionSetDeforestationSocioEnvironmentCriterias,
  actionSetSocioEnvironmentCriterias,
} from "./actions";

import { SocioEnvironmentCriteriasState } from "./types";

const SocioEnvironmentCriteriasInitialState: SocioEnvironmentCriteriasState = {
  data: {
    deforestationCriterias: [],
    socioEnvironmentalCriterias: [],
  },
  error: "",
  loading: false,
};

export const socioEnvironmentCriterias = createReducer(
  SocioEnvironmentCriteriasInitialState,
  (builder) => {
    builder.addCase(
      actionSetDeforestationSocioEnvironmentCriterias,
      (state, action) => {
        state.data.deforestationCriterias = action.payload;
      }
    );

    builder.addCase(actionSetSocioEnvironmentCriterias, (state, action) => {
      state.data.socioEnvironmentalCriterias = action.payload;
    });
  }
);
