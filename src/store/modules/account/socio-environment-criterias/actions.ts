import { createAction } from "@reduxjs/toolkit";
import { ACTION_TYPES, SetCriteriasActionPayload } from "./types";

export const actionSetDeforestationSocioEnvironmentCriterias = createAction<
  SetCriteriasActionPayload,
  ACTION_TYPES.SET_DEFORESTATION_CRITERIAS
>(ACTION_TYPES.SET_DEFORESTATION_CRITERIAS);

export const actionSetSocioEnvironmentCriterias = createAction<
  SetCriteriasActionPayload,
  ACTION_TYPES.SET_SOCIO_ENVIRONMENT_CRITERIAS
>(ACTION_TYPES.SET_SOCIO_ENVIRONMENT_CRITERIAS);
