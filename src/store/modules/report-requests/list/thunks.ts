import { createAsyncThunk } from "@reduxjs/toolkit";
import { listReportRequests } from "@services";
import { THUNK_TYPES } from "./types";
import { ListReportRequestsThunkPayload } from "./types";
import { RootState } from "@/store";

export const thunkListReportRequest = createAsyncThunk(
  THUNK_TYPES.LIST_REPORT_REQUESTS,
  async (payload: ListReportRequestsThunkPayload, thunkApi) => {
    const {
      listReportRequests: { filters, pagination },
    } = thunkApi.getState() as RootState;
    const { limit, offset } = pagination;
    return listReportRequests({ limit, offset, ...filters }).catch((err) =>
      thunkApi.rejectWithValue(err)
    );
  }
);
