import { ReportRequest, ReportType } from "@types";

export type Pagination = {
  offset: number;
  limit: number;
  total: number;
  pages: number;
  totalPages: number;
};

export type ListReportRequestsState = {
  data: { items: ReportRequest[]; count: number; reports_count: number };
  pagination: Pagination;
  filters: Record<string, string | number>;
  loading: boolean;
  error: string;
  isApiFetched: boolean;
};

export enum THUNK_TYPES {
  LIST_REPORT_REQUESTS = "@THUNK/LIST/REPORT_REQUESTS",
}
export enum ACTION_TYPES {
  SET_REPORT_REQUEST_LIST_PAGINATION = "@ACTION/SET/REPORT_REQUEST_LIST_PAGINATION",
  SET_REPORT_REQUEST_LIST_FILTERS = "@ACTION/SET/REPORT_REQUEST_LIST_FILTERS",
  RESET_REPORT_REQUEST_LIST_FILTERS = "@ACTION/RESET/REPORT_REQUEST_LIST_FILTERS",
}

export type ActionSetReportRequestListPaginationDataPayload = {
  pagination: Partial<Pagination>;
};

export type ActionSetReportRequestListFiltersDataPayload = {
  filters: Record<string, string | number>;
};

export type ListReportRequestsThunkPayload = {
  search?: string;
  created_start?: string;
  created_end?: string;
  reports_types?: Array<ReportType>;
  limit?: number;
  offset?: number;
};
