import { createAction } from "@reduxjs/toolkit";
import {
  ACTION_TYPES,
  ActionSetReportRequestListFiltersDataPayload,
  ActionSetReportRequestListPaginationDataPayload,
} from "./types";

export const actionSetReportRequestListPaginationData =
  createAction<ActionSetReportRequestListPaginationDataPayload>(
    ACTION_TYPES.SET_REPORT_REQUEST_LIST_PAGINATION
  );

export const actionSetReportRequestListFiltersData =
  createAction<ActionSetReportRequestListFiltersDataPayload>(
    ACTION_TYPES.SET_REPORT_REQUEST_LIST_FILTERS
  );

export const actionResetReportRequestListFiltersData = createAction(
  ACTION_TYPES.RESET_REPORT_REQUEST_LIST_FILTERS
);
