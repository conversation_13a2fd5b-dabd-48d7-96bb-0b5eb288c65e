import { createReducer } from "@reduxjs/toolkit";
import { thunkListReportRequest } from "./thunks";
import { ListReportRequestsState } from "./types";
import {
  actionSetReportRequestListFiltersData,
  actionSetReportRequestListPaginationData,
  actionResetReportRequestListFiltersData,
} from "./actions";

const ListReportsInitialState: ListReportRequestsState = {
  data: {
    count: 0,
    items: [],
    reports_count: 0,
  },
  filters: {},
  pagination: {
    offset: 0,
    limit: 10,
    total: 0,
    pages: 0,
    totalPages: 0,
  },

  loading: false,
  error: "",
  isApiFetched: false,
};

export const listReportRequests = createReducer(
  ListReportsInitialState,
  (builder) => {
    builder.addCase(thunkListReportRequest.pending, (state, action) => {
      state.loading = true;
      if (action.meta.arg.offset !== undefined) {
        state.pagination.offset = action.meta.arg.offset;
      }
      if (action.meta.arg.limit !== undefined) {
        state.pagination.limit = action.meta.arg.limit;
      }
    });

    builder.addCase(thunkListReportRequest.fulfilled, (state, action) => {
      state.data = action.payload;
      state.pagination.total = action.payload.count;
      state.pagination.pages = Math.ceil(
        action.payload.items.length / state.pagination.limit
      );
      state.pagination.totalPages = Math.ceil(
        action.payload.count / state.pagination.limit
      );
      state.loading = false;
      state.isApiFetched = true;
    });

    builder.addCase(thunkListReportRequest.rejected, (state, action) => {
      state.error = action.payload as string;
      state.loading = false;
      state.isApiFetched = true;
    });

    builder.addCase(
      actionSetReportRequestListPaginationData,
      (state, { payload }) => {
        state.pagination = { ...state.pagination, ...payload.pagination };
      }
    );

    builder.addCase(
      actionSetReportRequestListFiltersData,
      (state, { payload }) => {
        state.filters = { ...state.filters, ...payload.filters };
      }
    );

    builder.addCase(actionResetReportRequestListFiltersData, (state) => {
      state.filters = {};
    });
  }
);
