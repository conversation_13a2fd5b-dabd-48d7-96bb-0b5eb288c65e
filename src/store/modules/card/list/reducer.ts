import { createReducer } from "@reduxjs/toolkit";
import { thunkListCard } from "./thunks";
import { ListCardState } from "./types";

const ListCardsInitialState: ListCardState = {
  data: [],
  loading: false,
  error: "",
};

export const cardList = createReducer(ListCardsInitialState, (builder) => {
  builder.addCase(thunkListCard.pending, (state) => {
    state.loading = true;
  });

  builder.addCase(thunkListCard.fulfilled, (state, action) => {
    state.data = action.payload;
    state.loading = false;
  });

  builder.addCase(thunkListCard.rejected, (state, action) => {
    state.error = action.payload as string;
    state.loading = false;
  });
});
