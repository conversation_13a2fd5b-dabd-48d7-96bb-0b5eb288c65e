import { createAsyncThunk } from "@reduxjs/toolkit";
import { THUNK_TYPES, ThunkCreateRenovaBioReportRequestPayload } from "./types";
import {
  addTagToRequest,
  createFileBatchReportRequest,
  createTags,
} from "@services";
import { Tag } from "@types";

export const thunkCreateRenovaBioReportRequest = createAsyncThunk(
  THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST,
  async (payload: ThunkCreateRenovaBioReportRequestPayload, thunkApi) => {
    const { reportType, formData } = payload;
    const { tagsList, selectedTags } = payload;
    try {
      const response = await createFileBatchReportRequest(
        formData,
        reportType,
        false
      )
      if (response && selectedTags.length) {
        const existingSelectedTags = tagsList.filter((t) =>
          selectedTags.includes(t.name)
        );
        const newTags = selectedTags.filter(
          (t) => !tagsList.find((tag) => tag.name == t)
        );
        let currentTags: Tag[] = existingSelectedTags;
        if (newTags.length) {
          currentTags.push(...await createTags(newTags));
        }

        const addTagRequests = currentTags.map((tag) =>
          addTagToRequest(response.id, tag.id)
        );

        await Promise.all(addTagRequests);
      }
      return response;
    } catch (err) {
      return thunkApi.rejectWithValue(err);
    }
  }
);
