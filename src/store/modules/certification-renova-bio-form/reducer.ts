import { createReducer } from "@reduxjs/toolkit";

import {
  actionRenovaBioCertificationResetFileMetadata,
  actionSetCertificationRenovaBioFormStep,
  actionSetRenovaBioReportType,
  actionSetCertificationRenovaBioDocumentsData,
  actionRenovaBioCertificationResetState,
} from "./actions";

import { CertificationRenovaBioState } from "./types";
import { ReportType } from "@types";
import { thunkCreateRenovaBioReportRequest } from "./thunks";

const CertificationRenovaBioFormInitialState: CertificationRenovaBioState = {
  data: {
    formData: null,
    formStep: 0,
    invalidRows: [],
    carCodes: [],
    fileMetadata: {
      lastModified: 0,
      name: "",
      size: 0,
      type: "",
    },
    reportType: ReportType.RENOVABIO_CERTIFICATION,
  },
  loading: false,
  error: "",
};

export const certificationRenovaBio = createReducer(
  CertificationRenovaBioFormInitialState,
  (builder) => {
    builder.addCase(
      actionSetCertificationRenovaBioFormStep,
      (state, action) => {
        state.data.formStep = action.payload;
      }
    );

    builder.addCase(actionSetRenovaBioReportType, (state, action) => {
      state.data.reportType = action.payload;
    });

    builder.addCase(actionRenovaBioCertificationResetFileMetadata, (state) => {
      state.data.fileMetadata =
        CertificationRenovaBioFormInitialState.data.fileMetadata;
    });

    builder.addCase(actionRenovaBioCertificationResetState, (state) => {
      state.data = CertificationRenovaBioFormInitialState["data"];
    });

    builder.addCase(
      actionSetCertificationRenovaBioDocumentsData,
      (state, action) => {
        state.data = action.payload;
      }
    );

    builder.addCase(thunkCreateRenovaBioReportRequest.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(thunkCreateRenovaBioReportRequest.fulfilled, (state) => {
      state.loading = false;
      state.error = "";
      state.data = CertificationRenovaBioFormInitialState.data;
    });
    builder.addCase(
      thunkCreateRenovaBioReportRequest.rejected,
      (state, action) => {
        state.loading = false;
        state.error = JSON.stringify(action.payload);
      }
    );
  }
);
