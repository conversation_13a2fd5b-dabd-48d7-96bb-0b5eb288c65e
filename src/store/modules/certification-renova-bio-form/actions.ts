import { createAction } from "@reduxjs/toolkit";
import {
  ACTION_TYPES,
  ActionSetCertificationRenovaBioDocumentsDataPayload,
} from "./types";

import { ReportType } from "@types";

export const actionSetCertificationRenovaBioFormStep = createAction<number>(
  ACTION_TYPES.SET_RENOVA_BIO_FORM_STEP
);

export const actionSetCertificationRenovaBioDocumentsData =
  createAction<ActionSetCertificationRenovaBioDocumentsDataPayload>(
    ACTION_TYPES.SET_DOCUMENTS_DATA
  );

export const actionSetRenovaBioReportType = createAction<ReportType>(
  ACTION_TYPES.SET_REPORT_TYPE
);

export const actionRenovaBioCertificationResetFileMetadata = createAction(
  ACTION_TYPES.RESET_FILE_METADATA
);

export const actionRenovaBioCertificationResetState = createAction(
  ACTION_TYPES.RESET_STATE
);
