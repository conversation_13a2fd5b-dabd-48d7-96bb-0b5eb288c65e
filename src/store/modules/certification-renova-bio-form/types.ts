import { ReportType, Tag } from "@types";

export enum ACTION_TYPES {
  SET_RENOVA_BIO_FORM_STEP = "@ACTIONS/SET/RENOVA_BIO_FORM_STEP",
  SET_DOCUMENTS_DATA = "@ACTIONS/SET/CERTIFICATION_RENOVA_BIO_DOCUMENTS_DATA",
  SET_REPORT_TYPE = "@ACTIONS/SET/RENOVABIO_REPORT_TYPE",
  RESET_FILE_METADATA = "@ACTIONS/RESET/FILE_METADATA",
  RESET_STATE = "@ACTIONS/RESET/RENOVA_BIO_CERTIFICATION_STATE",
}

export enum THUNK_TYPES {
  CREATE_RENOVA_BIO_REPORT_REQUEST = "@THUNK/CREATE/RENOVA_BIO_REPORT_REQUEST",
}

export type CertificationRenovaBioState = {
  data: {
    formData: null | FormData;
    fileMetadata: {
      name: string;
      size: number;
      type: string;
      lastModified: number;
    };
    carCodes: Array<{ car: string }>;
    invalidRows: Array<{
      car: string;
      ano_elegibilidade: number;
      cultura: string;
    }>;
    reportType: ReportType;
    formStep: number;
  };
  loading: boolean;
  error: string;
};

export type ThunkCreateRenovaBioReportRequestPayload = {
  reportType: ReportType;
  formData: FormData;
  selectedTags: string[];
  tagsList: Tag[];
};

export type ActionSetCertificationRenovaBioDocumentsDataPayload = Pick<
  CertificationRenovaBioState["data"],
  | "carCodes"
  | "fileMetadata"
  | "formStep"
  | "invalidRows"
  | "reportType"
  | "formData"
>;
