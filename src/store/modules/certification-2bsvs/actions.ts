import { createAction } from "@reduxjs/toolkit";
import {
  ACTION_TYPES,
  Certification2BSvsFormStep,
  Certification2BSvsLanguages,
} from "./types";

export const actionSetCertification2BSvsLanguage =
  createAction<Certification2BSvsLanguages>(ACTION_TYPES.SET_LANGUAGE);

export const actionSetCertification2BSvsInvalidCars = createAction<string[]>(
  ACTION_TYPES.SET_INVALID_CARS
);

export const actionSetCertification2BSvsValidCars = createAction<string[]>(
  ACTION_TYPES.SET_VALID_CARS
);

export const actionSetCertification2BSvsFormStep =
  createAction<Certification2BSvsFormStep>(ACTION_TYPES.SET_FORM_STEP);

export const actionSetCertification2BSvsFileMetadata = createAction<{
  name: string;
  type: string;
}>(ACTION_TYPES.SET_FILE_METADATA);

export const actionResetCertification2BSvsFileMetadata = createAction(
  ACTION_TYPES.RESET_FILE_METADATA
);

export const actionResetCertification2BSvsState = createAction(
  ACTION_TYPES.RESET_STATE
);

export const actionSetCertification2BSvsFileObjectUrl = createAction<string>(
  ACTION_TYPES.SET_FILE_OBJECT_URL
);
