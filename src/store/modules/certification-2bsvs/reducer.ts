import { createReducer } from "@reduxjs/toolkit";
import {
  Certification2BSvsState,
  Certification2BSvsLanguages,
  Certification2BSvsFormStep,
} from "./types";
import {
  actionSetCertification2BSvsInvalidCars,
  actionSetCertification2BSvsLanguage,
  actionSetCertification2BSvsValidCars,
  actionSetCertification2BSvsFormStep,
  actionSetCertification2BSvsFileMetadata,
  actionResetCertification2BSvsFileMetadata,
  actionResetCertification2BSvsState,
  actionSetCertification2BSvsFileObjectUrl,
} from "./actions";

import { thunkCreateCertification2BSvsReportRequest } from "./thunks";
import { ReportType } from "@types";

export const Certification2BSvsInitialState: Certification2BSvsState = {
  data: {
    formStep: Certification2BSvsFormStep.UPLOAD_FILE,
    invalidCars: [],
    language: Certification2BSvsLanguages.PORTUGUESE,
    validCars: [],
    reportType: ReportType.CERTIFICATION_2BSVS,
    fileObjectUrl: "",
    fileMetadata: {
      name: "",
      type: "",
    },
  },
  error: "",
  loading: false,
};

export const certification2BSvs = createReducer(
  Certification2BSvsInitialState,
  (builder) => {
    builder.addCase(actionSetCertification2BSvsLanguage, (state, action) => {
      state.data.language = action.payload;
    });

    builder.addCase(actionSetCertification2BSvsInvalidCars, (state, action) => {
      state.data.invalidCars = action.payload;
    });

    builder.addCase(actionSetCertification2BSvsValidCars, (state, action) => {
      state.data.validCars = action.payload;
    });

    builder.addCase(actionSetCertification2BSvsFormStep, (state, action) => {
      state.data.formStep = action.payload;
    });

    builder.addCase(
      actionSetCertification2BSvsFileMetadata,
      (state, action) => {
        state.data.fileMetadata = action.payload;
      }
    );

    builder.addCase(actionResetCertification2BSvsState, (state) => {
      state.data = Certification2BSvsInitialState.data;
    });

    builder.addCase(actionResetCertification2BSvsFileMetadata, (state) => {
      state.data.fileMetadata = {
        name: "",
        type: "",
      };
    });

    builder.addCase(
      actionSetCertification2BSvsFileObjectUrl,
      (state, action) => {
        state.data.fileObjectUrl = action.payload;
      }
    );

    builder.addCase(
      thunkCreateCertification2BSvsReportRequest.pending,
      (state) => {
        state.error = "";
        state.loading = true;
      }
    );
    builder.addCase(
      thunkCreateCertification2BSvsReportRequest.fulfilled,
      (state) => {
        state.loading = false;
      }
    );
    builder.addCase(
      thunkCreateCertification2BSvsReportRequest.rejected,
      (state, action) => {
        state.error = JSON.stringify(action.payload);
        state.loading = false;
      }
    );
  }
);
