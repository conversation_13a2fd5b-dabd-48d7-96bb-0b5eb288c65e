import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  THUNK_TYPES,
  ThunkCreateCertification2BSvsReportRequestPayload,
} from "./types";
import { createFileReportRequest } from "@services";
import { RootState } from "@/store";

export const thunkCreateCertification2BSvsReportRequest = createAsyncThunk(
  THUNK_TYPES.CREATE_CERTIFICATION_2BSVS_REPORT_REQUEST,
  async (
    payload: ThunkCreateCertification2BSvsReportRequestPayload,
    thunkApi
  ) => {
    const { certification2BSvs } = thunkApi.getState() as RootState;
    const { reportType } = certification2BSvs.data;

    return createFileReportRequest({
      formData: payload.formData,
      report_type: reportType,
      signature: false,
      url: "/reports-requests/batch_upload",
    }).catch((err) => thunkApi.rejectWithValue(err));
  }
);
