import { ReportType } from "@types";

export enum Certification2BSvsLanguages {
  PORTUGUESE = "pt_BR",
  ENGLISH = "en",
  SPANISH = "es",
}

export enum THUNK_TYPES {
  CREATE_CERTIFICATION_2BSVS_REPORT_REQUEST = "@THUNK/CREATE/CERTIFICATION_2BSVS_REPORT_REQUEST",
}

export enum ACTION_TYPES {
  SET_LANGUAGE = "@ACTION/SET/CERTIFICATION_2BSVS_LANGUAGE",
  SET_INVALID_CARS = "@ACTION/SET/CERTIFICATION_2BSVS_INVALID_CARS",
  SET_VALID_CARS = "@ACTION/SET/CERTIFICATION_2BSVS_VALID_CARS",
  SET_FORM_STEP = "@ACTION/SET/CERTIFICATION_2BSVS_FORM_STEP",
  SET_FILE_METADATA = "@ACTION/SET/CERTIFICATION_2BSVS_FILE_METADATA",
  RESET_FILE_METADATA = "@ACTION/RESET/CERTIFICATION_2BSVS_FILE_METADATA",
  RESET_STATE = "@ACTION/RESET/CERTIFICATION_2BSVS_STATE",
  SET_FILE_OBJECT_URL = "@ACTION/SET/CERTIFICATION_2BSVS_FILE_OBJECT_URL",
}

export enum Certification2BSvsFormStep {
  UPLOAD_FILE = "UPLOAD_FILE",
  CAR_VALIDATION = "CAR_VALIDATION",
}

export type Certification2BSvsState = {
  data: {
    language: string;
    invalidCars: Array<string>;
    validCars: Array<string>;
    formStep: Certification2BSvsFormStep;
    reportType: ReportType;
    fileObjectUrl: string;
    fileMetadata: {
      name: string;
      type: string;
    };
  };
  error: string;
  loading: boolean;
};

export type ThunkCreateCertification2BSvsReportRequestPayload = {
  formData: FormData;
};
