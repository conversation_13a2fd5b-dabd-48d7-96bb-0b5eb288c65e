import { ReportType } from "@types";

export interface SoyDeforestationFormState {
  reportType:
    | ReportType.SOY_DEFORESTATION_CAR
    | ReportType.SOY_DEFORESTATION_MUNICIPALITY;
  selectedMunicipalities: { label: string; value: number }[];
  selectedCARs: string[];
  isLoading: boolean;
  error: string;
}

export enum ACTION_TYPES {
  SET_REPORT_TYPE = "@ACTION/SOY_DEFORESTATION/SET_REPORT_TYPE",
  SET_SELECTED_CARS = "@ACTION/SOY_DEFORESTATION/SET_SELECTED_CARS",
  SET_SELECTED_MUNICIPALITIES = "@ACTION/SOY_DEFORESTATION/SET_SELECTED_MUNICIPALITIES",
  RESET_STATE = "@ACTION/SOY_DEFORESTATION/RESET_STATE",
}

export enum THUNK_TYPES {
  CREATE_REQUEST = "@THUNK/SOY_DEFORESTATION/CREATE_REQUEST",
}
