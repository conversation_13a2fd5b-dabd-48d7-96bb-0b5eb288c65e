import { createAsyncThunk } from "@reduxjs/toolkit";
import { createReportExternalAPI } from "@services";
import { RootState } from "../../";
import { THUNK_TYPES } from "./types";
import { FileRequest } from "@types";

export const thunkCreateSoyDeforestationRequest = createAsyncThunk(
  THUNK_TYPES.CREATE_REQUEST,
  async (files: FileRequest[], thunkApi) => {
    const { reportType } = (thunkApi.getState() as RootState)
      .soybeanDeforestationForm;

    return Promise.all(
      files.map((fileRequest) => {
        const file = new File([fileRequest.file], fileRequest.name);
        return createReportExternalAPI(file, reportType);
      })
    ).catch((err) => thunkApi.rejectWithValue(err));
  }
);
