import { createAction } from "@reduxjs/toolkit";
import { ReportType } from "@types";
import { ACTION_TYPES } from "./types";

export const setReportTypeAction = createAction<
  ReportType.SOY_DEFORESTATION_CAR | ReportType.SOY_DEFORESTATION_MUNICIPALITY
>(ACTION_TYPES.SET_REPORT_TYPE);

export const setSelectedCARsAction = createAction<string[]>(
  ACTION_TYPES.SET_SELECTED_CARS
);

export const setSelectedMunicipalitiesAction = createAction<
  { value: number; label: string }[]
>(ACTION_TYPES.SET_SELECTED_MUNICIPALITIES);

export const resetStateAction = createAction(ACTION_TYPES.RESET_STATE);
