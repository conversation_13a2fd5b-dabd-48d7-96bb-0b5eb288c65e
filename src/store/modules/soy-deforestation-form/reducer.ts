import { createReducer, PayloadAction } from "@reduxjs/toolkit";
import { ReportType } from "@types";
import {
  resetStateAction,
  setReportTypeAction,
  setSelectedCARsAction,
  setSelectedMunicipalitiesAction,
} from "./actions";
import { thunkCreateSoyDeforestationRequest } from "./thunks";
import { SoyDeforestationFormState } from "./types";
import { AxiosError } from "axios";

export const GetSoyDeforestationFormInitialState: SoyDeforestationFormState = {
  reportType: ReportType.SOY_DEFORESTATION_CAR,
  selectedMunicipalities: [],
  selectedCARs: [],
  error: "",
  isLoading: false,
};

function setReportType(
  state: SoyDeforestationFormState,
  action: PayloadAction<
    ReportType.SOY_DEFORESTATION_CAR | ReportType.SOY_DEFORESTATION_MUNICIPALITY
  >
) {
  return Object.assign({}, state, { reportType: action.payload });
}

function setSelectedCARs(
  state: SoyDeforestationFormState,
  action: PayloadAction<string[]>
) {
  return Object.assign({}, state, { selectedCARs: action.payload });
}

function setSelectedMunicipalities(
  state: SoyDeforestationFormState,
  action: PayloadAction<{ value: number; label: string }[]>
) {
  return Object.assign({}, state, { selectedMunicipalities: action.payload });
}

function resetState(_state: SoyDeforestationFormState) {
  return GetSoyDeforestationFormInitialState;
}

export const soybeanDeforestationForm = createReducer(
  GetSoyDeforestationFormInitialState,
  (builder) => {
    builder.addCase(setReportTypeAction, setReportType);
    builder.addCase(setSelectedCARsAction, setSelectedCARs);
    builder.addCase(setSelectedMunicipalitiesAction, setSelectedMunicipalities);
    builder.addCase(thunkCreateSoyDeforestationRequest.pending, (state) => {
      state.isLoading = true;
      state.error = "";
    });
    builder.addCase(thunkCreateSoyDeforestationRequest.fulfilled, (state) => {
      state.isLoading = false;
      state.error = "";
    });
    builder.addCase(
      thunkCreateSoyDeforestationRequest.rejected,
      (state, action) => {
        state.isLoading = false;
        const payload = action.payload as any;
        if (payload && payload?.status === 400) {
          state.error =
            payload?.response?.data?.detail || "ERROR_CREATE_REPORT";
          return;
        }
        state.error = "ERROR_CREATE_REPORT";
      }
    );
    builder.addCase(resetStateAction, resetState);
  }
);
