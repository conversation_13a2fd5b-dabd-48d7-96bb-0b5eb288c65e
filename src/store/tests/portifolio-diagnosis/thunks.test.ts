import { thunkCreatePortifolioDiagnosisReportRequest } from "../../modules/portifolio-diagnosis/thunks";
import {
  createFileBatchReportRequest,
  createFileReportRequest,
} from "@/services";
import {
  THUNK_TYPES,
  ThunkCreatePortifolioDiagnosisReportRequestPayload,
} from "../../modules/portifolio-diagnosis/types";
import { RootState } from "../../../store";
import configureMockStore from "redux-mock-store";

import thunk from "redux-thunk";
import { Action } from "redux";
import { PortifolioDiagnosisInitialState } from "../../modules/portifolio-diagnosis/reducer";
import { ReportType } from "@types";

jest.mock("@services", () => ({
  createFileReportRequest: jest.fn(),
  createFileBatchReportRequest: jest.fn(),
}));

const middlewares = [thunk];
const mockStore = configureMockStore(middlewares);

describe("thunkCreatePortifolioDiagnosisReportRequest", () => {
  let store: ReturnType<typeof mockStore>;

  beforeEach(() => {
    jest.clearAllMocks();
    store = mockStore({ portifolioDiagnosis: PortifolioDiagnosisInitialState });
  });

  const payload: ThunkCreatePortifolioDiagnosisReportRequestPayload = {
    form: {} as FormData,
    reportType: ReportType.PORTFOLIO_DIAGNOSIS,
  };

  it("should dispatch fulfilled action when createFileBatchReportRequest is successful", async () => {
    (createFileReportRequest as jest.Mock).mockResolvedValue("success");

    const dispatch = jest.fn();
    const getState = jest.fn();
    const thunkApi = { dispatch, getState, rejectWithValue: jest.fn() };

    const result = await store.dispatch(
      thunkCreatePortifolioDiagnosisReportRequest(payload) as unknown as Action
    );

    expect(createFileReportRequest).toHaveBeenCalledWith({
      formData: {},
      url: "/reports-requests/portfolio_diagnosis",
    });
    const actions = store.getActions();

    expect(result.type).toBe(
      `${THUNK_TYPES.CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST}/fulfilled`
    );
    expect(thunkApi.rejectWithValue).not.toHaveBeenCalled();
    expect(actions[0].type).toEqual(
      `${THUNK_TYPES.CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST}/pending`
    );
    expect(actions[1].type).toEqual(
      `${THUNK_TYPES.CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST}/fulfilled`
    );
  });

  it("should dispatch rejected action when createFileBatchReportRequest fails", async () => {
    const error = new Error("Failed to create report");
    (createFileReportRequest as jest.Mock).mockRejectedValue(error);

    await store.dispatch(
      thunkCreatePortifolioDiagnosisReportRequest(payload) as unknown as Action
    );
    const actions = store.getActions();
    const state = store.getState() as RootState;

    expect(createFileReportRequest).toHaveBeenCalledWith({
      formData: {},
      url: "/reports-requests/portfolio_diagnosis",
    });
    expect(actions[1].type).toBe(
      `${THUNK_TYPES.CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST}/rejected`
    );
    expect(state.portifolioDiagnosis.loading).toEqual(false);
  });
});
