import {
  portifolioDiagnosis,
  PortifolioDiagnosisInitialState,
} from "../../modules/portifolio-diagnosis/reducer";
import { ReportType, RequestMethod, SubModule } from "../../../types";
import {
  PortifolioDiagnosisState,
  ReportPortifolioDiagnosisBatchDocumentType,
  ReportPortifolioDiagnosisManualSelectType,
} from "../../modules/portifolio-diagnosis/types";
import {
  actionSearchPortifolioDiagnosisMunicipality,
  actionSearchPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisMunicipalitiesSelected,
  actionSetPortifolioDiagnosisRequestMethod,
  actionSetPortifolioDiagnosisStatesSelected,
  actionSetPortifolioDiagnosisBatchDocumentType,
  actionSetPortifolioDiagnosisBatchDocumentList,
  actionSetPortifolioDiagnosisBatchInvalidDocuments,
  actionResetPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisManualSelectType,
  actionSetPortifolioDiagnosisBatchFileDetails,
  actionSetPortifolioDiagnosisManualSubModules,
  actionSetPortifolioDiagnosisManualSubModulesSelected,
} from "../../modules/portifolio-diagnosis/actions";
import { thunkCreatePortifolioDiagnosisReportRequest } from "../../modules/portifolio-diagnosis/thunks";
import { THUNK_TYPES } from "../../modules/portifolio-diagnosis/types";

describe("portifolioDiagnosis reducer", () => {
  const initialState: PortifolioDiagnosisState = {
    data: {
      requestMethod: RequestMethod.MANUAL,
      reportType: ReportType.PORTFOLIO_DIAGNOSIS,
      manual: {
        subModules: [],
        subModulesSelected: [],
        municipaliciesSelected: [
          { uf: "SP", name: "São Paulo", ibgeCode: 3550308 },
          { uf: "RJ", name: "Rio de Janeiro", ibgeCode: 3304557 },
        ],
        statesSelected: [
          { uf: "SP", name: "São Paulo" },
          { uf: "RJ", name: "Rio de Janeiro" },
        ],
        searchMunicipality: "São Paulo",
        searchState: "SP",
        municipalities: [
          { uf: "SP", name: "São Paulo", ibge_code: 3550308 },
          { uf: "RJ", name: "Rio de Janeiro", ibge_code: 3304557 },
        ],
        states: [
          { uf: "SP", name: "São Paulo" },
          { uf: "RJ", name: "Rio de Janeiro" },
        ],
        selectType: ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY,
      },
      batch: {
        subModulesAvailable: [],
        documentType: ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ,
        documentsList: ["12345678901", "98765432100"],
        invalidDocuments: ["00000000000"],
        fileDetails: {
          keyName: "keyname",
          name: "documents.csv",
          type: "text/csv",
        },
      },
    },
    loading: false,
    error: "",
  };
  it("should handle actionSetPortifolioDiagnosisRequestMethod", () => {
    const action = actionSetPortifolioDiagnosisRequestMethod(
      RequestMethod.BATCH
    );
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.requestMethod).toBe(RequestMethod.BATCH);
  });

  it("should handle actionSetPortifolioDiagnosisMunicipalitiesSelected", () => {
    const selectedMunicipalities = [
      { ibgeCode: 123456, name: "municipality-1", uf: "m1" },
      { ibgeCode: 789123, name: "municipality-2", uf: "m2" },
    ];
    const action = actionSetPortifolioDiagnosisMunicipalitiesSelected(
      selectedMunicipalities
    );
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.municipaliciesSelected).toEqual(
      selectedMunicipalities
    );
  });

  it("should handle actionSetPortifolioDiagnosisStatesSelected", () => {
    const selectedStates = [
      {
        name: "state-1",
        uf: "s1",
      },
      {
        name: "state-2",
        uf: "s2",
      },
    ];
    const action = actionSetPortifolioDiagnosisStatesSelected(selectedStates);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.statesSelected).toEqual(selectedStates);
  });

  it("should handle actionSearchPortifolioDiagnosisMunicipality", () => {
    const searchMunicipality = "SomeMunicipality";
    const action =
      actionSearchPortifolioDiagnosisMunicipality(searchMunicipality);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.searchMunicipality).toBe(searchMunicipality);
  });

  it("should handle actionSearchPortifolioDiagnosisState", () => {
    const searchState = "SomeState";
    const action = actionSearchPortifolioDiagnosisState(searchState);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.searchState).toBe(searchState);
  });

  it("should handle actionSetPortifolioDiagnosisBatchDocumentType", () => {
    const documentType = ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ;
    const action = actionSetPortifolioDiagnosisBatchDocumentType(documentType);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.batch.documentType).toBe(documentType);
  });

  it("should handle actionSetPortifolioDiagnosisBatchDocumentList", () => {
    const documentsList = ["Document1", "Document2"];
    const action = actionSetPortifolioDiagnosisBatchDocumentList(documentsList);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.batch.documentsList).toEqual(documentsList);
  });

  it("should handle actionSetPortifolioDiagnosisBatchInvalidDocuments", () => {
    const invalidDocuments = ["InvalidDocument1", "InvalidDocument2"];
    const action =
      actionSetPortifolioDiagnosisBatchInvalidDocuments(invalidDocuments);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.batch.invalidDocuments).toEqual(invalidDocuments);
  });

  it("should handle actionResetPortifolioDiagnosisState", () => {
    const action = actionResetPortifolioDiagnosisState();
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState).toEqual(PortifolioDiagnosisInitialState);
  });

  it("should handle actionSetPortifolioDiagnosisManualSelectType", () => {
    const selectType = ReportPortifolioDiagnosisManualSelectType.STATE;
    const action = actionSetPortifolioDiagnosisManualSelectType(selectType);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.selectType).toBe(selectType);
  });

  it("should handle actionSetPortifolioDiagnosisBatchFileDetails", () => {
    const fileDetails = {
      name: "file.txt",
      type: "text/plain",
      keyName: "keyName",
    };
    const action = actionSetPortifolioDiagnosisBatchFileDetails(fileDetails);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.batch.fileDetails).toEqual(fileDetails);
  });

  it("should alter state of loading if thunk is pending", async () => {
    const reducer = portifolioDiagnosis(
      initialState,
      thunkCreatePortifolioDiagnosisReportRequest.pending
    );
    expect(reducer.loading).toEqual(true);
  });
  it("should alter state of loading if thunk is sucessfull", async () => {
    const reducer = portifolioDiagnosis(
      initialState,
      thunkCreatePortifolioDiagnosisReportRequest.fulfilled
    );
    expect(reducer.loading).toEqual(false);
  });

  it("should alter state of loading if thunk is rejected", async () => {
    const expected = { message: "error" };
    const reducer = portifolioDiagnosis(initialState, {
      type:
        THUNK_TYPES.CREATE_PORTIFOLIO_DIAGNOSIS_REPORT_REQUEST + "/rejected",
      payload: expected,
    });
    expect(reducer.loading).toEqual(false);
    expect(reducer.error).toEqual(JSON.stringify({ message: "error" }));
  });

  /***
   *
   *
   *
   *
   *
   *
   */

  it("should handle actionSetPortifolioDiagnosisManualSubModules", () => {
    const payload: SubModule[] = [
      {
        type: "1",
        active: true,
        description: "description1",
        limit: "1",
        module_id: 1,
        report_type: "report-type1",
      },
      {
        type: "2",
        active: true,
        description: "description2",
        limit: "1",
        module_id: 2,
        report_type: "report-type2",
      },
    ];
    const action = actionSetPortifolioDiagnosisManualSubModules(payload);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.subModules).toEqual(payload);
  });

  it("should handle actionSetPortifolioDiagnosisManualSubModulesSelected", () => {
    const payload: SubModule[] = [
      {
        type: "1",
        active: true,
        description: "description1",
        limit: "1",
        module_id: 1,
        report_type: "report-type1",
      },
      {
        type: "2",
        active: true,
        description: "description2",
        limit: "1",
        module_id: 2,
        report_type: "report-type2",
      },
    ];
    const action =
      actionSetPortifolioDiagnosisManualSubModulesSelected(payload);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.subModulesSelected).toEqual(payload);
  });

  it("should not update state if payload of  actionSetPortifolioDiagnosisManualSubModules is empty", () => {
    const payload = null;
    const action = actionSetPortifolioDiagnosisManualSubModules(payload);
    const newState = portifolioDiagnosis(
      PortifolioDiagnosisInitialState,
      action
    );
    expect(newState.data.manual.subModules).toEqual([]);
  });
});
