import {
  ACTION_TYPES,
  ReportPortifolioDiagnosisBatchDocumentType,
  ReportPortifolioDiagnosisManualSelectType,
} from "../../modules/portifolio-diagnosis/types";
import { RequestMethod } from "../../../types";
import {
  actionSetPortifolioDiagnosisRequestMethod,
  actionSetPortifolioDiagnosisManualSelectType,
  actionSetPortifolioDiagnosisMunicipalitiesSelected,
  actionSetPortifolioDiagnosisStatesSelected,
  actionSearchPortifolioDiagnosisMunicipality,
  actionSearchPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisBatchDocumentType,
  actionSetPortifolioDiagnosisBatchDocumentList,
  actionSetPortifolioDiagnosisBatchInvalidDocuments,
  actionResetPortifolioDiagnosisState,
  actionSetPortifolioDiagnosisBatchFileDetails,
} from "../../modules/portifolio-diagnosis/actions";

describe("Portifolio Diagnosis Actions", () => {
  it("should create an action to set request method", () => {
    const requestMethod: RequestMethod = RequestMethod.BATCH;
    const expectedAction = {
      type: ACTION_TYPES.SET_REQUEST_METHOD,
      payload: requestMethod,
    };
    expect(actionSetPortifolioDiagnosisRequestMethod(requestMethod)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set manual select type", () => {
    const manualSelectType: ReportPortifolioDiagnosisManualSelectType =
      ReportPortifolioDiagnosisManualSelectType.MUNICIPALITY;
    const expectedAction = {
      type: ACTION_TYPES.SET_MANUAL_SELECT_TYPE,
      payload: manualSelectType,
    };
    expect(
      actionSetPortifolioDiagnosisManualSelectType(manualSelectType)
    ).toEqual(expectedAction);
  });

  it("should create an action to set municipalities selected", () => {
    const municipalities = [{ uf: "SP", name: "São Paulo", ibgeCode: 3550308 }];
    const expectedAction = {
      type: ACTION_TYPES.SET_MUNICIPALITIES,
      payload: municipalities,
    };
    expect(
      actionSetPortifolioDiagnosisMunicipalitiesSelected(municipalities)
    ).toEqual(expectedAction);
  });

  it("should create an action to set states selected", () => {
    const states = [{ uf: "SP", name: "São Paulo" }];
    const expectedAction = {
      type: ACTION_TYPES.SET_STATES,
      payload: states,
    };
    expect(actionSetPortifolioDiagnosisStatesSelected(states)).toEqual(
      expectedAction
    );
  });

  it("should create an action to search municipality", () => {
    const searchTerm = "São Paulo";
    const expectedAction = {
      type: ACTION_TYPES.SEARCH_MUNICIPALITY,
      payload: searchTerm,
    };
    expect(actionSearchPortifolioDiagnosisMunicipality(searchTerm)).toEqual(
      expectedAction
    );
  });

  it("should create an action to search state", () => {
    const searchTerm = "São Paulo";
    const expectedAction = {
      type: ACTION_TYPES.SEARCH_STATE,
      payload: searchTerm,
    };
    expect(actionSearchPortifolioDiagnosisState(searchTerm)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set batch document type", () => {
    const documentType = ReportPortifolioDiagnosisBatchDocumentType.CAR;
    const expectedAction = {
      type: ACTION_TYPES.SET_BATCH_DOCUMENT_TYPE,
      payload: documentType,
    };
    expect(actionSetPortifolioDiagnosisBatchDocumentType(documentType)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set batch document list", () => {
    const documentList = ["doc1", "doc2"];
    const expectedAction = {
      type: ACTION_TYPES.SET_BATCH_DOCUMENT_LIST,
      payload: documentList,
    };
    expect(actionSetPortifolioDiagnosisBatchDocumentList(documentList)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set batch invalid documents", () => {
    const invalidDocuments = ["invalidDoc1", "invalidDoc2"];
    const expectedAction = {
      type: ACTION_TYPES.SET_BATCH_INVALID_DOCUMENTS,
      payload: invalidDocuments,
    };
    expect(
      actionSetPortifolioDiagnosisBatchInvalidDocuments(invalidDocuments)
    ).toEqual(expectedAction);
  });

  it("should create an action to reset state", () => {
    const expectedAction = {
      type: ACTION_TYPES.RESET_STATE,
    };
    expect(actionResetPortifolioDiagnosisState()).toEqual(expectedAction);
  });

  it("should create an action to set batch file details", () => {
    const fileDetails = { name: "file1", type: "csv", keyName: "keyname" };
    const expectedAction = {
      type: ACTION_TYPES.SET_BATCH_FILE_DETAILS,
      payload: fileDetails,
    };
    expect(actionSetPortifolioDiagnosisBatchFileDetails(fileDetails)).toEqual(
      expectedAction
    );
  });
});
