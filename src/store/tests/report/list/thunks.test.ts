import { thunkListReport } from "../../../modules/report/list/thunks";
import { store } from "../../../";
import { listReports } from "../../../../services";

jest.mock("../../../../services/reports");

describe("thunkListReportRequest", () => {
  it("should handle with thunk list reports fullfield", async () => {
    const payload = { count: 0, items: [] };
    (listReports as jest.Mock).mockResolvedValue(payload);

    await store.dispatch(thunkListReport({}));

    const state = store.getState();

    expect(state.listReport.data).toEqual(payload);
  });

  it("should handle with thunk list reports rejects", async () => {
    const error = { status: 400, message: "error" };

    (listReports as jest.Mock).mockRejectedValue(error);

    await store.dispatch(thunkListReport({}));

    const state = store.getState();

    expect(state.listReport.error).toEqual(error);
  });
});
