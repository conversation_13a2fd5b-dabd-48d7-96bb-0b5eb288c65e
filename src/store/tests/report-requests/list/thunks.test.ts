import { thunkListReportRequest } from "../../../modules/report-requests/list/thunks";
import { listReportRequests } from "../../../../services/report-request";
import { store } from "../../../";

jest.mock("../../../../services/report-request");

describe("thunkListReportRequest", () => {
  it("should handle with thunk list report requests fullfield", async () => {
    const payload = { count: 0, items: [], reports_count: 0 };
    (listReportRequests as jest.Mock).mockResolvedValue(payload);

    await store.dispatch(thunkListReportRequest({}));

    const state = store.getState();

    expect(state.listReportRequests.data).toEqual(payload);
  });

  it("should handle with thunk list report requests rejects", async () => {
    const error = { status: 400, message: "error" };

    (listReportRequests as jest.Mock).mockRejectedValue(error);

    await store.dispatch(thunkListReportRequest({}));

    const state = store.getState();

    expect(state.listReportRequests.error).toEqual(error);
  });
});
