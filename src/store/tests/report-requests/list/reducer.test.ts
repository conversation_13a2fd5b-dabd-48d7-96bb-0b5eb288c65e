import { listReportRequests } from "../../../modules/report-requests/list/reducer";
import { ListReportRequestsState } from "../../../modules/report-requests/list/types";
import { thunkListReportRequest } from "../../../modules/report-requests/list/thunks";
import { AnyAction } from "@reduxjs/toolkit";
import {
  actionResetReportRequestListFiltersData,
  actionSetReportRequestListFiltersData,
  actionSetReportRequestListPaginationData,
} from "../../../modules/report-requests/list/actions";

describe("ListReportRequestsReducer", () => {
  const initialState: ListReportRequestsState = {
    data: {
      count: 0,
      items: [],
      reports_count: 0,
    },
    filters: {},
    pagination: {
      limit: 10,
      offset: 0,
      pages: 0,
      total: 0,
      totalPages: 0,
    },
    loading: false,
    error: "",
    isApiFetched: false,
  };

  test("should return the initial state", () => {
    const result = listReportRequests(undefined, {} as AnyAction);
    expect(result).toEqual(initialState);
  });

  test("should handle pending state", () => {
    const action = {
      type: thunkListReportRequest.pending.type,
      meta: {
        arg: {
          offset: 0,
          limit: 10,
        },
      },
    };
    const result = listReportRequests(initialState, action);
    expect(result.loading).toBe(true);
  });

  test("should handle fulfilled state", () => {
    const action = {
      type: thunkListReportRequest.fulfilled.type,
      payload: {
        count: 1,
        items: [{ id: 1, name: "Report 1" }],
        reports_count: 1,
      },
      meta: {
        arg: {
          offset: 0,
          limit: 10,
        },
      },
    };
    const result = listReportRequests(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toBe("");
    expect(result.data).toEqual(action.payload);
  });

  test("should handle rejected state", () => {
    const action = {
      type: thunkListReportRequest.rejected.type,
      payload: "Error fetching report requests",
    };
    const result = listReportRequests(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toBe(action.payload);
  });

  test("should handle setting filters", () => {
    const action = {
      type: actionSetReportRequestListFiltersData.type,
      payload: { filters: { status: "completed" } },
    };
    const result = listReportRequests(initialState, action);
    expect(result.filters).toEqual(action.payload.filters);
  });

  test("should handle setting pagination", () => {
    const action = {
      type: actionSetReportRequestListPaginationData.type,
      payload: { pagination: { limit: 20, offset: 20 } },
    };
    const result = listReportRequests(initialState, action);
    expect(result.pagination).toEqual(
      expect.objectContaining(action.payload.pagination)
    );
  });

  test("should reset filters to original value", () => {
    const action = {
      type: actionResetReportRequestListFiltersData.type,
    };
    const result = listReportRequests(
      { ...initialState, filters: { report_type: "ANY_REPORT_TYPE" } },
      action
    );
    expect(result.filters).toEqual({});
  });
});
