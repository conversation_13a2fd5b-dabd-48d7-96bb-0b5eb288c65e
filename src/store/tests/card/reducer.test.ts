import { AnyAction } from "@reduxjs/toolkit";
import { ListCardState } from "../../modules/card/list/types";
import { cardList } from "../../modules/card/list/reducer";
import { thunkListCard } from "../../modules/card/list/thunks";

describe("CardListReducer", () => {
  const initialState: ListCardState = {
    data: [],
    error: "",
    loading: false,
  };

  test("should return the initial state", () => {
    const result = cardList(undefined, {} as AnyAction);
    expect(result).toEqual(initialState);
  });

  test("should handle pending state", () => {
    const action = { type: thunkListCard.pending.type };
    const result = cardList(initialState, action);
    expect(result.loading).toBe(true);
  });

  test("should handle fulfilled state", () => {
    const action = {
      type: thunkListCard.fulfilled.type,
      payload: [{ id: 1 }],
    };
    const result = cardList(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toBe("");
    expect(result.data).toEqual(action.payload);
  });

  test("should handle rejected state", () => {
    const action = {
      type: thunkListCard.rejected.type,
      payload: "Error fetching account details",
    };
    const result = cardList(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toBe(action.payload);
  });
});
