import { store } from "../../";
import { thunkListCard } from "../../modules/card/list/thunks";
import { listCards } from "../../../services/cards";

jest.mock("../../../services/cards");

describe("thunkListReportRequest", () => {
  it("should handle with thunk list cards fullfield", async () => {
    const payload = { count: 0, items: [] };
    (listCards as jest.Mock).mockResolvedValue(payload);

    await store.dispatch(thunkListCard());

    const state = store.getState();

    expect(state.cardList.data).toEqual(payload);
  });

  it("should handle with thunk list cards rejects", async () => {
    const error = { status: 400, message: "error" };

    (listCards as jest.Mock).mockRejectedValue(error);

    await store.dispatch(thunkListCard());

    const state = store.getState();

    expect(state.cardList.error).toEqual(error);
  });
});
