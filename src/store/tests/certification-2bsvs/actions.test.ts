import {
  ACTION_TYPES,
  Certification2BSvsFormStep,
  Certification2BSvsLanguages,
} from "../../modules/certification-2bsvs/types";
import {
  actionSetCertification2BSvsLanguage,
  actionSetCertification2BSvsInvalidCars,
  actionSetCertification2BSvsValidCars,
  actionSetCertification2BSvsFormStep,
  actionSetCertification2BSvsFileMetadata,
  actionResetCertification2BSvsFileMetadata,
  actionResetCertification2BSvsState,
} from "../../modules/certification-2bsvs/actions";

describe("Certification 2BSvs Actions", () => {
  it("should create an action to set the language", () => {
    const language: Certification2BSvsLanguages =
      "en" as Certification2BSvsLanguages;
    const expectedAction = {
      type: ACTION_TYPES.SET_LANGUAGE,
      payload: language,
    };
    expect(actionSetCertification2BSvsLanguage(language)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set invalid cars", () => {
    const invalidCars = ["car1", "car2"];
    const expectedAction = {
      type: ACTION_TYPES.SET_INVALID_CARS,
      payload: invalidCars,
    };
    expect(actionSetCertification2BSvsInvalidCars(invalidCars)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set valid cars", () => {
    const validCars = ["car3", "car4"];
    const expectedAction = {
      type: ACTION_TYPES.SET_VALID_CARS,
      payload: validCars,
    };
    expect(actionSetCertification2BSvsValidCars(validCars)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set the form step", () => {
    const formStep: Certification2BSvsFormStep =
      Certification2BSvsFormStep.CAR_VALIDATION;
    const expectedAction = {
      type: ACTION_TYPES.SET_FORM_STEP,
      payload: formStep,
    };
    expect(actionSetCertification2BSvsFormStep(formStep)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set file metadata", () => {
    const fileMetadata = { name: "file.txt", type: "text/plain" };
    const expectedAction = {
      type: ACTION_TYPES.SET_FILE_METADATA,
      payload: fileMetadata,
    };
    expect(actionSetCertification2BSvsFileMetadata(fileMetadata)).toEqual(
      expectedAction
    );
  });

  it("should create an action to reset file metadata", () => {
    const expectedAction = {
      type: ACTION_TYPES.RESET_FILE_METADATA,
    };
    expect(actionResetCertification2BSvsFileMetadata()).toEqual(expectedAction);
  });

  it("should create an action to reset the state", () => {
    const expectedAction = {
      type: ACTION_TYPES.RESET_STATE,
    };
    expect(actionResetCertification2BSvsState()).toEqual(expectedAction);
  });
});
