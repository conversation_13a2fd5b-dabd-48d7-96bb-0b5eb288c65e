import { certification2BSvs } from "../../modules/certification-2bsvs/reducer";
import { ReportType } from "@types";
import {
  Certification2BSvsLanguages,
  Certification2BSvsFormStep,
} from "../../modules/certification-2bsvs/types";
import { thunkCreateCertification2BSvsReportRequest } from "../../modules/certification-2bsvs/thunks";
import {
  actionResetCertification2BSvsFileMetadata,
  actionSetCertification2BSvsFileMetadata,
  actionSetCertification2BSvsFormStep,
  actionSetCertification2BSvsInvalidCars,
  actionSetCertification2BSvsLanguage,
  actionSetCertification2BSvsValidCars,
  actionResetCertification2BSvsState,
  actionSetCertification2BSvsFileObjectUrl,
} from "@/store/modules/certification-2bsvs/actions";

describe("certification2BSvs reducer", () => {
  const initialState = {
    data: {
      formStep: Certification2BSvsFormStep.UPLOAD_FILE,
      invalidCars: [],
      language: Certification2BSvsLanguages.PORTUGUESE,
      validCars: [],
      fileObjectUrl: "",
      reportType: ReportType.CERTIFICATION_2BSVS,
      fileMetadata: {
        name: "",
        type: "",
      },
    },
    error: "",
    loading: false,
  };

  it("should handle actionSetCertification2BSvsLanguage", () => {
    const action = actionSetCertification2BSvsLanguage(
      Certification2BSvsLanguages.ENGLISH
    );
    const state = certification2BSvs(initialState, action);
    expect(state.data.language).toBe(Certification2BSvsLanguages.ENGLISH);
  });

  it("should handle actionSetCertification2BSvsInvalidCars", () => {
    const invalidCars = ["car1", "car2"];
    const action = actionSetCertification2BSvsInvalidCars(invalidCars);
    const state = certification2BSvs(initialState, action);
    expect(state.data.invalidCars).toEqual(invalidCars);
  });

  it("should handle actionSetCertification2BSvsValidCars", () => {
    const validCars = ["car3", "car4"];
    const action = actionSetCertification2BSvsValidCars(validCars);
    const state = certification2BSvs(initialState, action);
    expect(state.data.validCars).toEqual(validCars);
  });

  it("should handle actionSetCertification2BSvsFormStep", () => {
    const action = actionSetCertification2BSvsFormStep(
      Certification2BSvsFormStep.CAR_VALIDATION
    );
    const state = certification2BSvs(initialState, action);
    expect(state.data.formStep).toBe(Certification2BSvsFormStep.CAR_VALIDATION);
  });

  it("should handle actionSetCertification2BSvsFileMetadata", () => {
    const fileMetadata = { name: "file.txt", type: "text/plain" };
    const action = actionSetCertification2BSvsFileMetadata(fileMetadata);
    const state = certification2BSvs(initialState, action);
    expect(state.data.fileMetadata).toEqual(fileMetadata);
  });

  it("should handle actionResetCertification2BSvsState", () => {
    const modifiedState = {
      ...initialState,
      data: {
        ...initialState.data,
        language: Certification2BSvsLanguages.ENGLISH,
      },
    };
    const action = actionResetCertification2BSvsState();
    const state = certification2BSvs(modifiedState, action);
    expect(state.data).toEqual(initialState.data);
  });

  it("should handle actionResetCertification2BSvsFileMetadata", () => {
    const modifiedState = {
      ...initialState,
      data: {
        ...initialState.data,
        fileMetadata: { name: "file.txt", type: "text/plain" },
      },
    };
    const action = actionResetCertification2BSvsFileMetadata();
    const state = certification2BSvs(modifiedState, action);
    expect(state.data.fileMetadata).toEqual({ name: "", type: "" });
  });

  it("should handle actionSetCertification2BSvsLanguage", () => {
    const action = actionSetCertification2BSvsLanguage(
      Certification2BSvsLanguages.ENGLISH
    );
    const state = certification2BSvs(initialState, action);
    expect(state.data.language).toBe(Certification2BSvsLanguages.ENGLISH);
  });

  it("should handle actionSetCertification2BSvsInvalidCars", () => {
    const invalidCars = ["car1", "car2"];
    const action = actionSetCertification2BSvsInvalidCars(invalidCars);
    const state = certification2BSvs(initialState, action);
    expect(state.data.invalidCars).toEqual(invalidCars);
  });

  it("should handle actionSetCertification2BSvsValidCars", () => {
    const validCars = ["car3", "car4"];
    const action = actionSetCertification2BSvsValidCars(validCars);
    const state = certification2BSvs(initialState, action);
    expect(state.data.validCars).toEqual(validCars);
  });

  it("should handle actionSetCertification2BSvsFormStep", () => {
    const action = actionSetCertification2BSvsFormStep(
      Certification2BSvsFormStep.CAR_VALIDATION
    );
    const state = certification2BSvs(initialState, action);
    expect(state.data.formStep).toBe(Certification2BSvsFormStep.CAR_VALIDATION);
  });

  it("should handle actionSetCertification2BSvsFileMetadata", () => {
    const fileMetadata = { name: "file.txt", type: "text/plain" };
    const action = actionSetCertification2BSvsFileMetadata(fileMetadata);
    const state = certification2BSvs(initialState, action);
    expect(state.data.fileMetadata).toEqual(fileMetadata);
  });

  it("should handle actionResetCertification2BSvsState", () => {
    const modifiedState = {
      ...initialState,
      data: {
        ...initialState.data,
        language: Certification2BSvsLanguages.ENGLISH,
      },
    };
    const action = actionResetCertification2BSvsState();
    const state = certification2BSvs(modifiedState, action);
    expect(state.data).toEqual(initialState.data);
  });

  it("should handle actionResetCertification2BSvsFileMetadata", () => {
    const modifiedState = {
      ...initialState,
      data: {
        ...initialState.data,
        fileMetadata: { name: "file.txt", type: "text/plain" },
      },
    };
    const action = actionResetCertification2BSvsFileMetadata();
    const state = certification2BSvs(modifiedState, action);
    expect(state.data.fileMetadata).toEqual({ name: "", type: "" });
  });

  it("should handle thunkCreateCertification2BSvsReportRequest.pending", () => {
    const action = {
      type: thunkCreateCertification2BSvsReportRequest.pending.type,
    };
    const state = certification2BSvs(initialState, action);
    expect(state.loading).toBe(true);
    expect(state.error).toBe("");
  });

  it("should handle thunkCreateCertification2BSvsReportRequest.fulfilled", () => {
    const action = {
      type: thunkCreateCertification2BSvsReportRequest.fulfilled.type,
    };
    const state = certification2BSvs(initialState, action);
    expect(state.loading).toBe(false);
  });

  it("should handle thunkCreateCertification2BSvsReportRequest.rejected", () => {
    const error = "Some error";
    const action = {
      type: thunkCreateCertification2BSvsReportRequest.rejected.type,
      payload: error,
    };
    const state = certification2BSvs(initialState, action);
    expect(state.loading).toBe(false);
    expect(state.error).toBe(JSON.stringify(error));
  });

  it("should set fileObjectUrl when actionSetCertification2BSvsFileObjectUrl is called", () => {
    const fileObjectUrl = "file-url";
    const action = actionSetCertification2BSvsFileObjectUrl(fileObjectUrl);
    const state = certification2BSvs(initialState, action);
    expect(state.data.fileObjectUrl).toEqual(fileObjectUrl);
  });
});
