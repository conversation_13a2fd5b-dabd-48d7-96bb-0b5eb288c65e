import { configureStore } from "@reduxjs/toolkit";
import thunk from "redux-thunk";
import { createFileReportRequest } from "@services";
import { thunkCreateCertification2BSvsReportRequest } from "../../modules/certification-2bsvs/thunks";
import {
  Certification2BSvsState,
  THUNK_TYPES,
  ThunkCreateCertification2BSvsReportRequestPayload,
} from "../../modules/certification-2bsvs/types";
import { ReportType } from "@types";

jest.mock("@services");

const mockCreateFileReportRequest =
  createFileReportRequest as jest.MockedFunction<
    typeof createFileReportRequest
  >;

describe("thunkCreateCertification2BSvsReportRequest", () => {
  const initialState: Certification2BSvsState = {
    data: {
      reportType: ReportType.CERTIFICATION_2BSVS,
    },
  } as Certification2BSvsState;

  const store = configureStore({
    reducer: (state = { certification2BSvs: initialState }) => state,
    middleware: [thunk],
  });

  it("dispatches fulfilled action when createFileReportRequest succeeds", async () => {
    const payload: ThunkCreateCertification2BSvsReportRequestPayload = {
      formData: new FormData(),
    };
    mockCreateFileReportRequest.mockResolvedValueOnce("success");

    const result = await store.dispatch(
      thunkCreateCertification2BSvsReportRequest(payload) as any
    );

    expect(result.type).toBe(
      `${THUNK_TYPES.CREATE_CERTIFICATION_2BSVS_REPORT_REQUEST}/fulfilled`
    );
    expect(mockCreateFileReportRequest).toHaveBeenCalledWith({
      formData: payload.formData,
      report_type: initialState.data.reportType,
      signature: false,
      url: "/reports-requests/batch_upload",
    });
  });

  it("dispatches rejected action when createFileReportRequest fails", async () => {
    const payload = { formData: new FormData() };
    const error = new Error("Request failed");
    mockCreateFileReportRequest.mockRejectedValueOnce(error);

    const result = await store.dispatch(
      thunkCreateCertification2BSvsReportRequest(payload) as any
    );

    expect(result.type).toBe(
      `${THUNK_TYPES.CREATE_CERTIFICATION_2BSVS_REPORT_REQUEST}/rejected`
    );
    expect(result.payload).toBe(error);
    expect(mockCreateFileReportRequest).toHaveBeenCalledWith({
      formData: payload.formData,
      report_type: initialState.data.reportType,
      signature: false,
      url: "/reports-requests/batch_upload",
    });
  });
});
