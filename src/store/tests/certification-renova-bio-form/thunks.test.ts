import { thunkCreateRenovaBioReportRequest } from "../../modules/certification-renova-bio-form/thunks";
import { addTagToRequest, createFileBatchReportRequest, createTags } from "../../../services";
import {
  THUNK_TYPES,
  ThunkCreateRenovaBioReportRequestPayload,
} from "../../modules/certification-renova-bio-form/types";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import { Action } from "redux";
import { ReportType } from "@types";

jest.mock("@services");

const middlewares = [thunk];
const mockStore = configureMockStore(middlewares);

describe("thunkCreateRenovaBioReportRequest", () => {
  let store: ReturnType<typeof mockStore>;

  beforeEach(() => {
    store = mockStore({});
  });

  it("dispatches the correct actions on successful request", async () => {
    const payload: ThunkCreateRenovaBioReportRequestPayload = {
      reportType: ReportType.RENOVABIO_CERTIFICATION,
      formData: {} as FormData,
      tagsList: [],
      selectedTags: [],
    };
    (createFileBatchReportRequest as jest.Mock).mockResolvedValue({
      data: "success",
    });

    await store.dispatch(
      thunkCreateRenovaBioReportRequest(payload) as unknown as Action
    );

    const actions = store.getActions();
    expect(actions[0].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/pending`
    );
    expect(actions[1].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/fulfilled`
    );
    expect(actions[1].payload).toEqual({ data: "success" });
  });

  it("dispatches the correct actions on failed request", async () => {
    const payload: ThunkCreateRenovaBioReportRequestPayload = {
      reportType: ReportType.RENOVABIO_CERTIFICATION,
      formData: {} as FormData,
      tagsList: [],
      selectedTags: [],
    };
    const error = new Error("Request failed");
    (createFileBatchReportRequest as jest.Mock).mockRejectedValue(error);

    await store.dispatch(
      thunkCreateRenovaBioReportRequest(payload) as unknown as Action
    );

    const actions = store.getActions();
    expect(actions[0].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/pending`
    );
    expect(actions[1].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/rejected`
    );
    expect(actions[1].payload).toEqual(error);
  });

  it("handles adding new tags and associating them with the request", async () => {
    const payload: ThunkCreateRenovaBioReportRequestPayload = {
      reportType: ReportType.RENOVABIO_CERTIFICATION,
      formData: {} as FormData,
      tagsList: [
        {
          id: 1,
          name: "existingTag",
          account: 1,
          created: new Date(),
          modified: new Date(),
        },
      ],
      selectedTags: ["existingTag", "newTag1", "newTag2"],
    };

    (createFileBatchReportRequest as jest.Mock).mockResolvedValue({
      id: 123,
    });
    const mockCreateTags = jest.fn().mockResolvedValue([
      { id: 2, name: "newTag1" },
      { id: 3, name: "newTag2" },
    ]);
    const mockAddTagToRequest = jest.fn().mockResolvedValue(null);

    jest.mocked(createTags).mockImplementation(mockCreateTags);
    jest.mocked(addTagToRequest).mockImplementation(mockAddTagToRequest);

    await store.dispatch(
      thunkCreateRenovaBioReportRequest(payload) as unknown as Action
    );

    const actions = store.getActions();
    expect(actions[0].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/pending`
    );
    expect(actions[1].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/fulfilled`
    );
    expect(actions[1].payload).toEqual({ id: 123 });

    expect(mockCreateTags).toHaveBeenCalledWith(["newTag1", "newTag2"]);
    expect(mockAddTagToRequest).toHaveBeenCalledTimes(3);
    expect(mockAddTagToRequest).toHaveBeenCalledWith(123, 1); // existingTag
    expect(mockAddTagToRequest).toHaveBeenCalledWith(123, 2); // newTag1
    expect(mockAddTagToRequest).toHaveBeenCalledWith(123, 3); // newTag2
  });

  it("does not call addTagToRequest if no tags are selected", async () => {
    const payload: ThunkCreateRenovaBioReportRequestPayload = {
      reportType: ReportType.RENOVABIO_CERTIFICATION,
      formData: {} as FormData,
      tagsList: [],
      selectedTags: [],
    };

    (createFileBatchReportRequest as jest.Mock).mockResolvedValue({
      id: 123,
    });

    const mockAddTagToRequest = jest.fn();
    jest.mocked(addTagToRequest).mockImplementation(mockAddTagToRequest);

    await store.dispatch(
      thunkCreateRenovaBioReportRequest(payload) as unknown as Action
    );

    const actions = store.getActions();
    expect(actions[0].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/pending`
    );
    expect(actions[1].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/fulfilled`
    );
    expect(actions[1].payload).toEqual({ id: 123 });

    expect(mockAddTagToRequest).not.toHaveBeenCalled();
  });

  it("handles errors when creating tags", async () => {
    const payload: ThunkCreateRenovaBioReportRequestPayload = {
      reportType: ReportType.RENOVABIO_CERTIFICATION,
      formData: {} as FormData,
      tagsList: [],
      selectedTags: ["newTag1"],
    };

    const error = new Error("Failed to create tags");
    (createFileBatchReportRequest as jest.Mock).mockResolvedValue({
      id: 123,
    });
    jest.mocked(createTags).mockRejectedValue(error);

    await store.dispatch(
      thunkCreateRenovaBioReportRequest(payload) as unknown as Action
    );

    const actions = store.getActions();
    expect(actions[0].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/pending`
    );
    expect(actions[1].type).toBe(
      `${THUNK_TYPES.CREATE_RENOVA_BIO_REPORT_REQUEST}/rejected`
    );
    expect(actions[1].payload).toEqual(error);
  });
});
