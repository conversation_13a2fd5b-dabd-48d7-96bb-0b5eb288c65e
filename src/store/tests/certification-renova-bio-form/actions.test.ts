import {
  ACTION_TYPES,
  CertificationRenovaBioState,
} from "../../modules/certification-renova-bio-form/types";
import { ReportType } from "../../../types";

import {
  actionSetCertificationRenovaBioFormStep,
  actionSetCertificationRenovaBioDocumentsData,
  actionSetRenovaBioReportType,
  actionRenovaBioCertificationResetFileMetadata,
} from "../../modules/certification-renova-bio-form/actions";

describe("Certification Renova Bio Form Actions", () => {
  it("should create an action to set the form step", () => {
    const step = 2;
    const expectedAction = {
      type: ACTION_TYPES.SET_RENOVA_BIO_FORM_STEP,
      payload: step,
    };
    expect(actionSetCertificationRenovaBioFormStep(step)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set documents data", () => {
    const documentsData: CertificationRenovaBioState["data"] = {
      carCodes: [],
      formData: null,
      fileMetadata: {
        lastModified: 0,
        name: "",
        size: 0,
        type: "",
      },
      formStep: 0,
      invalidRows: [],
      reportType: ReportType.RENOVABIO_CERTIFICATION,
    };
    const expectedAction = {
      type: ACTION_TYPES.SET_DOCUMENTS_DATA,
      payload: documentsData,
    };
    expect(actionSetCertificationRenovaBioDocumentsData(documentsData)).toEqual(
      expectedAction
    );
  });

  it("should create an action to set the report type", () => {
    const reportType: ReportType = ReportType.RENOVABIO_MONITORING;
    const expectedAction = {
      type: ACTION_TYPES.SET_REPORT_TYPE,
      payload: reportType,
    };
    expect(actionSetRenovaBioReportType(reportType)).toEqual(expectedAction);
  });

  it("should create an action to reset file metadata", () => {
    const expectedAction = {
      type: ACTION_TYPES.RESET_FILE_METADATA,
    };
    expect(actionRenovaBioCertificationResetFileMetadata()).toEqual(
      expectedAction
    );
  });
});
