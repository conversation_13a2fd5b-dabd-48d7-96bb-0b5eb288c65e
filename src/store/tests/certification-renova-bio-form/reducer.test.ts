import { certificationRenovaBio } from "../../modules/certification-renova-bio-form/reducer";
import { CertificationRenovaBioState } from "../../modules/certification-renova-bio-form/types";
import { ReportType } from "../../../types";
import { thunkCreateRenovaBioReportRequest } from "../../modules/certification-renova-bio-form/thunks";
import {
  actionRenovaBioCertificationResetFileMetadata,
  actionSetCertificationRenovaBioFormStep,
  actionSetRenovaBioReportType,
  actionSetCertificationRenovaBioDocumentsData,
  actionRenovaBioCertificationResetState,
} from "../../modules/certification-renova-bio-form/actions";

describe("certificationRenovaBio reducer", () => {
  const initialState: CertificationRenovaBioState = {
    data: {
      formStep: 0,
      formData: null,
      invalidRows: [],
      carCodes: [],
      fileMetadata: {
        lastModified: 0,
        name: "",
        size: 0,
        type: "",
      },
      reportType: ReportType.RENOVABIO_CERTIFICATION,
    },
    loading: false,
    error: "",
  };

  it("should handle actionSetCertificationRenovaBioFormStep", () => {
    const action = actionSetCertificationRenovaBioFormStep(2);
    const state = certificationRenovaBio(initialState, action);
    expect(state.data.formStep).toBe(2);
  });

  it("should handle actionSetRenovaBioReportType", () => {
    const action = actionSetRenovaBioReportType(ReportType.AGRO_CREDIT);
    const state = certificationRenovaBio(initialState, action);
    expect(state.data.reportType).toBe(ReportType.AGRO_CREDIT);
  });

  it("should handle actionRenovaBioCertificationResetFileMetadata", () => {
    const modifiedState = {
      ...initialState,
      data: {
        ...initialState.data,
        fileMetadata: {
          lastModified: 123,
          name: "test",
          size: 456,
          type: "application/pdf",
        },
      },
    };
    const action = actionRenovaBioCertificationResetFileMetadata();
    const state = certificationRenovaBio(modifiedState, action);
    expect(state.data.fileMetadata).toEqual(initialState.data.fileMetadata);
  });

  it("should handle actionRenovaBioCertificationResetState", () => {
    const modifiedState = {
      ...initialState,
      data: {
        ...initialState.data,
        formData: {} as FormData,
        fileMetadata: {
          lastModified: 123,
          name: "test",
          size: 456,
          type: "application/pdf",
        },
      },
    };
    const action = actionRenovaBioCertificationResetState();
    const state = certificationRenovaBio(modifiedState, action);
    expect(state.data).toEqual(initialState.data);
  });

  it("should handle actionSetCertificationRenovaBioDocumentsData", () => {
    const newData: CertificationRenovaBioState["data"] = {
      formStep: 1,
      formData: null,
      invalidRows: [
        {
          ano_elegibilidade: 2026,
          car: "123456",
          cultura: "SOJA",
        },
      ],
      carCodes: [
        {
          car: "123456",
        },
      ],
      fileMetadata: {
        lastModified: 789,
        name: "newfile",
        size: 123,
        type: "application/json",
      },
      reportType: ReportType.RENOVABIO_CERTIFICATION,
    };
    const action = actionSetCertificationRenovaBioDocumentsData(newData);
    const state = certificationRenovaBio(initialState, action);
    expect(state.data).toEqual(newData);
  });

  it("should handle thunkCreateRenovaBioReportRequest.pending", () => {
    const action = { type: thunkCreateRenovaBioReportRequest.pending.type };
    const state = certificationRenovaBio(initialState, action);
    expect(state.loading).toBe(true);
  });

  it("should handle thunkCreateRenovaBioReportRequest.fulfilled", () => {
    const action = { type: thunkCreateRenovaBioReportRequest.fulfilled.type };
    const state = certificationRenovaBio(initialState, action);
    expect(state.loading).toBe(false);
    expect(state.error).toBe("");
    expect(state.data).toEqual(initialState.data);
  });

  it("should handle thunkCreateRenovaBioReportRequest.rejected", () => {
    const action = {
      type: thunkCreateRenovaBioReportRequest.rejected.type,
      payload: "Error message",
    };
    const state = certificationRenovaBio(initialState, action);
    expect(state.loading).toBe(false);
    expect(state.error).toBe(JSON.stringify("Error message"));
  });
});
