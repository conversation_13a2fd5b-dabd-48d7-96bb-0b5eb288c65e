import { getDetailsAccount } from "../../../modules/account/get-details/reducer";
import { GetDetailsAccountState } from "../../../modules/account/get-details/types";
import { GetDetailsAccountInitialState } from "../../../modules/account/get-details/reducer";
import { thunkGetDetailsAccount } from "../../../modules/account/get-details/thunks";
import { AnyAction } from "@reduxjs/toolkit";

describe("AccoutDetailsReducer", () => {
  const initialState: GetDetailsAccountState = {
    data: {
      criterias_list: [],
      id: 1,
      id_external: 1,
      id_platform: 1,
      name: "Test",
      document: "*********",
      plan: "Basic",
      type: "User",
      credit: 100,
      sub_modules: [],
      credit_available: {
        AGRO_CREDIT: 50,
        ALL: 0,
        DETAILED_ANALYSIS_DEFORESTATION: 0,
        DETAILED_ANALYSIS_DEFORESTATION_EUDR: 0,
        INSPECTION: 0,
        INSPECTION_FINANCIAL: 0,
        PORTF<PERSON>IO_DIAGNOSIS: 0,
        RENOVABIO_CERTIFICATION: 0,
        RENOVABIO_MONITORING: 0,
        SINISTER: 0,
        SOCIOENVIRONMENT_CERTIFICATION: 0,
        SOCIOENVIRONMENT_COMPLIANCE: 0,
        SOCIOENVIRONMENT_PROTOCOL: 0,
        SOCIOENVIRONMENT_PROTOCOL_EUDR: 0,
        SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE: 0,
        SOCIOENVIRONMENT_PROTOCOL_MARFRIG: 0,
        SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER: 0,
        SOY_DEFORESTATION_CAR: 0,
        SOY_DEFORESTATION_MUNICIPALITY: 0,
        VALUATION: 0,
        CERTIFICATION_2BSVS: 0,
      },
      resources: [],
    },
    error: "",
    loading: false,
  };

  test("should return the initial state", () => {
    const result = getDetailsAccount(undefined, {} as AnyAction);
    expect(result).toEqual(GetDetailsAccountInitialState);
  });

  test("should handle pending state", () => {
    const action = { type: thunkGetDetailsAccount.pending.type };
    const result = getDetailsAccount(initialState, action);
    expect(result.loading).toBe(true);
  });

  test("should handle fulfilled state", () => {
    const action = {
      type: thunkGetDetailsAccount.fulfilled.type,
      payload: {
        id: 2,
        id_external: 2,
        id_platform: 2,
        name: "Test 2",
        document: "*********",
        plan: "Premium",
        type: "Admin",
        credit: 200,
        credit_available: 150,
        resources: [],
      },
    };
    const result = getDetailsAccount(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toBe("");
    expect(result.data).toEqual(action.payload);
  });

  test("should handle rejected state", () => {
    const action = {
      type: thunkGetDetailsAccount.rejected.type,
      payload: "Error fetching account details",
    };
    const result = getDetailsAccount(initialState, action);
    expect(result.loading).toBe(false);
    expect(result.error).toBe(action.payload);
  });
});
