import { thunkGetDetailsAccount } from "../../../modules/account/get-details/thunks";
import { getAccountInfo } from "../../../../services/account";
import { store } from "../../../";

jest.mock("../../../../services/account");

describe("thunkGetDetailsAccount", () => {
  it("should handle with thunk get details account fullfield", async () => {
    const expected = {
      data: {
        id: "account-id",
        credit_available: 100,
      },
      loading: false,
      error: "",
    };

    (getAccountInfo as jest.Mock).mockResolvedValue(expected.data);

    await store.dispatch(thunkGetDetailsAccount());

    const state = store.getState();

    expect(state.getDetailsAccount).toEqual(expected);
  });

  it("dispatches fulfilled action when getAccountInfo rejects", async () => {
    const error = { status: 400, message: "error" };

    (getAccountInfo as jest.Mock).mockRejectedValue(error);

    await store.dispatch(thunkGetDetailsAccount());

    const state = store.getState();

    expect(state.getDetailsAccount.error).toEqual(error);
  });
});
