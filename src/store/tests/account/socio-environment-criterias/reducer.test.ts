import { socioEnvironmentCriterias } from "../../../modules/account/socio-environment-criterias/reducer";
import { SocioEnvironmentCriteriasState } from "../../../modules/account/socio-environment-criterias/types";

import {
  actionSetDeforestationSocioEnvironmentCriterias,
  actionSetSocioEnvironmentCriterias,
} from "../../../modules/account/socio-environment-criterias/actions";

describe("SocioEnvironmentCriterias Reducer", () => {
  const initialState: SocioEnvironmentCriteriasState = {
    data: {
      deforestationCriterias: [],
      socioEnvironmentalCriterias: [],
    },
    error: "",
    loading: false,
  };

  it("should return the initial state", () => {
    expect(socioEnvironmentCriterias(undefined, { type: undefined })).toEqual(
      initialState
    );
  });

  it("should handle actionSetDeforestationSocioEnvironmentCriterias", () => {
    const payload = [
      { label: "criteriaA", checked: false, value: "criteriaA" },
      { label: "criteriaB", checked: false, value: "criteriaB" },
    ];

    const action = actionSetDeforestationSocioEnvironmentCriterias(payload);
    const expectedState = {
      ...initialState,
      data: {
        ...initialState.data,
        deforestationCriterias: payload,
      },
    };

    expect(socioEnvironmentCriterias(initialState, action)).toEqual(
      expectedState
    );
  });

  it("should handle actionSetSocioEnvironmentCriterias", () => {
    const payload = [
      { label: "criteriaA", checked: false, value: "criteriaA" },
      { label: "criteriaB", checked: false, value: "criteriaB" },
    ];

    const action = actionSetSocioEnvironmentCriterias(payload);
    const expectedState = {
      ...initialState,
      data: {
        ...initialState.data,
        socioEnvironmentalCriterias: payload,
      },
    };

    expect(socioEnvironmentCriterias(initialState, action)).toEqual(
      expectedState
    );
  });
});
