import { store } from "@/store";
import { thunkCreateSoyDeforestationRequest } from "@/store/modules/soy-deforestation-form/thunks";
import { createReportExternalAPI } from "@/services/report-request";
import { FileRequest } from "@types";

jest.mock("@/services/report-request");

describe("thunkCreateSoyDeforestationRequest", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should handle thunk create soy deforestation request fulfilled", async () => {
    const files = [
      { file: new File([], "file1") },
      { file: new File([], "file2") },
    ] as unknown as FileRequest[];

    (createReportExternalAPI as jest.Mock)
      .mockResolvedValueOnce("response1")
      .mockResolvedValueOnce("response2");

    await store.dispatch(thunkCreateSoyDeforestationRequest(files));
    const state = store.getState();
    expect(state.soybeanDeforestationForm).toEqual({
      ...state.soybeanDeforestationForm,
      isLoading: false,
      error: "",
    });
  });

  it("should handle thunk create soy deforestation request fulfilled", async () => {
    const files = [
      { file: new File([], "file1") },
      { file: new File([], "file2") },
    ] as unknown as FileRequest[];

    (createReportExternalAPI as jest.Mock).mockRejectedValueOnce("ERROR");

    await store.dispatch(thunkCreateSoyDeforestationRequest(files));
    const state = store.getState();
    expect(state.soybeanDeforestationForm).toEqual({
      ...state.soybeanDeforestationForm,
      isLoading: false,
      error: "ERROR_CREATE_REPORT",
    });
  });
});
