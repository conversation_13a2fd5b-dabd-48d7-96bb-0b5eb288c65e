import { AnyAction } from "@reduxjs/toolkit";
import { soybeanDeforestationForm } from "../../modules/soy-deforestation-form/reducer";
import { SoyDeforestationFormState } from "../../modules/soy-deforestation-form/types";
import { thunkCreateSoyDeforestationRequest } from "../../modules/soy-deforestation-form/thunks";
import { ReportType } from "../../../types";
import {
  resetStateAction,
  setReportTypeAction,
  setSelectedCARsAction,
  setSelectedMunicipalitiesAction,
} from "../../modules";
describe("GetSoyDeforestationFormInitialState", () => {
  const initialState: SoyDeforestationFormState = {
    reportType: ReportType.SOY_DEFORESTATION_CAR,
    selectedMunicipalities: [],
    selectedCARs: [],
    error: "",
    isLoading: false,
  };

  test("should return the initial state", () => {
    const result = soybeanDeforestationForm(undefined, {} as AnyAction);
    expect(result).toEqual(initialState);
  });

  test("should handle pending state", () => {
    const action = { type: thunkCreateSoyDeforestationRequest.pending.type };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.isLoading).toBe(true);
  });

  test("should return the initial state", () => {
    const result = soybeanDeforestationForm(undefined, {} as AnyAction);
    expect(result).toEqual(initialState);
  });

  test("should handle setReportTypeAction", () => {
    const action = {
      type: setReportTypeAction.type,
      payload: ReportType.SOY_DEFORESTATION_MUNICIPALITY,
    };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.reportType).toBe(action.payload);
  });

  test("should handle setSelectedCARsAction", () => {
    const action = {
      type: setSelectedCARsAction.type,
      payload: ["CAR1", "CAR2"],
    };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.selectedCARs).toEqual(action.payload);
  });

  test("should handle setSelectedMunicipalitiesAction", () => {
    const action = {
      type: setSelectedMunicipalitiesAction.type,
      payload: [{ value: 1, label: "Municipality 1" }],
    };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.selectedMunicipalities).toEqual(action.payload);
  });

  test("should handle resetStateAction", () => {
    const modifiedState: SoyDeforestationFormState = {
      ...initialState,
      reportType: ReportType.SOY_DEFORESTATION_MUNICIPALITY,
    };
    const result = soybeanDeforestationForm(modifiedState, resetStateAction());
    expect(result).toEqual(initialState);
  });

  test("should handle pending state", () => {
    const action = { type: thunkCreateSoyDeforestationRequest.pending.type };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.isLoading).toBe(true);
  });

  test("should handle fulfilled state", () => {
    const action = { type: thunkCreateSoyDeforestationRequest.fulfilled.type };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.isLoading).toBe(false);
    expect(result.error).toBe("");
  });

  test("should handle rejected state", () => {
    const action = {
      type: thunkCreateSoyDeforestationRequest.rejected.type,
      payload: {},
    };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.isLoading).toBe(false);
    expect(result.error).toBe("ERROR_CREATE_REPORT");
  });

  test("should handle rejected state with 400 error", () => {
    const action = {
      type: thunkCreateSoyDeforestationRequest.rejected.type,
      payload: { status: 400, response: { data: { detail: "Bad Request" } } },
    };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.isLoading).toBe(false);
    expect(result.error).toBe("Bad Request");
  });

  test("should handle rejected state with 400 error without detail", () => {
    const action = {
      type: thunkCreateSoyDeforestationRequest.rejected.type,
      payload: { status: 400, response: { data: null } },
    };
    const result = soybeanDeforestationForm(initialState, action);
    expect(result.isLoading).toBe(false);
    expect(result.error).toBe("ERROR_CREATE_REPORT");
  });
});
