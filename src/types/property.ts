import { GeoJSONGeometry } from "ol/format/GeoJSON";

type AreaCoordinates = {} & GeoJSONGeometry;

export type Property = {
  id: number;
  account: number;
  area_name: string;
  area_code: string;
  external_code: string;
  contact: number;
  notes: string;
  created_at: string;
  updated_at: string;
  uf: string;
  city: string;
  area_total: number;
  latitude: string;
  longitude: string;
  area_coordinates: GeoJSONGeometry;
} & Record<string, any>;

export type ContactProperties = {
  id: number;
  account: number;
  name: string;
  document: string;
  created_at: string;
  updated_at: string;
  embargos: null | any;
  slaveries: null | any;
  processes: null | any;
  situation: number;
  uniqueness_score: number;
  crops: Array<number>;
  tags: Array<any>;
  group_id: number;
  ibama: null | any;
  ldi: null | any;
  moratoria: null | any;
  icmbio: null | any;
  sema: null | any;
  document_type: string;
  edited_name: boolean;
  edited_document: boolean;
  properties: Property[];
};
