import { ReportStatus } from "./report-status";
import { ReportType } from "./report-type";

export interface ReportAbout {
  label: string;
  value: string;
}

export enum RequestMethod {
  MANUAL = "MANUAL",
  BATCH = "BATCH",
}

export enum ReportGenericError {
  not_found_geom = "not_found_geom",
  duplicate_matriculation_code = "duplicate_matriculation_code",
  none_geom_params = "none_geom_params",
  wrong_dates = "wrong_dates",
  not_found_contact_in_base = "not_found_contact_in_base",
  not_found_protocol_in_base = "not_found_protocol_in_base",
  connection_failed_smart = "connection_failed_smart",
  error_in_document = "error_in_document",
  nonexistent_document_and_car = "nonexistent_document_and_car",
  invalid_or_no_intersection_with_property = "invalid_or_no_intersection_with_property",
  invalid_biome = "invalid_biome",
}

export enum ReportParamError {
  document = "document",
  ref_bacen = "ref_bacen",
  contact_name = "contact_name",
  crop = "crop",
  crop_year = "crop_year",
  municipalities = "municipalities",
  total_area = "total_area",
}

export type ReportError = ReportGenericError | ReportParamError;

export const REPORT_ERROR_LABELS = {
  [ReportGenericError.not_found_geom]: "NOT_FOUND_GEOM",
  [ReportGenericError.duplicate_matriculation_code]:
    "DUPLICATE_MATRICULATION_CODE",
  [ReportGenericError.none_geom_params]: "EMPTY_GEOMETRY_PARAMS",
  [ReportGenericError.wrong_dates]: "WRONG_DATES",
  [ReportGenericError.not_found_contact_in_base]: "NOT_FOUND_CONTACT_IN_BASE",
  [ReportGenericError.not_found_protocol_in_base]: "NOT_FOUND_PROTOCOL_IN_BASE",
  [ReportGenericError.connection_failed_smart]: "CONNECTION_FAILED_SMART",
  [ReportGenericError.error_in_document]: "ERROR_IN_DOCUMENT",
  [ReportGenericError.nonexistent_document_and_car]:
    "NONEXISTENT_DOCUMENT_AND_CAR",
  [ReportGenericError.invalid_or_no_intersection_with_property]:
    "INVALID_OR_NO_INTERSECTION_WITH_PROPERTY",
  [ReportGenericError.invalid_biome]: "INVALID_BIOMES",
};

export interface Report {
  external_id: null | number;
  id: number;
  report_exports: number[];
  report_request_id: number;
  key: React.Key;
  report_about: ReportAbout[];
  report_type: ReportType;
  status: ReportStatus;
  created: Date;
  modified: Date;
  errors: null | Record<ReportError, string>;
  observation: null | string;
  signature: boolean;
}

export interface ReportFormProps<T = any> {
  data: T;
  readonly isSubmitted: boolean;
  readonly onChange?: (value: any) => void;
}

export interface CharInput {
  label: string;
  value: string;
  is_parent?: boolean;
}

export interface IReportCSVParserResult {
  validRows: any[];
  invalidRows: any[];
}

export type IReportCSVParser = Record<
  string,
  (row: string[][]) => IReportCSVParserResult
>;
