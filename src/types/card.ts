import { FileModelReport } from "@constants";
import { ReportType } from "./report-type";

export enum TagCard {
  NEW = "NEW",
  SOON = "SOON",
}

export interface FileFormatData {
  file_format: string;
}

export interface CardData {
  id: number;
  title: string;
  description: string;
  tag: TagCard | null;
  report_type: ReportType | null;
  files_format: FileFormatData[] | null;
  has_metadata: boolean;
  has_signature: boolean;
  signature_resource: string;
  no_signature_resource: string;
  has_permission: boolean;
  value: number;
  models: FileModelReport[];
}
