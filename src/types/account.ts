import { ReportType } from "./report-type";

export interface SubModule {
  limit: string;
  report_type: string;
  module_id: number;
  type: string;
  description: string;
  active: boolean;
}

export interface Account {
  id: number;
  id_external: number;
  id_platform: number;
  name: string;
  document: string;
  plan: string;
  type: string;
  credit: number;
  credit_available: Record<ReportType, number>;
  resources: string[];
  criterias_list: Array<{
    label: string;
    value: string;
  }>;
  sub_modules: Array<SubModule>;
}

export interface User {
  id: string;
  platformId: string;
  email: string;
  status: string;
  modules: string[];
  account: {
    id: string;
    platformId: string;
    name: string;
    document: string;
    type: string;
    plan: string;
  };
  properties: {
    account: {
      customLayers: any[];
      imageUpdateLimit: number;
      partialConsultAllowed: boolean;
      geoprocessingLimit: number;
      consultGuaranteesAllowed: boolean;
      defaultRoute: string;
      ssoAllowed: boolean;
      backgroundCheck: number;
      serasa: number;
      users: number;
      usingOrganizationGroups: boolean;
    };
    user: {
      needChangePassword: boolean;
      lastLogin: Date;
      role: number;
      firstLogin: boolean;
      document: string;
      admin: boolean;
      needInitialTour: boolean;
      loginByEmail: boolean;
      name: string;
      organizationGroups: any[];
      email: string;
      situation: number;
      username: string;
    };
  };
  otherAccounts: any[];
}
