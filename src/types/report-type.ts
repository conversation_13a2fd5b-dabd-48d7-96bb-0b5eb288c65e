export enum ReportType {
  VALUATION = "VALUATION",
  R<PERSON><PERSON><PERSON><PERSON><PERSON>_CERTIFICATION = "REN<PERSON><PERSON><PERSON>O_CERTIFICATION",
  RENO<PERSON><PERSON>O_MONITORING = "RENO<PERSON><PERSON><PERSON>_MONITORING",
  INSPECTION = "INSPECTION",
  AGRO_CREDIT = "AGRO_CREDIT",
  INSPECTION_FINANCIAL = "INSPECTION_FINANCIAL",
  SOCIOENVIRONMENT_COMPLIANCE = "SOCIOENVIRONMENT_COMPLIANCE",
  SOCIOENVIRONMENT_PROTOCOL = "SOCIOENVIRONMENT_PROTOCOL",
  SOCIOENVIRONMENT_PROTOCOL_MARFRIG = "SOCIOENVIRONMENT_PROTOCOL_MARFRIG",
  SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER = "SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER",
  SOCIOENVIRONMENT_PROTOCOL_EUDR = "SOC<PERSON>ENVIRONMENT_PROTOCOL_EUDR",
  SOCIOENVIRONMENT_CERTIFICATION = "SOCIOENVIRONMENT_CERTIFICATION",
  SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE = "SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE",
  DETAILED_ANALYSIS_DEFORESTATION = "DETAILED_ANALYSIS_DEFORESTATION",
  DETAILED_ANALYSIS_DEFORESTATION_EUDR = "DETAILED_ANALYSIS_DEFORESTATION_EUDR",
  SOY_DEFORESTATION_CAR = "SOY_DEFORESTATION_CAR",
  SOY_DEFORESTATION_MUNICIPALITY = "SOY_DEFORESTATION_MUNICIPALITY",
  SINISTER = "SINISTER",
  ALL = "ALL",
  PORTFOLIO_DIAGNOSIS = "PORTFOLIO_DIAGNOSIS",
  CERTIFICATION_2BSVS = "CERTIFICATION_2BSVS",
}
