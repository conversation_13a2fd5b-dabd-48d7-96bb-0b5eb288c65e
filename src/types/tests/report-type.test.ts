import { ReportType } from '../report-type';

describe('ReportType', () => {
  it('should define all expected report types', () => {
    expect(ReportType.VALUATION).toBe('VALUATION');
    expect(ReportType.RENOVABIO_CERTIFICATION).toBe('RENOVABIO_CERTIFICATION');
    expect(ReportType.RENOVABIO_MONITORING).toBe('RENOVABIO_MONITORING');
    expect(ReportType.INSPECTION).toBe('INSPECTION');
    expect(ReportType.AGRO_CREDIT).toBe('AGRO_CREDIT');
    expect(ReportType.INSPECTION_FINANCIAL).toBe('INSPECTION_FINANCIAL');
    expect(ReportType.SOCIOENVIRONMENT_COMPLIANCE).toBe('SOCIOENVIRONMENT_COMPLIANCE');
    expect(ReportType.SOCIOENVIRONMENT_PROTOCOL).toBe('SOCIOENVIRONMENT_PROTOCOL');
    expect(ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG).toBe('SOCIOENVIRONMENT_PROTOCOL_MARFRIG');
    expect(ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER).toBe('SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER');
    expect(ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR).toBe('SOCIOENVIRONMENT_PROTOCOL_EUDR');
    expect(ReportType.SOCIOENVIRONMENT_CERTIFICATION).toBe('SOCIOENVIRONMENT_CERTIFICATION');
    expect(ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE).toBe('SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE');
    expect(ReportType.DETAILED_ANALYSIS_DEFORESTATION).toBe('DETAILED_ANALYSIS_DEFORESTATION');
    expect(ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR).toBe('DETAILED_ANALYSIS_DEFORESTATION_EUDR');
    expect(ReportType.SOY_DEFORESTATION_CAR).toBe('SOY_DEFORESTATION_CAR');
    expect(ReportType.SOY_DEFORESTATION_MUNICIPALITY).toBe('SOY_DEFORESTATION_MUNICIPALITY');
    expect(ReportType.SINISTER).toBe('SINISTER');
    expect(ReportType.ALL).toBe('ALL');
    expect(ReportType.PORTFOLIO_DIAGNOSIS).toBe('PORTFOLIO_DIAGNOSIS');
    expect(ReportType.CERTIFICATION_2BSVS).toBe('CERTIFICATION_2BSVS');
  });

  it('should have the correct number of report types', () => {
    const reportTypeCount = Object.keys(ReportType).length;

    expect(reportTypeCount).toBe(21);
  });

  it('should have unique values for each report type', () => {
    const values = Object.values(ReportType);

    const uniqueValues = new Set(values);

    expect(uniqueValues.size).toBe(values.length);
  });
});
