import { FileRequest } from '../batch';

describe('Batch Types', () => {
  describe('FileRequest', () => {
    it('should have the correct structure', () => {
      const mockFile = new Blob(['test content'], { type: 'text/plain' });
      
      const mockFileRequest: FileRequest = {
        tempId: '123456',
        name: 'test.csv',
        file: mockFile,
        lineCount: 10
      };

      expect(mockFileRequest).toHaveProperty('tempId');
      expect(mockFileRequest).toHaveProperty('name');
      expect(mockFileRequest).toHaveProperty('file');
      expect(mockFileRequest).toHaveProperty('lineCount');
      
      expect(typeof mockFileRequest.tempId).toBe('string');
      expect(typeof mockFileRequest.name).toBe('string');
      expect(mockFileRequest.file instanceof Blob).toBe(true);
      expect(typeof mockFileRequest.lineCount).toBe('number');
      
      expect(mockFileRequest.tempId).toBe('123456');
      expect(mockFileRequest.name).toBe('test.csv');
      expect(mockFileRequest.file.size).toBeGreaterThan(0);
      expect(mockFileRequest.lineCount).toBe(10);
    });

    it('should work with different file types', () => {
      const csvFile = new Blob(['id,name\n1,test'], { type: 'text/csv' });
      const jsonFile = new Blob(['{"id":1,"name":"test"}'], { type: 'application/json' });
      
      const csvFileRequest: FileRequest = {
        tempId: 'csv123',
        name: 'data.csv',
        file: csvFile,
        lineCount: 2
      };
      
      const jsonFileRequest: FileRequest = {
        tempId: 'json456',
        name: 'data.json',
        file: jsonFile,
        lineCount: 1
      };

      expect(csvFileRequest.file.type).toBe('text/csv');
      expect(csvFileRequest.name).toBe('data.csv');
      expect(csvFileRequest.lineCount).toBe(2);
      
      expect(jsonFileRequest.file.type).toBe('application/json');
      expect(jsonFileRequest.name).toBe('data.json');
      expect(jsonFileRequest.lineCount).toBe(1);
    });
  });
});
