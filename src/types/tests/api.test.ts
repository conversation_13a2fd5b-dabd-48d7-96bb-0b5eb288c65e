import { PaginatedResponse, PaginationParams } from '../api';

describe('API Types', () => {
  describe('PaginatedResponse', () => {
    it('should have the correct structure', () => {
      const mockResponse: PaginatedResponse<string> = {
        items: ['item1', 'item2', 'item3'],
        count: 3
      };

      expect(mockResponse).toHaveProperty('items');
      expect(mockResponse).toHaveProperty('count');
      
      expect(Array.isArray(mockResponse.items)).toBe(true);
      expect(typeof mockResponse.count).toBe('number');
      
      expect(mockResponse.items.length).toBe(3);
      expect(mockResponse.count).toBe(3);
    });

    it('should work with different types', () => {
      interface TestType {
        id: number;
        name: string;
      }
      
      const mockResponse: PaginatedResponse<TestType> = {
        items: [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' }
        ],
        count: 2
      };

      expect(mockResponse).toHaveProperty('items');
      expect(mockResponse).toHaveProperty('count');
      
      expect(Array.isArray(mockResponse.items)).toBe(true);
      expect(typeof mockResponse.count).toBe('number');
      
      expect(mockResponse.items.length).toBe(2);
      expect(mockResponse.items[0].id).toBe(1);
      expect(mockResponse.items[0].name).toBe('Item 1');
      expect(mockResponse.count).toBe(2);
    });
  });

  describe('PaginationParams', () => {
    it('should have the correct structure', () => {
      const mockParams: PaginationParams = {
        limit: 10,
        offset: 0
      };

      expect(mockParams).toHaveProperty('limit');
      expect(mockParams).toHaveProperty('offset');
      
      expect(typeof mockParams.limit).toBe('number');
      expect(typeof mockParams.offset).toBe('number');
      
      expect(mockParams.limit).toBe(10);
      expect(mockParams.offset).toBe(0);
    });

    it('should allow optional fields', () => {
      const mockParamsWithLimit: PaginationParams = {
        limit: 10
      };

      expect(mockParamsWithLimit).toHaveProperty('limit');
      expect(mockParamsWithLimit.limit).toBe(10);
      expect(mockParamsWithLimit.offset).toBeUndefined();
      
      const mockParamsWithOffset: PaginationParams = {
        offset: 20
      };

      expect(mockParamsWithOffset).toHaveProperty('offset');
      expect(mockParamsWithOffset.offset).toBe(20);
      expect(mockParamsWithOffset.limit).toBeUndefined();
      
      const mockEmptyParams: PaginationParams = {};

      expect(mockEmptyParams.limit).toBeUndefined();
      expect(mockEmptyParams.offset).toBeUndefined();
    });
  });
});
