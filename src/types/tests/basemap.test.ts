import { BasemapType } from '../basemap';

describe('BasemapType', () => {
  it('should define all expected basemap types', () => {
    expect(BasemapType.Default).toBeDefined();
    expect(BasemapType.Dark).toBeDefined();
    expect(BasemapType.Satellite).toBeDefined();
  });

  it('should have the correct number of basemap types', () => {
    const basemapTypeCount = Object.keys(BasemapType).length / 2;
    
    expect(basemapTypeCount).toBe(3);
  });

  it('should have numeric values for each basemap type', () => {
    expect(typeof BasemapType.Default).toBe('number');
    expect(typeof BasemapType.Dark).toBe('number');
    expect(typeof BasemapType.Satellite).toBe('number');
  });

  it('should have sequential values starting from 0', () => {
    expect(BasemapType.Default).toBe(0);
    expect(BasemapType.Dark).toBe(1);
    expect(BasemapType.Satellite).toBe(2);
  });
});
