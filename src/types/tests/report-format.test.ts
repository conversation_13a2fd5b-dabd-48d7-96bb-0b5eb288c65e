import { ReportFormat } from '../report-format';

describe('ReportFormat', () => {
  it('should define all expected report formats', () => {
    expect(ReportFormat.EXCEL).toBeDefined();
    expect(ReportFormat.PDF).toBeDefined();
    expect(ReportFormat.XLS).toBeDefined();
    expect(ReportFormat.XLSX).toBeDefined();
    expect(ReportFormat.HTML).toBeDefined();
    expect(ReportFormat.CSV).toBeDefined();
    expect(ReportFormat.JSON).toBeDefined();
    expect(ReportFormat.GEOJSON).toBeDefined();
    expect(ReportFormat.PNG).toBeDefined();
    expect(ReportFormat.DOC).toBeDefined();
    expect(ReportFormat.DOCX).toBeDefined();
  });

  it('should have the correct number of report formats', () => {
    const reportFormatCount = Object.keys(ReportFormat).length / 2;
    
    expect(reportFormatCount).toBe(11);
  });

  it('should have numeric values for each report format', () => {
    expect(typeof ReportFormat.EXCEL).toBe('number');
    expect(typeof ReportFormat.PDF).toBe('number');
    expect(typeof ReportFormat.XLS).toBe('number');
    expect(typeof ReportFormat.XLSX).toBe('number');
    expect(typeof ReportFormat.HTML).toBe('number');
    expect(typeof ReportFormat.CSV).toBe('number');
    expect(typeof ReportFormat.JSON).toBe('number');
    expect(typeof ReportFormat.GEOJSON).toBe('number');
    expect(typeof ReportFormat.PNG).toBe('number');
    expect(typeof ReportFormat.DOC).toBe('number');
    expect(typeof ReportFormat.DOCX).toBe('number');
  });

  it('should have sequential values starting from 0', () => {
    expect(ReportFormat.EXCEL).toBe(0);
    expect(ReportFormat.PDF).toBe(1);
    expect(ReportFormat.XLS).toBe(2);
    expect(ReportFormat.XLSX).toBe(3);
    expect(ReportFormat.HTML).toBe(4);
    expect(ReportFormat.CSV).toBe(5);
    expect(ReportFormat.JSON).toBe(6);
    expect(ReportFormat.GEOJSON).toBe(7);
    expect(ReportFormat.PNG).toBe(8);
    expect(ReportFormat.DOC).toBe(9);
    expect(ReportFormat.DOCX).toBe(10);
  });
});
