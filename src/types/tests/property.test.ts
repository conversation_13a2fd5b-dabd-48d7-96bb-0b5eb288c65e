import { Property, ContactProperties } from '../property';

describe('Property Type', () => {
  it('should have the correct structure', () => {
    const mockProperty: Property = {
      id: 1,
      account: 123,
      area_name: 'Test Property',
      area_code: 'TP001',
      external_code: 'EXT001',
      contact: 456,
      notes: 'Test notes',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z',
      uf: 'SP',
      city: 'São Paulo',
      area_total: 1000,
      latitude: '-23.5505',
      longitude: '-46.6333',
      area_coordinates: {
        type: 'Point',
        coordinates: [-46.6333, -23.5505]
      },
      custom_field: 'custom value'
    };

    expect(mockProperty).toHaveProperty('id');
    expect(mockProperty).toHaveProperty('account');
    expect(mockProperty).toHaveProperty('area_name');
    expect(mockProperty).toHaveProperty('area_code');
    expect(mockProperty).toHaveProperty('external_code');
    expect(mockProperty).toHaveProperty('contact');
    expect(mockProperty).toHaveProperty('notes');
    expect(mockProperty).toHaveProperty('created_at');
    expect(mockProperty).toHaveProperty('updated_at');
    expect(mockProperty).toHaveProperty('uf');
    expect(mockProperty).toHaveProperty('city');
    expect(mockProperty).toHaveProperty('area_total');
    expect(mockProperty).toHaveProperty('latitude');
    expect(mockProperty).toHaveProperty('longitude');
    expect(mockProperty).toHaveProperty('area_coordinates');
    
    expect(mockProperty).toHaveProperty('custom_field');
  });
});

describe('ContactProperties Type', () => {
  it('should have the correct structure', () => {
    const mockContactProperties: ContactProperties = {
      id: 1,
      account: 123,
      name: 'Test Contact',
      document: '***********',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-02T00:00:00Z',
      embargos: null,
      slaveries: null,
      processes: null,
      situation: 1,
      uniqueness_score: 0.95,
      crops: [1, 2, 3],
      tags: [],
      group_id: 789,
      ibama: null,
      ldi: null,
      moratoria: null,
      icmbio: null,
      sema: null,
      document_type: 'CPF',
      edited_name: false,
      edited_document: false,
      properties: [
        {
          id: 1,
          account: 123,
          area_name: 'Test Property',
          area_code: 'TP001',
          external_code: 'EXT001',
          contact: 456,
          notes: 'Test notes',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-02T00:00:00Z',
          uf: 'SP',
          city: 'São Paulo',
          area_total: 1000,
          latitude: '-23.5505',
          longitude: '-46.6333',
          area_coordinates: {
            type: 'Point',
            coordinates: [-46.6333, -23.5505]
          }
        }
      ]
    };

    expect(mockContactProperties).toHaveProperty('id');
    expect(mockContactProperties).toHaveProperty('account');
    expect(mockContactProperties).toHaveProperty('name');
    expect(mockContactProperties).toHaveProperty('document');
    expect(mockContactProperties).toHaveProperty('created_at');
    expect(mockContactProperties).toHaveProperty('updated_at');
    expect(mockContactProperties).toHaveProperty('embargos');
    expect(mockContactProperties).toHaveProperty('slaveries');
    expect(mockContactProperties).toHaveProperty('processes');
    expect(mockContactProperties).toHaveProperty('situation');
    expect(mockContactProperties).toHaveProperty('uniqueness_score');
    expect(mockContactProperties).toHaveProperty('crops');
    expect(mockContactProperties).toHaveProperty('tags');
    expect(mockContactProperties).toHaveProperty('group_id');
    expect(mockContactProperties).toHaveProperty('ibama');
    expect(mockContactProperties).toHaveProperty('ldi');
    expect(mockContactProperties).toHaveProperty('moratoria');
    expect(mockContactProperties).toHaveProperty('icmbio');
    expect(mockContactProperties).toHaveProperty('sema');
    expect(mockContactProperties).toHaveProperty('document_type');
    expect(mockContactProperties).toHaveProperty('edited_name');
    expect(mockContactProperties).toHaveProperty('edited_document');
    expect(mockContactProperties).toHaveProperty('properties');
    
    expect(Array.isArray(mockContactProperties.properties)).toBe(true);
    expect(mockContactProperties.properties.length).toBe(1);
    expect(mockContactProperties.properties[0]).toHaveProperty('id');
    expect(mockContactProperties.properties[0]).toHaveProperty('area_name');
  });
});
