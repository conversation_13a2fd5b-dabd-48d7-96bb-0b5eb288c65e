import { ReportStatus, REPORT_STATUS_LABELS } from '../report-status';

describe('ReportStatus', () => {
  it('should define all expected report statuses', () => {
    expect(ReportStatus.PENDING).toBe('PENDING');
    expect(ReportStatus.PROCESSING).toBe('PROCESSING');
    expect(ReportStatus.ERROR).toBe('ERROR');
    expect(ReportStatus.WARNING).toBe('WARNING');
    expect(ReportStatus.DONE).toBe('DONE');
    expect(ReportStatus.ALL).toBe('ALL');
  });

  it('should have the correct number of report statuses', () => {
    const reportStatusCount = Object.keys(ReportStatus).length;
    
    expect(reportStatusCount).toBe(6);
  });

  it('should have unique values for each report status', () => {
    const values = Object.values(ReportStatus);
    
    const uniqueValues = new Set(values);
    
    expect(uniqueValues.size).toBe(values.length);
  });
});

describe('REPORT_STATUS_LABELS', () => {
  it('should have a label for each report status', () => {
    Object.values(ReportStatus).forEach(status => {
      expect(REPORT_STATUS_LABELS[status]).toBeDefined();
    });
  });

  it('should have the correct labels for each status', () => {
    expect(REPORT_STATUS_LABELS[ReportStatus.PENDING]).toBe('STATUS_WAITING');
    expect(REPORT_STATUS_LABELS[ReportStatus.PROCESSING]).toBe('PROCESSING');
    expect(REPORT_STATUS_LABELS[ReportStatus.ERROR]).toBe('ERROR');
    expect(REPORT_STATUS_LABELS[ReportStatus.WARNING]).toBe('WARNING');
    expect(REPORT_STATUS_LABELS[ReportStatus.DONE]).toBe('DONE');
    expect(REPORT_STATUS_LABELS[ReportStatus.ALL]).toBe('STATUS_ALL');
  });
});
