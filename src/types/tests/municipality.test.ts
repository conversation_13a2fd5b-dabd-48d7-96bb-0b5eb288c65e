import { Municipality } from '../municipality';

describe('Municipality Type', () => {
  it('should have the correct structure', () => {
    const mockMunicipality: Municipality = {
      ibge_code: 3550308,
      name: 'São Paulo',
      latitude: -23.5505,
      longitude: -46.6333,
      capital: true,
      uf: 'SP'
    };

    expect(mockMunicipality).toHaveProperty('ibge_code');
    expect(mockMunicipality).toHaveProperty('name');
    expect(mockMunicipality).toHaveProperty('latitude');
    expect(mockMunicipality).toHaveProperty('longitude');
    expect(mockMunicipality).toHaveProperty('capital');
    expect(mockMunicipality).toHaveProperty('uf');
    
    expect(typeof mockMunicipality.ibge_code).toBe('number');
    expect(typeof mockMunicipality.name).toBe('string');
    expect(typeof mockMunicipality.latitude).toBe('number');
    expect(typeof mockMunicipality.longitude).toBe('number');
    expect(typeof mockMunicipality.capital).toBe('boolean');
    expect(typeof mockMunicipality.uf).toBe('string');
  });

  it('should allow creating a non-capital municipality', () => {
    const mockMunicipality: Municipality = {
      ibge_code: 3509502,
      name: 'Campinas',
      latitude: -22.9064,
      longitude: -47.0616,
      capital: false,
      uf: 'SP'
    };

    expect(mockMunicipality.capital).toBe(false);
  });
});
