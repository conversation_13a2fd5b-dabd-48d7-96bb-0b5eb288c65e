import React from "react";
import { ReportType } from "./report-type";
import { PaginationParams } from "./api";

export interface ReportRequest {
  id: number;
  key: React.Key;
  user: { name: string; email: string };
  tags_names: string[];
  reports_types: ReportType[];
  exports?: { id: number; file_format: string }[];
  created: Date;
  modified: Date;
  reports_count: number;
  reports_done: number;
  reports_error: number;
  reports_pending: number;
  reports_processing: number;
  reports_waiting: number;
  reports_warning: number;
}

export type ListReportsParams = {
  report_request_id?: number;
} & PaginationParams;

export interface ExportData {
  id: number;
  external_id: string;
  expiration_date: Date;
  file_format: string;
  created: Date;
  modified: Date;
}

export type ReportExport = ExportData & {
  report_id: number;
};

export type ReportRequestExport = ExportData & {
  report_request_id: number;
};
