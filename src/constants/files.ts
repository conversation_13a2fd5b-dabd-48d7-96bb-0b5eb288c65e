import { IReportCSVParser, ReportType } from "@types";
import { isCarValid, isDocumentValid, maskCAR } from "@utils";

export const CSV_PARSER_BY_REPORT_TYPE: IReportCSVParser = {
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE]: (rows: string[][]) => {
    const validRows = rows
      .filter((x) => isDocumentValid(x[0]) && isCarValid(maskCAR(x[1])))
      .map((x) => ({
        document: x[0],
        car: x[1],
      }));
    const invalidRows = rows
      .filter((x) => !isDocumentValid(x[0]) || !isCarValid(maskCAR(x[1])))
      .map((x) => ({
        document: x[0],
        car: x[1],
      }));
    return {
      validRows,
      invalidRows,
    };
  },
  [ReportType.AGRO_CREDIT]: (rows: string[][]) => {
    const validRows = rows
      .filter((x) => isDocumentValid(x[0]))
      .map((x) => ({
        document: x[0],
        debt: x[1],
      }));
    const invalidRows = rows
      .filter((x) => !isDocumentValid(x[0]))
      .map((x) => ({
        document: x[0],
        debt: x[1],
      }));
    return {
      validRows,
      invalidRows,
    };
  },
};
