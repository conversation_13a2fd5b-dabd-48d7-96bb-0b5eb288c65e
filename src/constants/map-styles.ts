import Fill from "ol/style/Fill";
import Stroke from "ol/style/Stroke";
import Style from "ol/style/Style";

export const PROPERTY_STYLE = (feature) => {
  const highlight = feature.get("highlight");
  const selected = feature.get("selected");

  return new Style({
    fill: new Fill({
      color: selected ? "rgba(0, 178, 119, 0.5)" : "transparent",
    }),
    stroke: new Stroke({
      color: highlight ? "#00b277" : "#ffffff",
      width: 3,
    }),
  });
};

export const SUBAREA_STYLE = (feature) => {
  const highlight = feature.get("highlight");
  const selected = feature.get("selected");

  return new Style({
    fill: new Fill({
      color: selected ? "rgba(0, 178, 119, 0.5)" : "rgba(255, 232, 20, 0.5)",
    }),
    stroke: new Stroke({
      color: highlight ? "#ffffff" : selected ? "#00b277" : "#ffe814",
      width: 3,
    }),
  });
};

export const UNSELECTED_SUBAREA_STYLE = new Style({
  fill: new Fill({
    color: "transparent",
  }),
  stroke: new Stroke({
    color: "#ffe814",
    width: 3,
  }),
});
