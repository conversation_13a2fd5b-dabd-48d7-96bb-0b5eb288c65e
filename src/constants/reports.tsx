import { AgroCreditForm, ReportFileUpload } from "@components";
import {
  input_model_deforestation_analysis,
  input_model_eudr,
  input_model_eudr_cecafe,
  input_model_inspection,
  input_model_inspection_financial,
  input_model_sinister,
  input_model_socioenvironmental,
  input_model_socioenvironmental_protocols,
  input_model_soy_deforestation_car,
  input_model_soy_deforestation_municipality,
  input_model_valuation,
  input_model_project_planner,
  input_model_protocol_marfrig,
  model_renova_bio_input,
  portifolio_diagnosis_document_batch_input,
  portifolio_diagnosis_car_batch_input,
  certification_2bsvs_input,
} from "../assets/data";
import SoyDeforestationForm from "../components/organisms/soy-deforestation-form";
import { ReportFormat } from "../types/report-format";
import { ReportType } from "../types/report-type";
import { Resource } from "./resources";
import { CertificationRenovaBioForm } from "@/components/organisms/certification-renova-bio-form";
import { PortifolioDiagnosisForm } from "@/components/organisms/portifolio-diagnosis-form";
import { ReportPortifolioDiagnosisBatchDocumentType } from "@/store/modules/portifolio-diagnosis/types";
import { Certification2BSvsForm } from "@/components/organisms/certification-2bsvs-form/Certification2BSvsForm";
export interface FileModelReport {
  file?: string;
  file_format?: ReportFormat;
}

export interface AvailableReport {
  id?: number;
  type: ReportType;
  title: string;
  description: string;
  list?: string[];
  formats: ReportFormat[];
  models: FileModelReport[];
  isNew: boolean;
  isHidden?: boolean;
  commingSoon: boolean;
  hasMetadata: boolean;
  hasSignature: boolean;
  hasSignatureResource: Resource;
  requiredFields?: string[];
  noSignatureResource?: any;
  value?: number;
  batchRequestOnly?: boolean;
  formWidth?: number;
  hasPermission?: boolean;
  form?: () => JSX.Element;
  // Executado ao solicitar o relatorio. Se retornar um objeto, uma notificação de erro é exibida.
  onValidate?: (data) => { message: string; description: string } | null;
}

export const REPORT_CARD_METADATA: Record<string, Record<string, any>> = {
  [ReportType.AGRO_CREDIT]: {
    hasMetadata: false,
    form: () => <AgroCreditForm />,
  },
  [ReportType.VALUATION]: {
    hasMetadata: false,
  },
  [ReportType.INSPECTION]: {
    hasMetadata: true,
    requiredFields: [
      "municipality",
      "cropYear",
      "totalArea",
      "contractNumber",
      "institutionName",
    ],
  },
  [ReportType.INSPECTION_FINANCIAL]: {
    hasMetadata: true,
    isDocumentRequired: true,
    requiredFields: [
      "municipality",
      "cropYear",
      "totalArea",
      "refBacen",
      "financialInstitution",
    ],
  },
  [ReportType.SOCIOENVIRONMENT_COMPLIANCE]: {
    hasMetadata: true,
  },
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG]: {
    hasMetadata: false,
    isDocumentRequired: true,
  },
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER]: {
    hasMetadata: false,
    isDocumentRequired: true,
  },
  [ReportType.SOCIOENVIRONMENT_PROTOCOL]: {
    hasMetadata: false,
    isDocumentRequired: true,
    onValidate: (data) => {
      if (
        !["AMAZONIA", "AMAZONIA_LEGAL", "CERRADO"].some((biome) =>
          data.property?.bioma.includes(biome)
        )
      )
        return {
          message: "INVALID_BIOMES",
          description: "INVALID_BIOMES_DESCRIPTION",
        };

      return null;
    },
  },
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR]: {
    hasMetadata: true,
    requiredFields: ["language", "commodity"],
  },
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE]: {
    hasMetadata: true,
    isDocumentRequired: true,
    batchRequestOnly: true,
    form: () => (
      <ReportFileUpload
        reportType={ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE}
      />
    ),
  },
  [ReportType.DETAILED_ANALYSIS_DEFORESTATION]: {
    hasMetadata: true,
    isDocumentRequired: true,
  },

  [ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR]: {
    hasMetadata: true,
    isDocumentRequired: true,
    requiredFields: ["language", "commodity", "contact"],
    onValidate: () => null,
  },
  [ReportType.RENOVABIO_CERTIFICATION]: {
    hasMetadata: false,
    formWidth: 480,
    form: () => <CertificationRenovaBioForm />,
  },
  [ReportType.SOCIOENVIRONMENT_CERTIFICATION]: {
    hasMetadata: false,
    isDocumentRequired: true,
  },
  [ReportType.SINISTER]: {
    hasMetadata: true,
    isDocumentRequired: true,
    requiredFields: [
      "cropYear",
      "crop",
      "totalArea",
      "event",
      "seedingDate",
      "occurrenceDate",
      "noticeDate",
    ],
  },
  [ReportType.SOY_DEFORESTATION_CAR]: {
    formWidth: 480,
    form: () => <SoyDeforestationForm />,
  },
  [ReportType.SOY_DEFORESTATION_MUNICIPALITY]: {
    formWidth: 480,
    form: () => <SoyDeforestationForm />,
  },

  [ReportType.PORTFOLIO_DIAGNOSIS]: {
    formWidth: 480,
    form: () => <PortifolioDiagnosisForm />,
  },
  [ReportType.CERTIFICATION_2BSVS]: {
    form: () => <Certification2BSvsForm />,
  },
};

export const REPORT_TITLE = {
  [ReportType.VALUATION]: "VALUATION_TITLE",
  [ReportType.INSPECTION]: "INSPECTION_TITLE",
  [ReportType.INSPECTION_FINANCIAL]: "INSPECTION_TITLE",
  [ReportType.AGRO_CREDIT]: "AGRO_CREDIT_TITLE",
  [ReportType.SOCIOENVIRONMENT_COMPLIANCE]: "SOCIOENVIRONMENTAL_TITLE",
  [ReportType.SOCIOENVIRONMENT_PROTOCOL]: "PROTOCOL_BOI_NA_LINHA",
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG]: "PROTOCOL_MARFRIG",
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER]:
    "PROTOCOL_PROJECT_PLANNER",
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR]: "PROTOCOL_EUDR",
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE]: "PROTOCOL_EUDR_CECAFE",
  [ReportType.SOCIOENVIRONMENT_CERTIFICATION]: "CERTIFICATION_TITLE",
  [ReportType.DETAILED_ANALYSIS_DEFORESTATION]: "DEFORESTATION_TITLE",
  [ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR]: "DEFORESTATION_EUDR_TITLE",
  [ReportType.SINISTER]: "SINISTER_TITLE",
  [ReportType.SOY_DEFORESTATION_CAR]: "SOY_DEFORESTATION_TITLE",

  [ReportType.SOY_DEFORESTATION_MUNICIPALITY]:
    "SOY_DEFORESTATION_MUNICIPALITY_TITLE",
  [ReportType.RENOVABIO_CERTIFICATION]: "RENOVABIO_CERTIFICATION_TITLE",
  [ReportType.RENOVABIO_MONITORING]: "RENOVABIO_MONITORING_TITLE",
  [ReportType.PORTFOLIO_DIAGNOSIS]: "PORTIFOLIO_DIAGNOSIS_TITLE",
  [ReportType.CERTIFICATION_2BSVS]: "CERTIFICATION_2BSVS_TITLE",
};

export const CONTENT_TYPE = {
  PDF: "application/pdf",
  XLS: "application/vnd.ms-excel",
  EXCEL: "application/vnd.ms-excel",
  XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
};

export const REPORT_FORMAT_DATA: Record<
  ReportFormat,
  { tooltip: string; text: string }
> = {
  [ReportFormat.EXCEL]: {
    tooltip: "REPORT_AVAILABLE_IN_XLS",
    text: "XLS",
  },
  [ReportFormat.PDF]: {
    tooltip: "REPORT_AVAILABLE_IN_PDF",
    text: "PDF",
  },
  [ReportFormat.XLS]: {
    tooltip: "REPORT_AVAILABLE_IN_XLS",
    text: "XLS",
  },
  [ReportFormat.XLSX]: {
    tooltip: "REPORT_AVAILABLE_IN_XLSX",
    text: "XLSX",
  },
  [ReportFormat.DOC]: { tooltip: "REPORT_AVAILABLE_IN_DOC", text: "DOC" },
  [ReportFormat.DOCX]: { tooltip: "REPORT_AVAILABLE_IN_DOCX", text: "DOCX" },
  [ReportFormat.CSV]: { tooltip: "REPORT_AVAILABLE_IN_CSV", text: "CSV" },
  [ReportFormat.HTML]: { tooltip: "REPORT_AVAILABLE_IN_HTML", text: "HTML" },
  [ReportFormat.JSON]: { tooltip: "REPORT_AVAILABLE_IN_JSON", text: "JSON" },
  [ReportFormat.GEOJSON]: {
    tooltip: "REPORT_AVAILABLE_IN_GEOJSON",
    text: "GeoJSON",
  },
  [ReportFormat.PNG]: { tooltip: "REPORT_AVAILABLE_IN_PNG", text: "PNG" },
};

export const MAX_PROPERTY_LIMIT = 10;

export const FIELD_TO_CHARINPUT = {
  financialInstitution: "FINANCIAL_INSTITUTION",
  institutionName: "INSTITUTION_NAME",
  institutionResponsible: "INSTITUTION_RESPONSIBLE",
  contractNumber: "CONTRACT_NUMBER",
  refBacen: "REF_BACEN",
  crop: "CROP",
  cropYear: "CROP_YEAR",
  totalArea: "TOTAL_AREA",
  language: "LANGUAGE",
  estimatedCropYield: "ESTIMATED_CROP_YIELD",
  cropGroup: "GROW_CROPS",
  soilType: "SOIL_TYPE",
  harvestingDate: "ESTIMATED_HARVEST_DATE",
  seedingDate: "ESTIMATED_SEED_DATE",
  contractExpirationDate: "EXPIRATION_CONTRACT",
  municipality: "MUNICIPALITY",
  queries: "QUERY",
  protocol: "PROTOCOL",
  minArea: "MIN_AREA",
  cutoffDate: "CUTOFF_DATE",
  noticeDate: "NOTICE_DATE",
  event: "EVENT",
  occurrenceDate: "OCCURRENCE_DATE",
  policyNumber: "POLICY_NUMBER",
  query: "QUERY",
  commodity: "COMMODITY",
};

export const SPLIT_FIELDS = ["municipality", "queries"];

export const FIELD_VALIDATION_ERROR_MESSAGE = {
  min: "O campo deve possuir pelo menos {value} caracteres.",
  eq: "O campo deve ter {value} caracteres.",
};

// List vazia = campo obrigatorio
export const FIELD_VALIDATORS = {
  financialInstitution: [{ min: 3 }],
  institutionName: [{ min: 3 }],
  refBacen: [{ min: 9 }],
  contractNumber: [],
  crop: [],
  cropYear: [],
  totalArea: [],
  municipality: [],
  protocol: [],
  queries: [],
  policyNumber: [],
};

export const MODEL_BY_REPORT_TYPE = {
  [ReportType.VALUATION]: input_model_valuation,
  [ReportType.INSPECTION]: input_model_inspection,
  [ReportType.INSPECTION_FINANCIAL]: input_model_inspection_financial,
  [ReportType.SOCIOENVIRONMENT_COMPLIANCE]: input_model_socioenvironmental,
  [ReportType.SOCIOENVIRONMENT_PROTOCOL]:
    input_model_socioenvironmental_protocols,
  [ReportType.SOCIOENVIRONMENT_CERTIFICATION]: input_model_socioenvironmental,
  [ReportType.DETAILED_ANALYSIS_DEFORESTATION]:
    input_model_deforestation_analysis,
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR]: input_model_eudr,
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_EUDR_CECAFE]: input_model_eudr_cecafe,
  [ReportType.SINISTER]: input_model_sinister,
  [ReportType.SOY_DEFORESTATION_CAR]: input_model_soy_deforestation_car,
  [ReportType.SOY_DEFORESTATION_MUNICIPALITY]:
    input_model_soy_deforestation_municipality,
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_PROJECT_PLANNER]:
    input_model_project_planner,
  [ReportType.SOCIOENVIRONMENT_PROTOCOL_MARFRIG]: input_model_protocol_marfrig,
  [ReportType.DETAILED_ANALYSIS_DEFORESTATION_EUDR]: input_model_eudr,
  [ReportType.RENOVABIO_CERTIFICATION]: model_renova_bio_input,
  [ReportType.RENOVABIO_MONITORING]: model_renova_bio_input,
  [`${ReportType.PORTFOLIO_DIAGNOSIS}_${ReportPortifolioDiagnosisBatchDocumentType.CPF_CNPJ}`]:
    portifolio_diagnosis_document_batch_input,
  [`${ReportType.PORTFOLIO_DIAGNOSIS}_${ReportPortifolioDiagnosisBatchDocumentType.CAR}`]:
    portifolio_diagnosis_car_batch_input,
  [ReportType.CERTIFICATION_2BSVS]: certification_2bsvs_input,
};
