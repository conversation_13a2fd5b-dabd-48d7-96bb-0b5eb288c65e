import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { initReactI18next } from "react-i18next";

export const RESOURCES = {
  "pt-BR": {
    translation: {
      DISCOVER_REPORTS: "Conheça os relatórios",
      MY_REQUESTS: "Minhas solicitações",
      CLICK: "Clique",
      DRAW: "Desenhe",
      REMOVE: "Remover",
      REMOVE_ITEM: "Remover item",
      ADD_ITEM: "Adicionar item",
      NO_DATA: "Não há dados",
      INVALID_CAR_CODE: "Código CAR inválido",

      FILL_EVERY_FIELD: "Preencha todos os campos",
      ALERT_FILL_COORDINATE_FIELDS:
        "Para continuar, preencha todos os campos de coordenadas.",
      ALERT_FILL_LATITUDE: "Informe a latitude da propriedade",
      ALERT_FILL_LONGITUDE: "Informe a longitude da propriedade",
      LATITUDE: "Latitude",
      LONGITUDE: "Longitude",
      FILL_FIELD: "Informe",
      VIEW_PROPERTY: "Visualizar propriedade",
      REQUEST_REPORT: "Solicitar",
      NEW: "NOVO",
      COMING_SOON: "Em breve",
      REQUESTS_REPORTS_COUNT: "{{requests}} de {{reports}} disponíveis",
      STATUS_WAITING: "Aguardando início",
      ERROR: "Erro",
      PROCESSING: "Em processamento",
      DONE: "Disponível",
      WARNING: "Alerta",
      STATUS_ALL: "Todos os status",
      TYPE_CITY_NAME: "Digite o nome da cidade",
      TAG_INPUT_HINT: "Tags para facilitar a busca (opcional)",
      DOCUMENT_OR_NAME: "CPF/CNPJ ou nome do produtor",
      NEW_CONTACT: "Novo contato",
      NEW_PROPERTY: "Nova propriedade",
      AVAILABLE_PROPERTIES: "Propriedades disponíveis desse produtor",
      HIDE_MAP: "Ocultar mapa",
      SHOW_MAP: "Exibir mapa",
      OBSERVATIONS_PLACEHOLDER: "Observações (opcional)",
      FILE_NOT_SENT: "Arquivo não enviado",
      ALERT_FILE_SIZE:
        "Certifique-se de que o arquivo possui {{sizeMb}}MB ou menos.",
      ALERT_FILE_SAME_NAME: "Já existe um arquivo com este nome",
      HINT_REPORT_MODEL_DOWNLOAD:
        "Baixe aqui o modelo padrão para upload em lote.",
      HINT_FILE_UPLOAD: "Anexe aqui um .csv utilizando o modelo padrão",
      DRAG_DROP_OR: "Arraste e solte aqui seu arquivo ou",
      CLICK_TO_UPLOAD_FROM_FILES: "clique aqui para procurar no computador",
      FILE_TOTAL: "Total desse arquivo",
      SELECTED_FILE: "Arquivo selecionado",
      REPORT: "relatório",
      REPORTS: "relatórios",
      NOTIFICATION_NEW_CONTACT_SUCCESS: "Contato(s) cadastrado(s)",
      NOTIFICATION_NEW_CONTACT_SUCCESS_DESC:
        "Contato(s) cadastrado(s) com sucesso!",
      QUERY_NOT_MADE: "Consulta não realizada",
      QUERY_PARTIALLY_MADE: "Consulta parcialmente realizada",
      ERR_INSUFFICIENT_CREDITS_QUERY:
        "Não há créditos suficientes para realizar a consulta.",
      ERR_CONTACT_EXISTS:
        "Um dos contatos consultados já existe na plataforma.",
      ALERT_IMPORTED_DOCS:
        "Demais documentos ({{remaining}}) foram importados corretamente.",
      ALERT_QUERY_MADE_PARTIALLY: "Consulta parcialmente realizada",
      ALERT_SOME_CONTACTS_ALREADY_EXISTS:
        "Alguns contatos consultados já existem na plataforma: ",
      ERROR_CONTACTS_BLOCKED:
        "Não é possível retornar informações do contato devido a bloqueios.",
      ERR_INVALID_DOCUMENT: "Documento inválido",
      ERR_INVALID_DOCUMENT_DESC: "Siga o modelo csv de exemplo",
      ERR_INVALID_DOCUMENTS: "Documentos inválidos",
      ERR_INVALID_DOCUMENTS_DESC: "Um ou mais documentos são inválidos",
      ERR_GENERIC_QUERY: "Não foi possível realizar a consulta.",
      TRY_AGAIN_IN_MINUTES: "Tente novamente após {{minutes}} minutos",
      CANCEL: "Cancelar",
      CREATE_CONTACTS: "Criar contatos",
      ALERT_EMPTY_CAR: "CAR em branco",
      ALERT_EMPTY_CAR_DESC:
        "Preencha o CAR do contato para adicionar um novo campo.",
      CAR_ALREADY_EXISTS: "CAR já adicionado",
      CAR_ALREADY_EXISTS_DESC: "Adicione um código diferente.",
      IDENTIFICATION_TYPE: "Tipo de Identificação",
      UNIQUE_REGISTRATION: "Registro Único",
      INTERNAL_CODE: "Código Interno",
      NAME_SEARCHED_AUTOMATICALLY: "Nome será buscado automaticamente",
      CONTACT_NAME: "Nome do contato",
      SEARCH_PROPERTIES_AUTOMATICALLY: "Pesquisar propriedades automaticamente",
      INFO_QUERY_TAKE_LONGER:
        "Sua consulta pode ser menos precisa e levar mais tempo.",
      CITY_SELECTION_UNAVAILABLE:
        "Seleção de cidades indisponível para esse tipo de documento",
      CONTACT_PROPERTIES_CITIES:
        "Cidades onde esse contato possui propriedades",
      PROPERTY_CAR_PLACEHOLDER:
        "Digite aqui o CAR da sua propriedade (opcional)",
      IDENTIFICATION_DOCUMENT: "CPF/CNPJ do produtor",
      IDENTIFICATION_UNIQUE_REGISTER: "Registro Único do produtor",
      IDENTIFICATION_INTERNAL_CODE: "Código Interno do produtor",
      REPORT_CATEGORIES_HEADER:
        " categorias de relatórios disponíveis para você",
      REPORT_CATEGORY_HEADER: "1 categoria de relatório disponível para você",
      FINANCIAL_INSTITUTION: "Instituição financeira",
      INSTITUTION_OFFICER: "Reponsável pela instituição",
      BACEN_REFERENCE: "Referência BACEN",
      FINANCED_CROP_YEAR: "Safra financiada",
      FINANCED_CROP: "Cultura financiada",
      TOTAL_FINANCED_AREA: "Área total financiada (ha)",
      ESTIMATED_CROP_YIELD: "Produção esperada (ton)",
      PROPERTY_NOT_FOUND: "Propriedade não encontrada",
      PROPERTY_NOT_FOUND_DESC:
        "Nenhuma propriedade encontrada nessas coordenadas.",
      CAR_NOT_FOUND: "CAR não encontrado",
      PROPERTY_NOT_ADDED: "Propriedade não adicionada",
      PROPERTY_NOT_ADDED_DESC: "Não foi possível adicionar a propriedade",
      NO_PROPERTIES_FOUND: "Nenhuma propriedade foi encontrada",
      ERR_FILE_CORRUPTED:
        "Arquivo corrompido. Por favor, escolha um arquivo válido.",
      PROPERTY_ADDED_SUCCESSFULLY: "Propriedade adicionada com sucesso!",
      NO_PROPERTY_FOUND_IN_COORDS:
        "Nenhuma propriedade encontrada nessas coordenadas.",
      INCRA_CODE_NOT_FOUND: "Código INCRA não encontrado",
      VALUE_INVALID: "{{value}} inválido.",
      QUERY_TEMPORARILY_UNAVAILABLE: "Consulta temporariamente indisponível",
      REGISTER_NEW_PROPERTY: "Cadastrar nova propriedade",
      PROPERTY_NAME: "Nome da propriedade",
      CLICK_OR_DRAW: "Clique ou desenhe",
      PROPERTY_CITY: "Cidade da propriedade",
      SEARCH_PROPERTY_CITY: "Pesquise aqui a cidade da sua propriedade",
      KML_OR_SHP: "KML ou SHP",
      INSERT_PROPERTY_CAR: "Digite o CAR da sua propriedade",
      INSERT_PROPERTY_INCRA_CODE: "Digite o SIGEF ou SNCI da sua propriedade",
      INFO_PROPERTY_ICON_CLICK:
        "Clique no ícone da geometria para selecionar a propriedade a ser cadastrada",
      INFO_SUBAREA_ICON_CLICK:
        "Clique no ícone da subárea para selecionar a área a ser cadastrada",
      SEARCH: "Buscar",
      FILTERS_PLACEHOLDER:
        "Busque por CPF, CNPJ, nome do proprietário ou propriedade",
      REPORT_TYPE: "Tipo do relatório",
      EVERY_REPORT_TYPE: "Todos os tipos de relatório",
      REPORT_STATUS: "Status do relatório",
      SELECT: "Selecione",
      APPLY: "Aplicar",
      REQUESTED_BY: "Solicitado por",
      REQUESTED_AT: "Solicitado em",
      CLEAR: "Limpar",
      VALUATION_TITLE: "Valoração de Imóveis Rurais",
      INSPECTION_TITLE: "Fiscalização por Sensoriamento Remoto",
      ITEM_NOT_ADDED: "Item não adicionado",
      SELECT_FARMER_AND_PROPERTY:
        "Selecione o produtor e a propriedade para adicionar um relatório à solicitação.",
      ITEM_ALREADY_EXISTS: "O item adicionado já está na lista.",
      FILL_EVERY_FIELD_CORRECTLY:
        "Preencha corretamente todos os campos para adicionar o relatório.",
      ATTACH_AT_LEAST_ONE_FILE:
        "Anexe pelo menos um arquivo para solicitar os relatórios.",
      REPORTS_REQUESTED_SUCCESSFULLY: "Relatórios solicitados com sucesso",
      REPORT_REQUESTED_SUCCESSFULLY: "Relatório solicitado com sucesso",
      REPORTS_NOT_REQUESTED: "Relatórios não solicitados",
      REPORT_NOT_REQUESTED: "Relatório não solicitado",
      FILL_FIELDS_TO_REQUEST:
        "Preencha todos os campos para solicitar o relatório.",
      REQUEST_METHOD_LABEL: "Desejo solicitar um novo relatório:",
      MANUALLY: "Manualmente",
      BATCH: "Em lote",
      REPORT_CHARACTERISTICS: "Características desse relatório",
      TAGS_PLACEHOLDER: "Tags para facilitar a busca (opcional)",
      ACTION_REQUEST_REPORT: "Solicitar relatório",
      ACTION_REQUEST_N_REPORTS: "Solicitar esses {{value}} reports",
      ACTIONS: "Ações",
      DOWNLOAD_REPORTS_ZIP: "Baixar ZIP",
      DOWNLOAD_STARTED: "Download iniciado",
      ALERT_NO_REPORTS_AVAILABLE_REQUEST:
        "Não há nenhum relatório disponível nesta solicitação",
      ERR_REPORTS_DOWNLOAD: "Não foi possível fazer o download dos relatórios.",
      ERR_REPORTS_DOWNLOAD_ZIP: "Nenhum relatório disponível foi encontrado.",
      REQUEST_REPORT_UPDATE: "Solicitar atualização",
      REQUEST_REPORTS_UPDATE: "Solicitar atualizações",
      REPORT_UPDATE_SUCCESS: "Atualização solicitada com sucesso",
      REPORT_UPDATES_SUCCESS: "Atualizações solicitadas com sucesso",
      ERROR_UPDATE_REPORT:
        "Não foi possível solicitar a atualização do relatório",
      ERROR_UPDATE_REPORTS:
        "Não foi possível solicitar a atualização do(s) relatório(s)",
      TRY_AGAIN_LATER: "Tente novamente mais tarde.",
      UPDATE_REPORTS: "Atualiza relatórios",
      REQUEST_FOUND: "1 solicitação encontrada",
      REQUESTS_FOUND: "{{count}} solicitações encontradas",
      WITH_TOTAL_REPORT: "com um total de 1 relatório",
      WITH_TOTAL_REPORTS: "com um total de {{count}} relatórios",
      DOWNLOAD_REPORT: "Baixar relatório",
      DOWNLOAD_REPORT_EXTENSION: "Baixar {{extension}}",
      DOWNLOAD_FINISHED: "Download concluído",
      ERR_REPORT_DOWNLOAD: "Erro ao fazer download do relatório",
      FILTERS_APPLIED: "Filtros aplicados",
      COLUMN_DOCUMENT: "CPF / CNPJ informado",
      INTEREST_AREA: "Área de interesse",
      MODULES_TYPES: "Tipos de módulo",
      CREATED_AT: "Gerado em",
      HOUR_OF: "{{hour}} de",
      REPORT_COUNT: "Quantidade de relatórios",
      SOYBEAN: "Soja",
      COTTON: "Algodão",
      CORN: "Milho",
      RICE: "Arroz",
      WHEAT: "Trigo",
      CANE: "Cana-de-açúcar",
      SUGAR_CANE: "Cana-de-açúcar",
      VALUATION_REPORT_DESC:
        "Análise que permite obter o valor do imóvel (R$) de forma rápida, remota (imagens de satélite) e com validação de analistas especializados.",
      INSPECTION_REPORT_DESC:
        "Inspeção das operações de crédito rural constituído pela resolução BACEN nº 4.895 de 26/02/2021. O que entregamos?",
      INSPECTION_REPORT_BULLETPOINT_1: "Detecção da área plantada e colheita;",
      INSPECTION_REPORT_BULLETPOINT_2:
        "Confirmação de cultura e avaliação da performance;",
      REPORT_AVAILABLE_IN_PDF: "Relatório disponibilizado em .pdf",
      REPORT_AVAILABLE_IN_XLS: "Relatório disponibilizado em .xls",
      REPORT_AVAILABLE_IN_XLSX: "Relatório disponibilizado em .xlsx",
      REPORT_AVAILABLE_IN_DOC: "Relatório disponibilizado em .doc",
      REPORT_AVAILABLE_IN_DOCX: "Relatório disponibilizado em .docx",
      REPORT_AVAILABLE_IN_CSV: "Relatório disponibilizado em .csv",
      REPORT_AVAILABLE_IN_HTML: "Relatório disponibilizado em .html",
      REPORT_AVAILABLE_IN_JSON: "Relatório disponibilizado em .json",
      REPORT_AVAILABLE_IN_GEOJSON: "Relatório disponibilizado em .geojson",
      REPORT_AVAILABLE_IN_PNG: "Relatório disponibilizado em .png",
      NOT_FOUND_GEOM: "Geometria não encontrada.",
      DUPLICATE_MATRICULATION_CODE:
        "CAR e Incra foram enviados. Envie apenas um código para obtenção da geometria.",
      EMPTY_GEOMETRY_PARAMS:
        "Nenhum parâmetro para obter a geometria foi enviado.",
      WRONG_DATES: "A data da colheita está anterior à data de semeadura.",
      INVALID_OR_NONEXISTENT_PARAMETER: "Parâmetro inválido ou inexistente",
      NONEXISTENT_DOCUMENT_AND_CAR:
        "Parâmetro CAR e Documento não foram enviados",
      NOT_FOUND_CONTACT_IN_BASE: "Contato ou CAR não encontrado no sistema",
      NOT_FOUND_PROTOCOL_IN_BASE:
        "Protocolo no SmartESG em processamento ou não encontrado",
      CONNECTION_FAILED_SMART: "Falha na conexão com o Smart ESG",
      ERROR_IN_DOCUMENT: "Erro ao tentar ler o arquivo",
      REF_BACEN: "Referência BACEN",
      DOCUMENT: "Documento",
      CROP: "Cultura",
      CROP_YEAR: "Safra",
      STATE: "Estado",
      MUNICIPALITY: "Município",
      MUNICIPALITIES: "Municípios",
      TOTAL_AREA: "Área total",
      TOTAL_AREA_HA: "Área total (ha)",
      SOIL_TYPE: "Tipos de solo",
      CROP_GROUP: "Grupos de cultura",
      SOIL_SANDY: "Arenoso",
      SOIL_AVERAGE: "Médio",
      SOIL_CLAYISH: "Argiloso",
      GROUP_N: "Grupo {{group}}",
      PERENNIAL: "Perene",
      SEEDING_DATE: "Date de plantio",
      HARVESTING_DATE: "Data de colheita",
      CONTRACT_EXPIRE_DATE: "Vencimento do contrato",
      AVAILABLE_SUBAREAS: "Subáreas disponíveis",
      NEW_SUBAREA: "Nova subárea",
      REGISTER_NEW_SUBAREA: "Cadastrar nova subárea",
      SUBAREA_NAME: "Nome da subárea",
      SUBAREA_ADDED_SUCCESSFULLY: "Subárea adicionada com sucesso!",
      ALERT_SUBAREA_NOT_IN_PROPERTY:
        "A subárea não está contida na propriedade.",
      REGISTER: "Cadastrar",
      REGISTER_N_SUBAREAS: "Cadastrar {{value}} subáreas",
      SUBAREA_PREFIX: "Prefixo da(s) subárea(s)",
      YES: "Sim",
      NO: "Não",
      ALERT_TAB_CHANGE_SUBAREA:
        "Se a aba for mudada você perderá as subáreas já adicionadas. Tem certeza que deseja prosseguir?",
      AUTOMATIC_SUBAREAS: "Subáreas automáticas",
      CREATE_AUTOMATIC_SUBAREAS: "Gerar subáreas automaticamente",
      INFO_AUTOMATIC_SUBAREAS:
        "A geração automática de subáreas é realizada com base na 1ª safra 2022/2023 e contempla somente as principais culturas que mapeamos (soja, milho, algodão, cana-de-açúcar e arroz).",
      SELECT_ALL_SUBAREAS: "Selecionar todas as subáreas",
      TOTAL_IDENTIFIED_AGRICULTURAL_AREA:
        "Área Agrícola Total Identificada {{value}} ha",
      SELECTED_AGRICULTURAL_AREA: "Área Agrícola Selecionada {{value}} ha",
      ALERT_SUBAREA_SAME_NAME: "Já existe uma subárea com o mesmo nome",
      SIGNATURE: "Assinatura",
      WITH_SIGNATURE: "Com assinatura",
      NO_SIGNATURE: "Sem assinatura",
      SIGN_REPORTS: "Assinar relatórios",
      SIGN_REPORTS_INFO:
        "Ao marcar essa opção você concorda em ter os relatórios assinados por um responsável técnico.",
      SIGN_POPUP_ALERT: "Assinatura de relatório",
      SIGN_POPUP_ALERT_DESCRIPTION_SIGNED:
        "Você optou por solicitar o(s) relatório(s) com a assinatura de um responsável técnico. Se desejar, pode voltar e revisar essa opção antes de continuar.",
      SIGN_POPUP_ALERT_DESCRIPTION_UNSIGNED:
        "Você optou por solicitar o(s) relatório(s) sem a assinatura de um responsável técnico. Se desejar, pode voltar e revisar essa opção antes de continuar.",
      SOCIOENVIRONMENTAL_TITLE: "Conformidade Socioambiental",
      SOCIOENVIRONMENTAL_DESC:
        "Escolha critério(s) socioambiental(is) para compor sua análise. Dispomos mais de 9 bases para interseccionar com o produtor e/ou imóvel rural. Dentre elas: Embargo IBAMA, PRODES, Terra Indígena, entre outras.",
      IBAMA_EMBARGOES: "Embargo IBAMA",
      PROTOCOL_BOI_NA_LINHA_DESC:
        "Análise que permite monitorar, a partir de consultas geoespaciais e análise de documentos se a compra de gado advinda do bioma Amazônia está em conformidade socioambiental. São analisados também: Desmatamento ilegal pós 01/08/2008, Embargos, Terra Indígena e Unidade de Conservação, Trabalho Escravo, entre outros.",
      PROTOCOL_EUDR_DESC:
        "Análise que permite monitorar, a partir de consultas geoespaciais e listas públicas se a compra de produtos adquiridos (soja, café, gado etc.) sejam legais e não estejam vinculados a terras desmatadas ou degradadas após 31 de dezembro de 2020. São analisados também: Embargos e PRODES, Terra Indígena e Unidade de Conservação, Trabalho Escravo, entre outros.",
      PROTOCOL_BOI_NA_LINHA: "Protocolo Boi na Linha",
      PROTOCOL_MARFRIG: "Protocolo Marfrig",
      PROTOCOL_PROJECT_PLANNER: "Conformidade Socioambiental | Projetistas",
      PROTOCOL_EUDR: "Conformidade Socioambiental | EUDR",
      PROTOCOL_EUDR_CECAFE: "Conformidade Socioambiental | Protocolo Cecafe",
      AVAILABLE_PROTOCOLS: "Protocolos disponíveis",
      CERTIFICATION_TITLE: "Certificação",
      REPORT_AVAILABLE_SOON: "Este relatório estará disponível em breve!",
      SOCIOENVIRONMENTAL_CRITERIA: "Critérios Socioambientais",
      ASSENTAMENTOS: "Assentamentos",
      UNIDADES_CONSERVACAO: "Unidades de Conservação",
      QUILOMBOS: "Quilombos",
      EMBARGO_LDI: "Embargos LDI Pará",
      EMBARGO_IBAMA: "Embargos Ibama",
      EMBARGO_ICMBIO: "Embargos ICMBio",
      EMBARGO_SEMA: "Embargos SEMA - MT",
      TERRAS_INDIGENAS: "Terras Indígenas",
      TRABALHO_ESCRAVO: "Trabalho Análogo à Escravidão",
      SUSCETIBILIDADE_A_INUNDACAO: "Suscetibilidade a inundação",
      ALERTA_MAPBIOMAS: "Alertas Mapbiomas",
      READ_MORE: "Saiba mais",
      DOWNLOAD_TEMPLATE: "Baixar modelo",
      REPORT_BADGE_NEW: "Novidade",
      ALERT_LOW_CREDITS:
        "Seu saldo está quase no fim, solicite adição de créditos",
      DETAILED_ANALYSIS_DEFORESTATION_DESC:
        "Solicite uma análise detalhada dos desmatamentos identificados pelo PRODES, DETER ou Embargos (IBAMA, ICMBio ou Estaduais).",
      DEFORESTATION_TITLE: "Análise Detalhada de Desmatamento",
      DEFORESTATION_EUDR_TITLE: "Análise Detalhada de Desmatamento - EUDR",
      DEFORESTATION_EUDR_DESCRIPTION:
        "Solução que permite identificar, dentro de imóveis rurais, a ocorrência de desmatamento após 30/12/2020.",
      REQUEST_CONTESTATION: "Solicitar Análise Detalhada",
      REPORT_CONTESTATION_SUCCESS: "Análise Detalhada solicitada com sucesso",
      ERROR_CONTESTATION_REPORT: "Erro ao solicitar contestação",
      LANGUAGE: "Idioma",
      SELECT_REPORT_LANGUAGE: "Selecione o idioma do relatório.",
      PORTUGUESE: "Português",
      ENGLISH: "Inglês",
      SPANISH: "Espanhol",
      INVALID_BIOMES: "A propriedade está em um bioma inválido",
      INVALID_BIOMES_DESCRIPTION:
        "Certifique-se que a propriedade informada está no bioma Cerrado ou Amazônia.",
      FOCUS_ON_MAP: "Focar no mapa",
      NO_SUBAREAS_IN_PROPERTY:
        "Não foram identificadas subáreas na propriedade.",
      AGRO_CREDIT_TITLE: "Relatório Gerencial de Crédito Agro",
      AGRO_CREDIT_DESC:
        "Analise e monitore o risco de crédito e socioambiental de toda sua carteira através dos nossos Agro Score e Score ESG, permitindo acionabilidade para possíveis casos de inadimplência e RJs.",
      UPLOAD_DOCUMENT_LIST: "Envie sua lista de documentos",
      NEXT: "Próximo",
      TAG_NAME: "Nome da tag",
      IDENTIFICATION_TAG: "Tag de identificação",
      IDENTIFICATION_TAG_DESCRIPTION:
        "Adicione uma tag para identificar o seu relatório",
      UPLOAD_DOCUMENTS_FILE: "Carregue a planilha com a sua base",
      UPLOAD_DOCUMENTS_FILE_DESCRIPTION:
        'Para importar uma planilha, é necessário que ela esteja no padrão da plataforma (CPF ou CNPJ com somente números*). Caso precise, você pode fazer o download do modelo clicando no botão "Baixar modelo".',
      DEBTOR: "Devedor",
      DEBTORS: "Devedores",
      REVIEW_UPLOADED_DOCUMENTS: "Confirme os documentos encontrados",
      REVIEW_UPLOADED_DOCUMENTS_DESCRIPTION:
        "Os documentos abaixo não foram encontrados. Caso passe para a próxima etapa, eles não serão considerados. Caso deseje inserir, alterar ou excluir mais documentos é necessário editar o arquivo e realizar um novo upload.",
      BACK: "Voltar",
      VALID_DOCUMENTS: "Documentos válidos",
      INVALID_DOCUMENTS: "Documentos inválidos",
      INVALID_DOCUMENTS_DESCRIPTION:
        "Os documentos abaixo não foram encontrados. Caso passe para a próxima etapa, eles não serão considerados. Caso deseje inserir, alterar ou excluir mais documentos é necessário editar o arquivo e realizar um novo upload.",
      DOWNLOAD_XLS_RELATION: "Baixar relação em .xls",
      REPORT_SCORE_SELECT:
        "Selecione quais Scores gostaria de obter em seu relatório",
      CREDIT_RISK: "Risco de Crédito",
      CREDIT_RISK_DESCRIPTION:
        "O Agro Score utiliza milhares de variáveis da maior base de dados da América Latina, desenhado para analisar o risco na concessão do Crédito rural. A pontuação indica a probabilidade de inadimplência de cada produtor de toda a carteira avaliada.",
      ESG_RISK: "Risco ESG (Em breve)",
      ESG_RISK_DESCRIPTION:
        "Através de modelos de IA e Machine Learning, o Score ESG avalia questões Socioambientais, como embargos, infrações no Ibama, sobreposições, etc. e classifica o risco de produtores, suas propriedades e pessoas relacionadas. Para mais informações contate seu especialista de vendas.",
      SELECTED_SCORES: "Scores selecionados",
      SUMMARY: "Resumo",
      FINISH: "Concluir",
      DOCUMENTS: "Documentos",
      STANDART: "Padrão",
      BACEN_REPORT: "Resolução BACEN",
      INSTITUTION_NAME: "Nome da instituição",
      CONTRACT_NAME: "Número do contrato",
      COFFEE: "Café",
      NO_VALID_DOCUMENTS_IN_FILE:
        "Não exitem documentos válidos no arquivo enviado",
      MIN_AREA_HA: "Área mínima (ha)",
      CUTOFF_DATE: "Data de corte",
      UNLOCK: "Desbloquear",
      DOWNLOAD_PROPERTY_UNIFIED: "Baixar GeoJSON Unificado",
      DOWNLOAD_PROPERTY_INDIVIDUAL: "Baixar GeoJSON Individual",
      ERR_PROPERTIES_DOWNLOAD: "Erro ao fazer download das geometrias",
      CONFIRM_REQUEST: "Confirmar solicitação",
      NO_ACTION_FOR_THIS_REQUEST:
        "Não há ações disponíveis para esta requisição neste momento",
      REFBACEN_NOT_FOUND:
        "Ref Bacen não encontrado. Digite as informações de forma manual.",
      ALERT_NEW_GEOMETRY_BACEN:
        "Deseja salvar a geometria relacionada ao código BACEN na sua base?",
      RELATED_TO_CODE_VALUE: "Referente ao código BACEN {{value}}",
      REQUEST_REPORTS_XLS_DOWNLOAD: "Baixar XLS",
      SEARCHING_REFBACEN:
        "Buscando informações adicionais pela Referência BACEN {{value}}",
      NOTICE_DATE: "Data do aviso",
      OCCURRENCE_DATE: "Data da ocorrência",
      EVENT: "Evento",
      POLICY_NUMBER: "Número da apólice",
      SINISTER_TITLE: "Relatório de Sinistro",
      INSUFICIENT_FUNDS: "Você não tem créditos suficientes.",
      COMMODITY: "Commodity",
      BEEF: "Carne bovina",
      MORATORIA_DA_SOJA: "Moratória da soja",
      PROTOCOLO_VERDE_DOS_GRAOS: "Protocolo verde dos grãos",
      CHECK_ALL: "Selecionar Todos",
      DAP: "DAP",
      SOIL_USE_CAR: "Uso de Solo - CAR",
      FLORESTA_PUBLICA_TIPO_B: "Florestas Públicas Tipo B",
      SITIOS_ARQUEOLOGICOS: "Sítios Arqueológicos",
      UNIDADES_DE_CONSERVACAO_ZA: "Zona de Amortecimento (UC)",
      TERRAS_INDIGENAS_ZA: "Zona de Amortecimento (TI)",
      REQUIRED_FIELD: "Campo obrigatório",
      PROPERTIES: "Propriedades",
      SEARCH_MUNICIPALITY: "Buscar município",
      SEARCH_STATE: "Buscar propriedade",
      SEARCH_LOCATION_STATE: "Buscar estado",
      SEARCH_CAR: "Buscar propriedade",
      SELECTED_MUNICIPALITIES: "Municípios selecionados",
      SELECTED_STATES: "Estado(s) selecionados",
      SELECTED_PROPERTIES: "Propriedades selecionadas",
      SELECT_AT_LEAST_ONE_PROPERTY: "Selecione pelo menos uma propriedade",
      SELECT_AT_LEAST_ON_MUNICIPALITY: "Selecione pelo menos um município",
      INFO_UPLOAD_SOY_DEFORESTATION:
        "Para importar uma planilha, é necessário que ela esteja no padrão da plataforma, no formato .csv. Caso precise, você pode fazer o download do modelo clicando no botão “Baixar CSV exemplo”.",
      INFO_UPLOAD_CSV_RENOVA_BIO:
        "Para importar uma planilha, é necessário que ela esteja no padrão da plataforma, no formato .csv. Caso precise, você pode fazer o download do modelo clicando no botão “Baixar CSV exemplo”.",
      INFO_UPLOAD_CSV_PORTIFOLIO_DIAGNOSIS:
        "Para importar uma planilha, é necessário que ela esteja no padrão da plataforma, no formato .csv. Caso precise, você pode fazer o download do modelo clicando no botão “Baixar CSV exemplo”.",

      CHOOSE_FILES: "Escolha os arquivos que deseja",
      DOWNLOAD_SAMPLE_CSV: "Baixar CSV exemplo",
      SOY_DEFORESTATION_TITLE: "Soja em desmatamento",
      SOY_DEFORESTATION_MUNICIPALITY_TITLE:
        "Soja em desmatamento por município",
      MUST_SELECT_SOME_CRITERIA:
        "Selecione ao menos 1 Critério Sócio Ambiental",
      QUERIES: "Critérios Socioambientais",
      INVALID_OR_NO_INTERSECTION_WITH_PROPERTY:
        "Gleba inválida ou sem interseção com propriedade",
      CAR_CODE_ALREADY_REGISTERED: "Código CAR já cadastrado",
      SOIL_TYPE_CROP_GROUP_INFO:
        "Quando as informações referentes ao Tipo Solo e Grupo de Cultura não forem informadas, o risco ZARC, balanço hídrico e estimativa de produtividade não são apresentadas no relatório.",
      RENOVABIO_CERTIFICATION_OPTION_LABEL: "Certificação",
      RENOVABIO_CERTIFICATION_TITLE: "Certificação Renovabio",
      RENOVABIO_MONITORING_TITLE: "Monitoramento Renovabio",
      PORTIFOLIO_DIAGNOSIS_TITLE: "Diagnóstico de Carteira",
      RENOVABIO_MONITORING_OPTIONAL_LABEL: "Monitoramento",
      CERTIFICATION_2BSVS_TITLE: "Certificação 2BSvs",
      REQUEST_NEW_REPORT_TITLE: "Solicitar novo relatório",
      VALID_CARS: "CARs válidos",
      INVALID_CARS: "CARs inválidos",
      REPORT_TYPE_OPTION: "Modelo de relatório",
      ROW: "Linha",
      ROWS: "Linhas",
      VALID_ROWS: "Linhas válidas",
      INVALID_ROWS: "Linhas inválidas",
      INVALID_ROWS_DESCRIPTION:
        "Os dados abaixo não são válidos. Caso solicite o relatório, eles não serão considerados. Caso deseje inserir, alterar ou excluir mais documentos é necessário editar o arquivo e realizar um novo upload.",
      TITLE_FOR_SECTION: "Título para seção",
      CAR_CODE: "Código CAR",
      ERROR_CREATE_REPORT: "Erro ao solicitar o relatório.",
      SUPPORTED_FORMATS: "Formatos suportados: ",
      PORTIFOLIO_DIAGNOSIS_MODULES_SELECTED: "Módulos selecionados",
      PORTIFOLIO_DIAGNOSIS_SELECT_MODULE_PLACEHOLDER: "Selecionar módulos",
      ADD_ONE_MODULE_IS_MANDATORY:
        "É necessário adicionar pelo menos 1 módulo.",
    },
  },
};

const OPTIONS = {
  order: ["navigator", "htmlTag", "querystring"],
};

i18n
  .use(initReactI18next)
  .use(LanguageDetector)
  .init({
    detection: OPTIONS,
    fallbackLng: "pt-BR",
    resources: RESOURCES,
    interpolation: {
      escapeValue: false,
    },
  });

export { i18n };
