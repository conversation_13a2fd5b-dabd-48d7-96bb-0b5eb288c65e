import { DataProvider } from "@contexts";
import "./index.css";
import { RootContainer } from "./root.styles";
import { Routes } from "./routes";
import { AnalyticsDataLayerProvider } from "./contexts/AnalyticsDataLayerContext";
import { Provider } from "react-redux";
import { store } from "./store";
import { FC } from "react";

/** TMP Workaround because of different versions of react in Micro front-ends */
const ReduxProvider = Provider as any;

const Root: FC = () => {
  return (
    <ReduxProvider store={store}>
      <RootContainer>
        <DataProvider>
          <AnalyticsDataLayerProvider>
            <Routes />
          </AnalyticsDataLayerProvider>
        </DataProvider>
      </RootContainer>
    </ReduxProvider>
  );
};

export default Root;
