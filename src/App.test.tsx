import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import App from './App';
import { I18nextProvider } from 'react-i18next';
import { AnalyticsDataLayerProvider } from './contexts/AnalyticsDataLayerContext';
import { i18n } from './i18n';

describe('App Component', () => {
    it('renders the Home tab by default', () => {
        render(
            <MemoryRouter>
                <AnalyticsDataLayerProvider>
                    <I18nextProvider i18n={i18n}>
                        <App />
                    </I18nextProvider>
                </AnalyticsDataLayerProvider>
            </MemoryRouter>
        );

        const homeSection = screen.getByTestId('pb-t-home');
        expect(screen.getAllByText(/Conheça os relatórios/i).length).toBeGreaterThan(0);
    });


    it('switches to My Requests tab when clicked', () => {
        render(
            <MemoryRouter>
                <AnalyticsDataLayerProvider>
                    <I18nextProvider i18n={i18n}>
                        <App />
                    </I18nextProvider>
                </AnalyticsDataLayerProvider>
            </MemoryRouter>
        );

        const myRequestsTab = screen.getByText(/Minhas solicitações/i);
        fireEvent.click(myRequestsTab);
    });
});
