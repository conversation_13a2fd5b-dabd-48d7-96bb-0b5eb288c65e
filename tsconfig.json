{"extends": "ts-config-single-spa", "compilerOptions": {"jsx": "react-jsx", "target": "es6", "declarationDir": "dist", "paths": {"@services": ["./src/services"], "@types": ["./src/types"], "@utils": ["./src/utils"], "@components": ["./src/components"], "@constants": ["./src/constants"], "@constants/*": ["./src/constants/*"], "@contexts": ["./src/contexts"], "@/*": ["./src/*"]}}, "files": ["src/brain-pb-agro-report-frontend.tsx"], "include": ["src/**/*"]}