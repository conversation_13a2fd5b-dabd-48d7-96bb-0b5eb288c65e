sonar.projectKey=pb-agro-report-frontend
 
# Código fonte
sonar.sources=src
 
# Diretório onde estão os testes (mantém src)
sonar.tests=src
 
# Define exatamente quais arquivos são testes
sonar.test.inclusions=**/*.test.tsx,**/*.spec.tsx,**/*.test.ts,**/*.spec.ts
 
# Caminho do relatório de cobertura LCOV gerado pelo Jest
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.clover.reportPaths=coverage/clover.xml
 
# Exclui arquivos que não devem ser analisados
 
sonar.exclusions=**/node_modules/**,\
**/build/**,\
**/dist/**,\
**/coverage/**,\
**/__mocks__/**,\
**/types/**,\
**/assets/**,\
**/*.styles.ts,\
**/*.styles.tsx

sonar.coverage.exclusions=**/constants/**,\
**/services/**,\
**/hooks/**,\
**/coverage/**,\
**/i18n/**,\
**/setupTests.ts,\
**/svgTransform.ts,\
**/ecosystem.config.js,\
**/webpack.config.js,\
**/routes.tsx,\
**/brain-pb-agro-report-frontend.tsx,\
**/root.component.tsx
