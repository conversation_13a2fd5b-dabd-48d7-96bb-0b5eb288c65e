pipeline {
  tools {
    terraform '1.2.9'
  }

  options {
    buildDiscarder(logRotator(numToKeepStr: '3'))
  }

  agent any

  environment {
    DOCKER_REGISTRY = "registry.agribusiness-brain.br.experian.eeca"
    DOCKER_IMAGE = "pb-agro-report-front"

    DOCKER_IMAGE_AGENT_NODE = "dockerhub.agribusiness-brain.br.experian.eeca/node:20-alpine"
    DOCKER_IMAGE_AGENT_NODE_ARGS = "-v /etc/resolv.conf:/etc/resolv.conf -e npm_config_cache=npm-cache"
    DOCKER_IMAGE_AGENT_SONAR_SCANNER = "${DOCKER_REGISTRY}/sonarsource/sonar-scanner-cli:jenkins"
    DOCKER_IMAGE_AGENT_SONAR_SCANNER_ARGS = "-v ${JAVA_CACERTS}:/usr/lib/jvm/java-17-openjdk/jre/lib/security/cacerts"
    NODE_CA_CERTS = "node_cert.pem"
  }

  stages {
    stage('Build Image') {
      when {
        anyOf {
          branch 'main'
          branch 'uat'
          branch 'dev'
        }
      }
      steps {
        script {
          def dateFormat = new java.text.SimpleDateFormat("yyyyMMddHHmm")
          def date = new java.util.Date()

          def env = BRANCH_NAME

          if (BRANCH_NAME == "main") {
            env = "prod"
          }

          DATE = dateFormat.format(date)
          VERSION = "${BRANCH_NAME}-${DATE}"
          sh "cp ${NODE_EXTRA_CA_CERTS} ${NODE_CA_CERTS}"
          sh "docker build -t ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${VERSION} \
                            --build-arg NODE_CA_CERTS=\$NODE_CA_CERTS \
                            --build-arg HTTP_PROXY=\$HTTP_PROXY \
                            --build-arg HTTPS_PROXY=\$HTTPS_PROXY \
                            --build-arg NO_PROXY=\$NO_PROXY \
                            --build-arg NPM_TOKEN=\$NPM_TOKEN \
                            --build-arg ENV=${env} ."
        }
      }
    }

    stage('Run tests & generate coverage') {
      agent {
        docker {
          image "${DOCKER_IMAGE_AGENT_NODE}"
          args "-v /var/lib/jenkins/pem/cert.pem:/var/lib/jenkins/pem/cert.pem -v /etc/resolv.conf:/etc/resolv.conf -e npm_config_cache=npm-cache"
          reuseNode true
        }
      }
      steps {
        script {
          sh "echo NODE_EXTRA_CA_CERTS=${NODE_EXTRA_CA_CERTS}"
          sh "if [ -f ${NODE_EXTRA_CA_CERTS} ]; then echo 'Cert file exists'; else echo 'Cert file missing'; fi"
          sh "cp ${NODE_EXTRA_CA_CERTS} ${NODE_CA_CERTS}"
          sh 'yarn install'
          sh 'yarn coverage'
        }
      }
    }

    stage('SonarQube analysis') {
      when {
        not { tag '*' }
      }

      agent {
        docker {
          image "${DOCKER_IMAGE_AGENT_SONAR_SCANNER}"
          args "${DOCKER_IMAGE_AGENT_SONAR_SCANNER_ARGS}"
          reuseNode true
        }
      }

      steps {
        withSonarQubeEnv(credentialsId: 'a6c31a75-0174-4151-9785-985b3628c7bb', installationName: 'agro-sonarqube') {
          sh 'ls -la coverage'
          sh 'cat coverage/lcov.info | head -n 20'
          sh 'sonar-scanner'
        }
      }
    }

    // stage('SonarQube analysis (Quality Gate)') {
    //   when {
    //     anyOf {
    //       branch 'main'
    //       branch 'uat'
    //       branch 'dev'
    //     }
    //   }
    //   steps {
    //     // https://community.sonarsource.com/t/need-a-sleep-between-withsonarqubeenv-and-waitforqualitygate-or-it-spins-in-in-progress/2265/11
    //     sleep(time: 30, unit: 'SECONDS')
    //     waitForQualityGate abortPipeline: true
    //   }
    // }

    stage('Push Image') {
      when {
        anyOf {
          branch 'main'
          branch 'uat'
          branch 'dev'
        }
      }
      steps {
        withCredentials([usernamePassword(credentialsId: 'DOCKER_REPOSITORY', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
          sh "docker login ${DOCKER_REGISTRY} --username ${USERNAME} --password ${PASSWORD}"
          sh "docker push ${DOCKER_REGISTRY}/${DOCKER_IMAGE}:${VERSION}"
        }
      }
    }

    stage('Deploy on EKS') {
      when {
        anyOf {
          branch 'main'
          branch 'uat'
          branch 'dev'
        }
      }
      steps {
        script {
          if (BRANCH_NAME == 'main') {
            env = 'prd'
					  profile = "eec-agribusiness-${env}"
          } else if (BRANCH_NAME == 'uat') {
            env = 'uat'
            profile = "eec-agribusiness-${env}"
          } else if (BRANCH_NAME == 'dev') {
            env = 'dev'
            profile = "eec-agribusiness-${env}"
          }
          sh "cp ${NODE_EXTRA_CA_CERTS} ${NODE_CA_CERTS}"
          sh(script: "cd kubernetes/terraform ; AWS_REGION=sa-east-1 AWS_PROFILE=${profile} terraform init -backend-config=backend-config/${env}.tfvars", returnStdout: true).trim()
          sh(script: "cd kubernetes/terraform ; AWS_REGION=sa-east-1 AWS_PROFILE=${profile} terraform apply -auto-approve -var-file=variables-${env}.tfvars -var \"app_version=\"${VERSION}", returnStdout: true).trim()
        }
      }
    }
  }
}