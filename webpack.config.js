const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react-ts");
const TsconfigPathsPlugin = require("tsconfig-paths-webpack-plugin");
const path = require("path");
const Dotenv = require("dotenv-webpack");
const webpack = require("webpack");

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "brain",
    projectName: "pb-agro-report-frontend",
    webpackConfigEnv,
    argv,
  });

  return merge(defaultConfig, {
    // modify the webpack config however you'd like to by adding to this object
    resolve: {
      plugins: [new TsconfigPathsPlugin()],
    },
    plugins: [
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/(locale|index\.es\.js)$/,
        contextRegExp: /(moment$|react-tooltip\/dist$)/,
      }),
      new Dotenv({ systemvars: true }),
    ],
    module: {
      rules: [
        {
          test: /\.(woff(2)?|ttf|eot)(\?v=\d+\.\d+\.\d+)?$/,
          use: [
            {
              loader: "file-loader",
              options: {
                name: "[name].[ext]",
                outputPath: "fonts/",
              },
            },
          ],
        },
        {
          test: /\.(csv|pdf|xlsx)$/,
          use: [
            {
              loader: "file-loader",
              options: {
                name: "[name].[ext]",
                outputPath: "files/",
              },
            },
          ],
        },
      ],
    },
    output: {
      path: path.resolve(__dirname, "dist/module/agro-report"),
      clean: true,
    },
  });
};
