{"name": "@brain/pb-agro-report-frontend", "version": "0.0.1a0", "scripts": {"start": "webpack serve --port 8089", "start:local": "webpack serve --port 8089 --env isLocal", "start:standalone": "webpack serve --env standalone", "build": "concurrently yarn:build:*", "build:webpack": "webpack --mode=production", "analyze": "webpack --mode=production --env analyze", "lint": "eslint src --ext js,ts,tsx", "format": "prettier --write .", "check-format": "prettier --check .", "test": "cross-env BABEL_ENV=test jest --config jest.config.json", "test:badges": "yarn coverage && yarn badges", "badges": "jest-coverage-badges --input './coverage/coverage-summary.json' --output './doc/badges'", "watch-tests": "cross-env BABEL_ENV=test jest --watch", "prepare": "husky install", "coverage": "cross-env BABEL_ENV=test jest --coverage", "build:types": "tsc --project tsconfig.build.json"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/eslint-parser": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "^7.23.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.2.0", "@types/cookiejar": "^2.1.5", "@types/geojson": "^7946.0.14", "@types/http-cache-semantics": "^4.0.4", "@types/methods": "^1.1.4", "@types/papaparse": "^5.3.15", "@types/redux-mock-store": "^1.5.0", "@types/styled-components": "^5.1.34", "@types/superagent": "^8.1.9", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "babel-jest": "^27.5.1", "concurrently": "^6.2.1", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-config-ts-react-important-stuff": "^3.0.0", "eslint-plugin-prettier": "^3.4.1", "husky": "^7.0.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^27.5.1", "jest-junit": "^16.0.0", "jest-sonar": "^0.2.16", "prettier": "^2.3.2", "pretty-quick": "^3.1.1", "ts-config-single-spa": "^3.0.0", "typescript": "^4.3.5", "webpack": "^5.89.0", "webpack-cli": "^6.0.1", "webpack-config-single-spa-react": "^4.0.5", "webpack-config-single-spa-react-ts": "^4.0.5", "webpack-dev-server": "^5.2.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@ant-design/icons": "^5.3.0", "@mapbox/geojson-area": "^0.2.2", "@reduxjs/toolkit": "1.9.5", "@testing-library/dom": "^10.4.0", "@tmcw/togeojson": "^5.8.1", "@turf/intersect": "^6.5.0", "@types/jest": "^29.5.14", "@types/react": "^18.2.51", "@types/react-dom": "^18.2.18", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.2", "antd": "^4.20.6", "axios": "^1.8.2", "brainui": "0.4.2", "buffer": "^6.0.3", "dotenv-webpack": "^8.0.1", "exceljs": "4.4.0", "file-loader": "^6.2.0", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "jest-coverage-badges": "^1.1.2", "jest-sonar-reporter": "^2.0.0", "moment": "^2.30.1", "ol": "^8.2.0", "papaparse": "^5.5.2", "react": "^18.2.0", "react-document-title": "^2.0.3", "react-dom": "^18.2.0", "react-i18next": "^14.1.1", "react-icons": "5.4.0", "react-redux": "7.2.6", "react-router-dom": "^6.22.0", "redux-mock-store": "^1.5.5", "shpjs": "^4.0.4", "single-spa": "^5.9.3", "single-spa-react": "^4.3.1", "styled-components": "^5.3.5", "ts-jest": "^29.2.4", "tsconfig-paths-webpack-plugin": "^4.1.0", "use-debounce": "^10.0.0", "use-onclickoutside": "^0.4.1", "util": "^0.12.5"}, "types": "dist/brain-pb-agro-report-frontend.d.ts", "jestSonar": {"sonar56x": true, "reportPath": "reports", "reportFile": "sonar-report.xml", "indent": 4}, "resolutions": {"minimist": "^1.2.6"}}