# Agro Report - Frontend

<div align="center">
  <img src="./doc/badges/badge-statements.svg" />
  <img src="./doc/badges/badge-functions.svg" />
  <img src="./doc/badges/badge-lines.svg" />
  <img src="./doc/badges/badge-branches.svg" />
</div>

Microfrontend do módulo AgroReport para listagem e solicitação de relatórios agro em pdf e xls.

## Requisitos

- Node v16 ou mais recente

## Instalação

```
yarn install
```

## Execução

Executando o projeto como standalone, sem as dependências pb-header e pb-container:

```
yarn start:standalone
```
