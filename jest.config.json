{"preset": "ts-jest", "rootDir": "src", "testEnvironment": "jsdom", "testTimeout": 5000, "transform": {"^.+\\.(j|t)sx?$": "babel-jest", "^.+\\.(svg|csv)$": "<rootDir>/svgTransform.ts"}, "transformIgnorePatterns": ["node_modules/(?!@ol)/"], "setupFilesAfterEnv": ["@testing-library/jest-dom"], "moduleNameMapper": {"\\.(css)$": "identity-obj-proxy", "single-spa-react/parcel": "single-spa-react/lib/cjs/parcel.cjs", "@services": "<rootDir>/services", "@types": "<rootDir>/types", "@utils": "<rootDir>/utils", "@components": "<rootDir>/components", "^@contexts(.*)$": "<rootDir>/contexts$1", "^@constants(.*)$": "<rootDir>/constants/$1", "^@/(.*)$": "<rootDir>/$1"}, "setupFiles": ["<rootDir>/i18n/index.ts", "<rootDir>/setupTests.ts"], "coveragePathIgnorePatterns": ["index.ts", "types/*", "src/assets/*", "\\.styles.ts", "constants/*", "services/*", "hook/*"], "collectCoverage": true, "coverageDirectory": "../coverage", "coverageReporters": ["json", "lcov", "text", "clover"]}