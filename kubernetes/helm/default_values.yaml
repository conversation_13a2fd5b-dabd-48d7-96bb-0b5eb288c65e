deployment:
  repository: dockerhub.agribusiness-brain.br.experian.eeca/pb-agro-report-front
  pullPolicy: IfNotPresent
  strategy:
    type: RollingUpdate
  production:
    replicaCount: 1

app:
  labels:
    gearr: "25166"

imagePullSecrets:
  - name: regcred-dockerhub

nameOverride: "pb-agro-report-front"

fullnameOverride: "pb-agro-report-front"

serviceAccount:
  create: true
  annotations: { }
  name: "pb-agro-report-front"

podAnnotations: { }

podSecurityContext: { }

securityContext: { }

containerPort: 3333

service:
  name: front
  type: ClusterIP
  port: 80
  containerPort: 3333


livenessProbe:
  httpGet:
    path: /module/agro-report/brain-pb-agro-report-frontend.js
    port: 3333
  initialDelaySeconds: 30
  timeoutSeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /module/agro-report/brain-pb-agro-report-frontend.js
    port: 3333
  initialDelaySeconds: 30
  timeoutSeconds: 30
  periodSeconds: 10
  tcpSocket:

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 512Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 60

nodeSelector: { }

tolerations: [ ]

affinity: { }

virtualService:
  enabled: true
  match:
    - uri:
        prefix: /
    - uri:
        exact: /
